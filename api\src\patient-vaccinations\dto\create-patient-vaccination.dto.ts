import {
	IsNotEmpty,
	IsString,
	IsOptional,
	IsBoolean,
	Length,
	IsUUID,
	IsObject,
	IsDate
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreatePatientVaccinationDto {
	@IsNotEmpty()
	@IsUUID()
	patientId!: string;

	@IsOptional()
	@IsUUID()
	appointmentId?: string;

	@IsNotEmpty()
	@IsBoolean()
	systemGenerated!: boolean;

	@IsOptional()
	@IsString()
	@Length(0, 25)
	doctorName?: string;

	@IsNotEmpty()
	@IsString()
	@Length(1, 50)
	vaccineName!: string;

	@IsNotEmpty()
	@Type(() => Date)
	@IsDate()
	vaccinationDate!: Date;

	@IsOptional()
	@IsString()
	reportUrl?: string;

	@IsOptional()
	@IsObject()
	urlMeta?: object;
}

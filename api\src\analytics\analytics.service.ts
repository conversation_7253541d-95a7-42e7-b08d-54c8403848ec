import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InvoiceEntity } from '../invoice/entities/invoice.entity';
import { PaymentDetailsEntity } from '../payment-details/entities/payment-details.entity';
import { OwnerBrand } from '../owners/entities/owner-brand.entity';
import { ClinicEntity } from '../clinics/entities/clinic.entity';
import {
	DownloadAnalyticsReportDto,
	AnalyticsType,
	AnalyticsReportType,
	GetRevenueChartDataDto,
	RevenueChartDataPoint,
	CollectedPaymentsChartDataPoint,
	GetAppointmentsChartDataDto,
	AppointmentsChartResponse,
	AppointmentAnalyticsType,
	GetDoctorSummaryDto,
	DoctorSummaryResponseDto,
	GetSummaryDto,
	SummaryResponseDto
} from './dto/analytics.dto';
import * as XLSX from 'xlsx';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';

interface BillingData extends ExcelDataRow {
	Name: string;
	'Total Quantity Sold': number;
	'Total Revenue': number;
	'Total Quantity Returned': number;
	'Total Refund Value': number;
	'Total Bad Debt': number; // Add bad debt tracking
}

interface PatientBillingData extends ExcelDataRow {
	'Patient Name': string;
	'Owner First Name': string;
	'Owner Last Name': string;
	'Owner Phone Number': string;
	'Owner Email': string;
	'Transaction Date': string;
	'Transaction Type': string;
	'Total Price': number;
	'Total Discount': number;
	'Total Tax': number;
	'Total Value': number;
}

interface ClinicInfo {
	name: string;
	addressLine1: string;
	addressLine2: string;
	city: string;
	state: string;
	country: string;
	pincode: string;
}

interface OwnerSummaryData extends ExcelDataRow {
	'Owner First Name': string;
	'Owner Last Name': string;
	'Number of Pets': number;
	'Owner Email': string;
	'Owner Phone': string;
	'Total Current Balance': number;
}

interface BadDebtData extends ExcelDataRow {
	'Patient Name': string;
	'Owner Name': string;
	'Owner Email': string;
	'Owner Phone': string;
	'Invoice Date': string;
	'Invoice ID': string;
	'Total Invoice Amount': number;
	'Amount Paid': number;
	'Write-off Amount': number;
	'Write-off Date': string;
	'Write-off Reason': string;
	'Write-off By': string;
}

// Raw SQL query result interfaces for type safety
interface BadDebtDataRow {
	'Patient Name': string | null;
	'Owner Name': string;
	'Owner Email': string;
	'Owner Phone': string;
	'Invoice Date': string;
	'Invoice ID': string;
	'Total Invoice Amount': string | number;
	'Amount Paid': string | number;
	'Write-off Amount': string | number;
	'Write-off Date': string;
	'Write-off Reason': string;
	'Write-off By': string;
}

interface CollectedPaymentsLedgerRow {
	'Patient Name': string | null;
	'Owner Name': string;
	'Owner Email': string;
	'Owner Phone': string;
	Date: string;
	'Invoice ID': string;
	Amount: string | number;
	'Invoice Amount Cleared': string | number | null;
	'Pending Balance': string | number | null;
	'Balance Pending Since': string;
}

interface CreditNotesDataRow {
	'Patient Name': string | null;
	'Owner Name': string;
	'Owner Email': string;
	'Owner Phone': string;
	Date: string;
	'Credit Note ID': string;
	Amount: string | number;
}

interface RevenueChartDataRow {
	date: string;
	products: string | number;
	services: string | number;
	diagnostics: string | number;
	medications: string | number;
	vaccinations: string | number;
}

interface CollectedPaymentsChartDataRow {
	date: string;
	cash: string | number;
	card: string | number;
	wallet: string | number;
	cheque: string | number;
	banktransfer: string | number;
}

interface AppointmentChartDataRow {
	date: string;
	total: string | number;
	missed: string | number;
}

interface BusiestDaysRow {
	day: string;
	count: string | number;
	weeks_counted: string | number;
	total: string | number;
}

interface BusiestHoursRow {
	hour: string | number;
	count: string | number;
	days_count: string | number;
	total: string | number;
}

interface AverageDurationRow {
	date: string;
	checkinduration: string | number;
	receivingcareduration: string | number;
	checkoutduration: string | number;
	totalduration: string | number;
	validappointments: string | number;
}

interface DoctorSummaryRow {
	doctor_name: string;
	num_appointments: string | number;
	total_revenue: string | number;
	revenue_per_appointment: string | number;
	avg_appointment_duration_minutes: string | number;
}

interface OwnerSummaryDataRow {
	'Owner First Name': string;
	'Owner Last Name': string;
	'Number of Pets': string | number;
	'Owner Email': string;
	'Owner Phone': string;
	'Total Current Balance': string | number;
}

interface PatientBillingDataRow {
	'Patient Name': string | null;
	'Owner First Name': string;
	'Owner Last Name': string;
	'Owner Phone Number': string;
	'Owner Email': string;
	'Transaction Date': string;
	'Transaction Type': string;
	'Total Price': string | number;
	'Total Discount': string | number;
	'Total Tax': string | number;
	'Total Value': string | number;
}

interface PaymentModeRow {
	payment_mode: string;
	total: string | number;
}

interface PaymentTypeRow {
	payment_type: string;
	[key: string]: any; // For additional properties that might be present
}

interface ClinicInfoRow {
	name: string;
	addressLine1: string;
	addressLine2: string;
	city: string;
	state: string;
	country: string;
	pincode: string;
}

// Excel data processing interfaces
interface ExcelDataRow {
	[key: string]: string | number | boolean | null | undefined;
}

interface ColumnProperties {
	numericColumns: Set<number>;
	columnWidths: { wch: number }[];
}

interface ExcelHeaderStyle {
	font: { bold: boolean; color: { rgb: string } };
	fill: { fgColor: { rgb: string } };
	alignment: { horizontal: string };
	border: {
		top: { style: string; color: { rgb: string } };
		bottom: { style: string; color: { rgb: string } };
		left: { style: string; color: { rgb: string } };
		right: { style: string; color: { rgb: string } };
	};
}

interface ExcelBorderStyle {
	top: { style: string; color: { rgb: string } };
	bottom: { style: string; color: { rgb: string } };
	left: { style: string; color: { rgb: string } };
	right: { style: string; color: { rgb: string } };
}

interface ExcelNumericCellStyle {
	border: ExcelBorderStyle;
	numFmt: string;
	alignment: { horizontal: string };
}

interface ExcelTextCellStyle {
	border: ExcelBorderStyle;
	alignment: { horizontal: string };
}

interface ExcelCellStyles {
	header: ExcelHeaderStyle;
	numeric: ExcelNumericCellStyle;
	text: ExcelTextCellStyle;
}

interface ExcelCell {
	v: string | number;
	t?: string;
	s?: ExcelHeaderStyle | ExcelNumericCellStyle | ExcelTextCellStyle;
}

// Payment statistics interfaces
type PaymentModeKey = 'wallet' | 'card' | 'cash' | 'cheque' | 'bank_transfer';

interface PaymentSummaryStats {
	collectedByMode: Record<PaymentModeKey, number>;
	collectedCountByMode: Record<PaymentModeKey, number>;
	returnedByMode: Record<PaymentModeKey, number>;
	returnedCountByMode: Record<PaymentModeKey, number>;
	totalCollected: number;
	totalCollectedCount: number;
	totalReturned: number;
	totalReturnedCount: number;
}

interface PaymentModeInfo {
	key: PaymentModeKey;
	label: string;
}

// Excel data array types
type ExcelCellValue = string | number;
type ExcelDataArray = ExcelCellValue[][];

// Return types for ledger data functions
interface CollectedPaymentsLedgerData extends ExcelDataRow {
	'Patient Name': string;
	'Owner Name': string;
	'Owner Email': string;
	'Owner Phone': string;
	Date: string;
	'Invoice ID': string;
	Amount: number;
	'Invoice Amount Cleared': string | number;
	'Pending Balance': string | number;
	'Balance Pending Since': string;
}

interface ReturnedPaymentsLedgerData extends ExcelDataRow {
	'Patient Name': string;
	'Owner Name': string;
	'Owner Email': string;
	'Owner Phone': string;
	Date: string;
	'Credit Note ID': string;
	Amount: number;
}

// Generic data fetcher function type
type DataFetcherFunction = (
	clinicId: string,
	startDate: Date,
	endDate: Date,
	timezone: string
) => Promise<ExcelDataRow[]>;

// All payments by mode data interface
interface AllPaymentsModeData extends ExcelDataRow {
	'Owner Name': string;
	'Payment Mode': string;
	'Receipt Number': string;
	'Payment Date': string;
	Amount: number;
	Action: string;
	Pet: string;
	'Invoice Cleared': string;
}

@Injectable()
export class AnalyticsService {
	private readonly logger = new Logger(AnalyticsService.name);

	constructor(
		@InjectRepository(InvoiceEntity)
		private readonly invoiceRepository: Repository<InvoiceEntity>,
		@InjectRepository(PaymentDetailsEntity)
		private readonly paymentDetailsRepository: Repository<PaymentDetailsEntity>,
		@InjectRepository(OwnerBrand)
		private readonly ownerBrandRepository: Repository<OwnerBrand>,
		@InjectRepository(AppointmentEntity)
		private readonly appointmentRepository: Repository<AppointmentEntity>,
		@InjectRepository(ClinicEntity)
		private readonly clinicRepository: Repository<ClinicEntity>
	) {}

	/**
	 * Get the timezone for a specific clinic
	 */
	private async getClinicTimezone(clinicId: string): Promise<string> {
		const clinic = await this.clinicRepository.findOne({
			where: { id: clinicId },
			select: ['timezone']
		});
		return clinic?.timezone || 'Asia/Kolkata'; // Fallback to default
	}

	/**
	 * Helper function to determine date grouping strategy based on date range
	 */
	private _getDateGroupingStrategy(startDate: Date, endDate: Date) {
		const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
		const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

		let dateFormat: string;
		let interval: string;
		let truncPart: string;

		if (diffDays <= 35) {
			dateFormat = 'YYYY-MM-DD';
			interval = '1 day';
			truncPart = 'day';
		} else if (diffDays <= 210) {
			dateFormat = 'YYYY-"W"IW';
			interval = '1 week';
			truncPart = 'week';
		} else if (diffDays <= 730) {
			dateFormat = 'YYYY-MM';
			interval = '1 month';
			truncPart = 'month';
		} else {
			dateFormat = 'YYYY';
			interval = '1 year';
			truncPart = 'year';
		}

		return { dateFormat, interval, truncPart };
	}

	private async getClinicInfo(clinicId: string): Promise<ClinicInfo> {
		const query = `
			SELECT 
				name,
				address_line_1 AS "addressLine1",
				address_line_2 AS "addressLine2",
				city,
				state,
				country,
				address_pincode AS pincode
			FROM public.clinics
			WHERE id = $1
		`;

		const result: ClinicInfoRow[] = await this.invoiceRepository.query(
			query,
			[clinicId]
		);
		return result[0];
	}

	private async createMetadataSheet(
		clinicId: string,
		startDate: Date,
		endDate: Date,
		reportType: string
	): Promise<XLSX.WorkSheet> {
		// Get clinic information
		const clinicInfo = await this.getClinicInfo(clinicId);

		// Define styles
		const headerStyle = {
			font: { bold: true, sz: 14, color: { rgb: '000000' } },
			fill: { fgColor: { rgb: 'CCCCCC' } },
			border: {
				top: { style: 'medium', color: { rgb: '000000' } },
				bottom: { style: 'medium', color: { rgb: '000000' } },
				left: { style: 'medium', color: { rgb: '000000' } },
				right: { style: 'medium', color: { rgb: '000000' } }
			},
			alignment: { horizontal: 'center' }
		};

		const labelStyle = {
			font: { bold: true, color: { rgb: '000000' } },
			border: {
				top: { style: 'thin', color: { rgb: '000000' } },
				bottom: { style: 'thin', color: { rgb: '000000' } },
				left: { style: 'thin', color: { rgb: '000000' } },
				right: { style: 'thin', color: { rgb: '000000' } }
			},
			alignment: { horizontal: 'left' }
		};

		const valueStyle = {
			border: {
				top: { style: 'thin', color: { rgb: '000000' } },
				bottom: { style: 'thin', color: { rgb: '000000' } },
				left: { style: 'thin', color: { rgb: '000000' } },
				right: { style: 'thin', color: { rgb: '000000' } }
			},
			alignment: { horizontal: 'left' }
		};

		// Create metadata rows with proper styling
		const metadata = [
			[{ v: 'REPORT DETAILS', s: headerStyle }],
			[
				{ v: 'Report Type:', s: labelStyle },
				{ v: reportType, s: valueStyle }
			],
			[''],
			[{ v: 'CLINIC INFORMATION', s: headerStyle }],
			[
				{ v: 'Clinic Name:', s: labelStyle },
				{ v: clinicInfo.name, s: valueStyle }
			],
			[
				{ v: 'Address Line 1:', s: labelStyle },
				{ v: clinicInfo.addressLine1, s: valueStyle }
			],
			[
				{ v: 'Address Line 2:', s: labelStyle },
				{ v: clinicInfo.addressLine2, s: valueStyle }
			],
			[
				{ v: 'City:', s: labelStyle },
				{ v: clinicInfo.city, s: valueStyle }
			],
			[
				{ v: 'State:', s: labelStyle },
				{ v: clinicInfo.state, s: valueStyle }
			],
			[
				{ v: 'Country:', s: labelStyle },
				{ v: clinicInfo.country, s: valueStyle }
			],
			[
				{ v: 'Pincode:', s: labelStyle },
				{ v: clinicInfo.pincode, s: valueStyle }
			],
			[''],
			[{ v: 'REPORT PARAMETERS', s: headerStyle }],
			[
				{ v: 'Date Range:', s: labelStyle },
				{
					v: `${startDate.getDate()} ${['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'][startDate.getMonth()]} ${startDate.getFullYear()} - ${endDate.getDate()} ${['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'][endDate.getMonth()]} ${endDate.getFullYear()}`,
					s: valueStyle
				}
			],
			[
				{ v: 'Generated on:', s: labelStyle },
				{
					v: (() => {
						const now = new Date();
						const day = now.getDate();
						const month = [
							'January',
							'February',
							'March',
							'April',
							'May',
							'June',
							'July',
							'August',
							'September',
							'October',
							'November',
							'December'
						][now.getMonth()];
						const year = now.getFullYear();
						const hours = now.getHours();
						const minutes = now
							.getMinutes()
							.toString()
							.padStart(2, '0');
						const ampm = hours >= 12 ? 'pm' : 'am';
						const formattedHours = (hours % 12 || 12)
							.toString()
							.padStart(2, '0');
						return `${day} ${month} ${year} at ${formattedHours}:${minutes} ${ampm}`;
					})(),
					s: valueStyle
				}
			]
		];

		// Create worksheet
		const ws = XLSX.utils.aoa_to_sheet(metadata);

		// Set column widths
		ws['!cols'] = [
			{ wch: 20 }, // Width for first column (labels)
			{ wch: 60 } // Width for second column (values)
		];

		// Merge cells for headers
		ws['!merges'] = [
			{ s: { r: 0, c: 0 }, e: { r: 0, c: 1 } }, // REPORT DETAILS
			{ s: { r: 3, c: 0 }, e: { r: 3, c: 1 } }, // CLINIC INFORMATION
			{ s: { r: 12, c: 0 }, e: { r: 12, c: 1 } } // REPORT PARAMETERS
		];

		return ws;
	}

	private cleanDataForExcel(data: ExcelDataRow[]): ExcelDataRow[] {
		return data.map(row => {
			const cleanRow = { ...row };
			Object.keys(cleanRow).forEach(key => {
				cleanRow[key] ??= '';
			});
			return cleanRow;
		});
	}

	private determineColumnProperties(
		cleanedData: ExcelDataRow[]
	): ColumnProperties {
		const headers = Object.keys(cleanedData[0]);
		const numericColumns = new Set<number>();
		const columnWidths = headers.map(() => ({ wch: 15 }));

		headers.forEach((header, idx) => {
			const isNumericColumn = cleanedData.some(row => {
				const value = row[header];
				return (
					value !== '' &&
					(typeof value === 'number' ||
						(typeof value === 'string' &&
							!isNaN(Number(value)) &&
							value.trim() !== ''))
				);
			});

			if (isNumericColumn) {
				numericColumns.add(idx);
			}
		});

		return { numericColumns, columnWidths };
	}

	private styleWorksheet(
		ws: XLSX.WorkSheet,
		cleanedData: ExcelDataRow[],
		numericColumns: Set<number>
	): void {
		const styles = this._createCellStyles();
		const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');
		const headers = Object.keys(cleanedData[0]);

		this._applyHeaderStyles(ws, headers, styles.header);
		this._applyDataCellStyles(ws, range, numericColumns, styles);
	}

	private _createCellStyles(): ExcelCellStyles {
		const borderStyle = {
			top: { style: 'thin', color: { rgb: '000000' } },
			bottom: { style: 'thin', color: { rgb: '000000' } },
			left: { style: 'thin', color: { rgb: '000000' } },
			right: { style: 'thin', color: { rgb: '000000' } }
		};

		return {
			header: {
				font: { bold: true, color: { rgb: '000000' } },
				fill: { fgColor: { rgb: 'CCCCCC' } },
				border: borderStyle,
				alignment: { horizontal: 'center' }
			},
			numeric: {
				border: borderStyle,
				numFmt: '#,##0.00',
				alignment: { horizontal: 'right' }
			},
			text: {
				border: borderStyle,
				alignment: { horizontal: 'left' }
			}
		};
	}

	private _applyHeaderStyles(
		ws: XLSX.WorkSheet,
		headers: string[],
		headerStyle: ExcelHeaderStyle
	) {
		headers.forEach((header, idx) => {
			const cellRef = XLSX.utils.encode_cell({ r: 0, c: idx });
			if (!ws[cellRef]) ws[cellRef] = { v: header };
			ws[cellRef].s = headerStyle;
		});
	}

	private _applyDataCellStyles(
		ws: XLSX.WorkSheet,
		range: XLSX.Range,
		numericColumns: Set<number>,
		styles: ExcelCellStyles
	) {
		for (let R = 1; R <= range.e.r; ++R) {
			for (let C = 0; C <= range.e.c; ++C) {
				const cellRef = XLSX.utils.encode_cell({ r: R, c: C });
				if (!ws[cellRef]) continue;

				this._applyCellStyle(ws, cellRef, C, numericColumns, styles);
			}
		}
	}

	private _applyCellStyle(
		ws: XLSX.WorkSheet,
		cellRef: string,
		columnIndex: number,
		numericColumns: Set<number>,
		styles: ExcelCellStyles
	) {
		if (numericColumns.has(columnIndex)) {
			this._applyNumericCellStyle(ws, cellRef, styles.numeric);
		} else {
			ws[cellRef].s = styles.text;
		}
	}

	private _applyNumericCellStyle(
		ws: XLSX.WorkSheet,
		cellRef: string,
		numericStyle: ExcelNumericCellStyle
	) {
		const cell = ws[cellRef];

		if (cell.v === '') {
			cell.t = 's';
			cell.s = numericStyle;
			return;
		}

		this._convertToNumericIfPossible(cell);
		cell.t = 'n';
		cell.s = numericStyle;
	}

	private _convertToNumericIfPossible(cell: ExcelCell) {
		if (typeof cell.v !== 'string') return;

		const numValue = Number(cell.v);
		if (!isNaN(numValue)) {
			cell.v = numValue;
		}
	}

	private async createDataSheet(
		data: ExcelDataRow[]
	): Promise<XLSX.WorkSheet> {
		if (!data || data.length === 0) {
			return XLSX.utils.json_to_sheet([{ message: 'No data available' }]);
		}

		const cleanedData = this.cleanDataForExcel(data);
		const ws = XLSX.utils.json_to_sheet(cleanedData);

		const { numericColumns, columnWidths } =
			this.determineColumnProperties(cleanedData);
		ws['!cols'] = columnWidths;

		this.styleWorksheet(ws, cleanedData, numericColumns);

		return ws;
	}

	async generateReport(dto: DownloadAnalyticsReportDto): Promise<Buffer> {
		try {
			// Adjust dates to include full day
			const startDateTime = new Date(dto.startDate);
			startDateTime.setHours(0, 0, 0, 0);

			const endDateTime = new Date(dto.endDate);
			endDateTime.setHours(23, 59, 59, 999);

			const workbook = XLSX.utils.book_new();

			const timezone = await this.getClinicTimezone(dto.clinicId);

			if (
				dto.type === AnalyticsType.REVENUE &&
				dto.reportType === AnalyticsReportType.BY_BILLING
			) {
				await this._generateRevenueByBillingReport(
					workbook,
					dto.clinicId,
					startDateTime,
					endDateTime,
					timezone
				);
			} else if (
				dto.type === AnalyticsType.REVENUE &&
				dto.reportType === AnalyticsReportType.BY_PATIENT
			) {
				await this._generateRevenueByPatientReport(
					workbook,
					dto.clinicId,
					startDateTime,
					endDateTime,
					timezone
				);
			} else if (
				dto.type === AnalyticsType.COLLECTED_PAYMENTS &&
				dto.reportType === AnalyticsReportType.BY_PATIENT
			) {
				await this._generateCollectedPaymentsReport(
					workbook,
					dto.clinicId,
					startDateTime,
					endDateTime,
					timezone
				);
			}

			// Add collected payments ledger data
			await this._createCollectedPaymentsLedgerSheet(
				workbook,
				dto.clinicId,
				startDateTime,
				endDateTime,
				timezone
			);

			// Add returned payments ledger data
			await this._createReturnedPaymentsLedgerSheet(
				workbook,
				dto.clinicId,
				startDateTime,
				endDateTime,
				timezone
			);

			// If no data sheets were added (only metadata sheet exists)
			if (workbook.SheetNames.length === 1) {
				const noDataWs = await this.createDataSheet([
					{ message: 'No data found' }
				]);
				XLSX.utils.book_append_sheet(workbook, noDataWs, 'No Data');
			}

			const buffer = XLSX.write(workbook, {
				type: 'buffer',
				bookType: 'xlsx',
				compression: true
			});

			return buffer;
		} catch (error: unknown) {
			const errorMessage =
				error instanceof Error ? error.message : 'Unknown error';
			const errorStack = error instanceof Error ? error.stack : undefined;
			this.logger.error(`Error in generateReport: ${errorMessage}`);
			if (errorStack) {
				this.logger.error(errorStack);
			}
			throw error;
		}
	}

	private async getProductsBillingData(
		clinicId: string,
		startDate: Date,
		endDate: Date
	): Promise<BillingData[]> {
		try {
			const query = `
				WITH invoice_details AS (
					SELECT 
						items.value->>'name' AS product_name,
						CAST(items.value->>'quantity' AS INTEGER) AS quantity,
						CAST(items.value->>'actualPrice' AS NUMERIC) AS actual_price,
						CAST(items.value->>'finalPrice' AS NUMERIC) AS final_price,
						invoices.created_at,
						invoices.id as invoice_id,
						invoices.invoice_type,
						invoices.status,
						invoices.metadata,
						invoices.invoice_amount::numeric as total_invoice_amount
					FROM public.invoices
					CROSS JOIN LATERAL json_array_elements(
						CASE 
							WHEN invoices.details IS NULL THEN '[]'::json
							ELSE invoices.details::json
						END
					) AS items
					INNER JOIN public.patients ON invoices.patient_id = patients.id
					WHERE patients.clinic_id = $1
						AND invoices.created_at BETWEEN $2 AND $3
						AND invoices.details IS NOT NULL
						AND items.value->>'itemType' = 'Product'
						AND (items.value->>'isAddedToCart')::boolean = true
						-- Exclude canceled invoices completely
						AND NOT (invoices.invoice_type = 'Invoice' AND invoices.status = 'cancelled')
				),
				writeoff_calculations AS (
					SELECT 
						product_name,
						invoice_type,
						quantity,
						actual_price,
						-- Calculate the proportion of writeoff for this item
						CASE 
							WHEN invoice_type = 'Invoice' AND metadata->>'writeoff' IS NOT NULL THEN
								(
									(actual_price * quantity) / 
									NULLIF(total_invoice_amount, 0)
								) * (metadata->'writeoff'->>'amount')::numeric
							ELSE 0
						END AS item_writeoff_amount
					FROM invoice_details
				)
				SELECT
					SUBSTRING(product_name, 1, 256) AS "Name",
					SUM(CASE 
						WHEN invoice_type = 'Invoice' THEN quantity
						ELSE 0 
					END) AS "Total Quantity Sold",
					SUM(CASE 
						WHEN invoice_type = 'Invoice' THEN actual_price * quantity 
						ELSE 0 
					END) AS "Total Revenue",
					SUM(CASE 
						WHEN invoice_type = 'Refund' THEN quantity 
						ELSE 0 
					END) AS "Total Quantity Returned",
					SUM(CASE 
						WHEN invoice_type = 'Refund' THEN actual_price * quantity 
						ELSE 0 
					END) AS "Total Refund Value",
					SUM(item_writeoff_amount) AS "Total Bad Debt"
				FROM writeoff_calculations
				GROUP BY product_name
				ORDER BY "Total Revenue" DESC
				LIMIT 1000;
			`;

			const result = await this.invoiceRepository.query(query, [
				clinicId,
				startDate,
				endDate
			]);

			return result;
		} catch (error: unknown) {
			const errorMessage =
				error instanceof Error ? error.message : 'Unknown error';
			const errorStack = error instanceof Error ? error.stack : undefined;
			this.logger.error(
				`Error in getProductsBillingData: ${errorMessage}`
			);
			if (errorStack) {
				this.logger.error(errorStack);
			}
			throw error;
		}
	}

	private async getServicesBillingData(
		clinicId: string,
		startDate: Date,
		endDate: Date
	): Promise<BillingData[]> {
		try {
			const query = `
				WITH invoice_details AS (
					SELECT 
						items.value->>'name' AS service_name,
						CAST(items.value->>'quantity' AS INTEGER) AS quantity,
						CAST(items.value->>'actualPrice' AS NUMERIC) AS actual_price,
						CAST(items.value->>'finalPrice' AS NUMERIC) AS final_price,
						invoices.created_at,
						invoices.id as invoice_id,
						invoices.invoice_type,
						invoices.status,
						invoices.metadata,
						invoices.invoice_amount::numeric as total_invoice_amount
					FROM public.invoices
					CROSS JOIN LATERAL json_array_elements(
						CASE 
							WHEN invoices.details IS NULL THEN '[]'::json
							ELSE invoices.details::json
						END
					) AS items
					INNER JOIN public.patients ON invoices.patient_id = patients.id
					WHERE patients.clinic_id = $1
						AND invoices.created_at BETWEEN $2 AND $3
						AND invoices.details IS NOT NULL
						AND items.value->>'itemType' = 'Service'
						AND (items.value->>'isAddedToCart')::boolean = true
						-- Exclude canceled invoices completely
						AND NOT (invoices.invoice_type = 'Invoice' AND invoices.status = 'cancelled')
				),
				writeoff_calculations AS (
					SELECT 
						service_name,
						invoice_type,
						quantity,
						actual_price,
						-- Calculate the proportion of writeoff for this item
						CASE 
							WHEN invoice_type = 'Invoice' AND metadata->>'writeoff' IS NOT NULL THEN
								(
									(actual_price * quantity) / 
									NULLIF(total_invoice_amount, 0)
								) * (metadata->'writeoff'->>'amount')::numeric
							ELSE 0
						END AS item_writeoff_amount
					FROM invoice_details
				)
				SELECT
					SUBSTRING(service_name, 1, 256) AS "Name",
					SUM(CASE 
						WHEN invoice_type = 'Invoice' THEN quantity 
						ELSE 0 
					END) AS "Total Quantity Sold",
					SUM(CASE 
						WHEN invoice_type = 'Invoice' THEN actual_price * quantity 
						ELSE 0 
					END) AS "Total Revenue",
					SUM(CASE 
						WHEN invoice_type = 'Refund' THEN quantity 
						ELSE 0 
					END) AS "Total Quantity Returned",
					SUM(CASE 
						WHEN invoice_type = 'Refund' THEN actual_price * quantity 
						ELSE 0 
					END) AS "Total Refund Value",
					SUM(item_writeoff_amount) AS "Total Bad Debt"
				FROM writeoff_calculations
				GROUP BY service_name
				ORDER BY "Total Revenue" DESC
				LIMIT 1000;
			`;

			const result = await this.invoiceRepository.query(query, [
				clinicId,
				startDate,
				endDate
			]);

			return result;
		} catch (error: unknown) {
			const errorMessage =
				error instanceof Error ? error.message : 'Unknown error';
			const errorStack = error instanceof Error ? error.stack : undefined;
			this.logger.error(
				`Error in getServicesBillingData: ${errorMessage}`
			);
			if (errorStack) {
				this.logger.error(errorStack);
			}
			throw error;
		}
	}

	private async getVaccinationsBillingData(
		clinicId: string,
		startDate: Date,
		endDate: Date
	): Promise<BillingData[]> {
		try {
			const query = `
				WITH invoice_details AS (
					SELECT 
						items.value->>'name' AS product_name,
						CAST(items.value->>'quantity' AS INTEGER) AS quantity,
						CAST(items.value->>'actualPrice' AS NUMERIC) AS actual_price,
						CAST(items.value->>'finalPrice' AS NUMERIC) AS final_price,
						invoices.created_at,
						invoices.id as invoice_id,
						invoices.invoice_type,
						invoices.status,
						invoices.metadata,
						invoices.invoice_amount::numeric as total_invoice_amount
					FROM public.invoices
					CROSS JOIN LATERAL json_array_elements(
						CASE 
							WHEN invoices.details IS NULL THEN '[]'::json
							ELSE invoices.details::json
						END
					) AS items
					INNER JOIN public.patients ON invoices.patient_id = patients.id
					WHERE patients.clinic_id = $1
						AND invoices.created_at BETWEEN $2 AND $3
						AND invoices.details IS NOT NULL
						AND items.value->>'itemType' = 'Vaccination'
						AND (items.value->>'isAddedToCart')::boolean = true
						-- Exclude canceled invoices completely
						AND NOT (invoices.invoice_type = 'Invoice' AND invoices.status = 'cancelled')
				),
				writeoff_calculations AS (
					SELECT 
						product_name,
						invoice_type,
						quantity,
						actual_price,
						-- Calculate the proportion of writeoff for this item
						CASE 
							WHEN invoice_type = 'Invoice' AND metadata->>'writeoff' IS NOT NULL THEN
								(
									(actual_price * quantity) / 
									NULLIF(total_invoice_amount, 0)
								) * (metadata->'writeoff'->>'amount')::numeric
							ELSE 0
						END AS item_writeoff_amount
					FROM invoice_details
				)
				SELECT
					SUBSTRING(product_name, 1, 256) AS "Name",
					SUM(CASE 
						WHEN invoice_type = 'Invoice' THEN quantity 
						ELSE 0 
					END) AS "Total Quantity Sold",
					SUM(CASE 
						WHEN invoice_type = 'Invoice' THEN actual_price * quantity 
						ELSE 0 
					END) AS "Total Revenue",
					SUM(CASE 
						WHEN invoice_type = 'Refund' THEN quantity 
						ELSE 0 
					END) AS "Total Quantity Returned",
					SUM(CASE 
						WHEN invoice_type = 'Refund' THEN actual_price * quantity 
						ELSE 0 
					END) AS "Total Refund Value",
					SUM(item_writeoff_amount) AS "Total Bad Debt"
				FROM writeoff_calculations
				GROUP BY product_name
				ORDER BY "Total Revenue" DESC
				LIMIT 1000;
			`;

			const result = await this.invoiceRepository.query(query, [
				clinicId,
				startDate,
				endDate
			]);

			return result;
		} catch (error: unknown) {
			const errorMessage =
				error instanceof Error ? error.message : 'Unknown error';
			const errorStack = error instanceof Error ? error.stack : undefined;
			this.logger.error(
				`Error in getVaccinationsBillingData: ${errorMessage}`
			);
			if (errorStack) {
				this.logger.error(errorStack);
			}
			throw error;
		}
	}

	private async getMedicationsBillingData(
		clinicId: string,
		startDate: Date,
		endDate: Date
	): Promise<BillingData[]> {
		try {
			const query = `
				WITH invoice_details AS (
					SELECT 
						items.value->>'name' AS name,
						CAST(items.value->>'quantity' AS INTEGER) AS quantity,
						CAST(items.value->>'actualPrice' AS NUMERIC) AS actual_price,
						CAST(items.value->>'finalPrice' AS NUMERIC) AS final_price,
						invoices.created_at,
						invoices.id as invoice_id,
						invoices.invoice_type,
						invoices.status,
						invoices.metadata,
						invoices.invoice_amount::numeric as total_invoice_amount
					FROM public.invoices
					CROSS JOIN LATERAL json_array_elements(
						CASE 
							WHEN invoices.details IS NULL THEN '[]'::json
							ELSE invoices.details::json
						END
					) AS items
					INNER JOIN public.patients ON invoices.patient_id = patients.id
					WHERE patients.clinic_id = $1
						AND invoices.created_at BETWEEN $2 AND $3
						AND invoices.details IS NOT NULL
						AND items.value->>'itemType' = 'Medication'
						AND (items.value->>'isAddedToCart')::boolean = true
						-- Exclude canceled invoices completely
						AND NOT (invoices.invoice_type = 'Invoice' AND invoices.status = 'cancelled')
				),
				writeoff_calculations AS (
					SELECT 
						name,
						invoice_type,
						quantity,
						actual_price,
						-- Calculate the proportion of writeoff for this item
						CASE 
							WHEN invoice_type = 'Invoice' AND metadata->>'writeoff' IS NOT NULL THEN
								(
									(actual_price * quantity) / 
									NULLIF(total_invoice_amount, 0)
								) * (metadata->'writeoff'->>'amount')::numeric
							ELSE 0
						END AS item_writeoff_amount
					FROM invoice_details
				)
				SELECT
					SUBSTRING(name, 1, 256) AS "Name",
					SUM(CASE 
						WHEN invoice_type = 'Invoice' THEN quantity 
						ELSE 0 
					END) AS "Total Quantity Sold",
					SUM(CASE 
						WHEN invoice_type = 'Invoice' THEN actual_price * quantity 
						ELSE 0 
					END) AS "Total Revenue",
					SUM(CASE 
						WHEN invoice_type = 'Refund' THEN quantity 
						ELSE 0 
					END) AS "Total Quantity Returned",
					SUM(CASE 
						WHEN invoice_type = 'Refund' THEN actual_price * quantity 
						ELSE 0 
					END) AS "Total Refund Value",
					SUM(item_writeoff_amount) AS "Total Bad Debt"
				FROM writeoff_calculations
				GROUP BY name
				ORDER BY "Total Revenue" DESC
				LIMIT 1000;
			`;

			const result = await this.invoiceRepository.query(query, [
				clinicId,
				startDate,
				endDate
			]);

			return result;
		} catch (error: unknown) {
			const errorMessage =
				error instanceof Error ? error.message : 'Unknown error';
			const errorStack = error instanceof Error ? error.stack : undefined;
			this.logger.error(
				`Error in getMedicationsBillingData: ${errorMessage}`
			);
			if (errorStack) {
				this.logger.error(errorStack);
			}
			throw error;
		}
	}

	private async getLabReportsBillingData(
		clinicId: string,
		startDate: Date,
		endDate: Date
	): Promise<BillingData[]> {
		try {
			const query = `
				WITH invoice_details AS (
					SELECT 
						items.value->>'name' AS name,
						CAST(items.value->>'quantity' AS INTEGER) AS quantity,
						CAST(items.value->>'actualPrice' AS NUMERIC) AS actual_price,
						CAST(items.value->>'finalPrice' AS NUMERIC) AS final_price,
						invoices.created_at,
						invoices.id as invoice_id,
						invoices.invoice_type,
						invoices.status,
						invoices.metadata,
						invoices.invoice_amount::numeric as total_invoice_amount
					FROM public.invoices
					CROSS JOIN LATERAL json_array_elements(
						CASE 
							WHEN invoices.details IS NULL THEN '[]'::json
							ELSE invoices.details::json
						END
					) AS items
					INNER JOIN public.patients ON invoices.patient_id = patients.id
					WHERE patients.clinic_id = $1
						AND invoices.created_at BETWEEN $2 AND $3
						AND invoices.details IS NOT NULL
						AND items.value->>'itemType' = 'Labreport'
						AND (items.value->>'isAddedToCart')::boolean = true
						-- Exclude canceled invoices completely
						AND NOT (invoices.invoice_type = 'Invoice' AND invoices.status = 'cancelled')
				),
				writeoff_calculations AS (
					SELECT 
						name,
						invoice_type,
						quantity,
						actual_price,
						-- Calculate the proportion of writeoff for this item
						CASE 
							WHEN invoice_type = 'Invoice' AND metadata->>'writeoff' IS NOT NULL THEN
								(
									(actual_price * quantity) / 
									NULLIF(total_invoice_amount, 0)
								) * (metadata->'writeoff'->>'amount')::numeric
							ELSE 0
						END AS item_writeoff_amount
					FROM invoice_details
				)
				SELECT
					SUBSTRING(name, 1, 256) AS "Name",
					SUM(CASE 
						WHEN invoice_type = 'Invoice' THEN quantity 
						ELSE 0 
					END) AS "Total Quantity Sold",
					SUM(CASE 
						WHEN invoice_type = 'Invoice' THEN actual_price * quantity 
						ELSE 0 
					END) AS "Total Revenue",
					SUM(CASE 
						WHEN invoice_type = 'Refund' THEN quantity 
						ELSE 0 
					END) AS "Total Quantity Returned",
					SUM(CASE 
						WHEN invoice_type = 'Refund' THEN actual_price * quantity 
						ELSE 0 
					END) AS "Total Refund Value",
					SUM(item_writeoff_amount) AS "Total Bad Debt"
				FROM writeoff_calculations
				GROUP BY name
				ORDER BY "Total Revenue" DESC
				LIMIT 1000;
			`;

			const result = await this.invoiceRepository.query(query, [
				clinicId,
				startDate,
				endDate
			]);

			return result;
		} catch (error: unknown) {
			const errorMessage =
				error instanceof Error ? error.message : 'Unknown error';
			const errorStack = error instanceof Error ? error.stack : undefined;
			this.logger.error(
				`Error in getLabReportsBillingData: ${errorMessage}`
			);
			if (errorStack) {
				this.logger.error(errorStack);
			}
			throw error;
		}
	}

	private async getPatientsBillingData(
		clinicId: string,
		startDate: Date,
		endDate: Date
	): Promise<PatientBillingData[]> {
		const query = `
			WITH filtered_invoices AS (
				SELECT 
					invoices.id,
					invoices.patient_id,
					invoices.owner_id,
					invoices.created_at,
					invoices.invoice_type,
					invoices.status,
					SUM(
						CASE 
							WHEN items.value->>'isAddedToCart' = 'true' THEN 
								CAST(items.value->>'quantity' AS INTEGER) * CAST(items.value->>'actualPrice' AS NUMERIC)
							ELSE 
								0
						END
					) as total_price,
					SUM(
						CASE 
							WHEN items.value->>'isAddedToCart' = 'true' THEN 
								CAST(items.value->>'quantity' AS INTEGER) * (CAST(items.value->>'actualPrice' AS NUMERIC) - CAST(items.value->>'finalPrice' AS NUMERIC) / CAST(items.value->>'quantity' AS NUMERIC))
							ELSE 
								0
						END
					) as total_discount,
					SUM(
						CASE 
							WHEN items.value->>'isAddedToCart' = 'true' THEN 
								CAST(items.value->>'taxedAmount' AS NUMERIC)
							ELSE 
								0
						END
					) as total_tax,
					SUM(
						CASE 
							WHEN items.value->>'isAddedToCart' = 'true' THEN 
								CAST(items.value->>'finalPrice' AS NUMERIC)
							ELSE 
								0
						END
					) as amount_payable
				FROM public.invoices
				CROSS JOIN LATERAL json_array_elements(
					CASE 
						WHEN invoices.details IS NULL THEN '[]'::json
						ELSE invoices.details::json
					END
				) AS items
				WHERE invoices.clinic_id = $1
					AND invoices.created_at BETWEEN $2 AND $3
					-- Exclude canceled invoices completely
					AND NOT (invoices.invoice_type = 'Invoice' AND invoices.status = 'cancelled')
				GROUP BY 
					invoices.id,
					invoices.patient_id,
					invoices.owner_id,
					invoices.created_at,
					invoices.invoice_type,
					invoices.status
			)
			SELECT 
				patients.patient_name AS "Patient Name",
				ob.first_name AS "Owner First Name",
					ob.last_name AS "Owner Last Name",
				go.phone_number AS "Owner Phone Number",
				ob.email AS "Owner Email",
				TO_CHAR(fi.created_at, 'DD/MM/YY') AS "Transaction Date",
				fi.invoice_type AS "Transaction Type",
				CASE 
					WHEN fi.invoice_type = 'Refund' THEN -fi.total_price::numeric
					ELSE fi.total_price::numeric
				END AS "Total Price",
				CASE 
					WHEN fi.invoice_type = 'Refund' THEN -fi.total_discount::numeric
					ELSE fi.total_discount::numeric
				END AS "Total Discount",
				CASE 
					WHEN fi.invoice_type = 'Refund' THEN -fi.total_tax::numeric
					ELSE fi.total_tax::numeric
				END AS "Total Tax",
				CASE 
					WHEN fi.invoice_type = 'Refund' THEN -fi.amount_payable::numeric
					ELSE fi.amount_payable::numeric
				END AS "Total Value"
			FROM filtered_invoices fi
			INNER JOIN public.patients
				ON fi.patient_id = patients.id
			INNER JOIN public.patient_owners
				ON patients.id = patient_owners.patient_id
			INNER JOIN public.owner_brands ob
				ON patient_owners.owner_id = ob.id
			INNER JOIN public.global_owners go
				ON ob.global_owner_id = go.id
			WHERE fi.amount_payable > 0
			ORDER BY 
				fi.created_at DESC,
				patients.patient_name ASC;
		`;

		const results: PatientBillingDataRow[] =
			await this.invoiceRepository.query(query, [
				clinicId,
				startDate,
				endDate
			]);

		return results.map((row: PatientBillingDataRow) => ({
			'Patient Name': row['Patient Name'] || '',
			'Owner First Name': row['Owner First Name'],
			'Owner Last Name': row['Owner Last Name'],
			'Owner Phone Number': row['Owner Phone Number'],
			'Owner Email': row['Owner Email'],
			'Transaction Date': row['Transaction Date'],
			'Transaction Type': row['Transaction Type'],
			'Total Price': Number(row['Total Price'] || 0),
			'Total Discount': Number(row['Total Discount'] || 0),
			'Total Tax': Number(row['Total Tax'] || 0),
			'Total Value': Number(row['Total Value'] || 0)
		}));
	}

	private async getBadDebtData(
		clinicId: string,
		startDate: Date,
		endDate: Date,
		timezone: string
	): Promise<BadDebtData[]> {
		try {
			const query = `
				SELECT 
					COALESCE(p.patient_name, '') AS "Patient Name",
					CONCAT(ob.first_name, ' ', ob.last_name) AS "Owner Name",
					ob.email AS "Owner Email",
					go.phone_number AS "Owner Phone",
					TO_CHAR(i.created_at AT TIME ZONE 'UTC' AT TIME ZONE $4, 'DD/MM/YY') AS "Invoice Date",
					i.reference_alpha_id AS "Invoice ID",
					i.invoice_amount::numeric AS "Total Invoice Amount",
					COALESCE(i.paid_amount::numeric, 0) AS "Amount Paid",
					CASE 
						WHEN i.metadata->>'writeoff' IS NOT NULL THEN 
							(i.metadata->'writeoff'->>'amount')::numeric
						ELSE 0
					END AS "Write-off Amount",
					CASE 
						WHEN i.metadata->>'writeoff' IS NOT NULL THEN 
							TO_CHAR((i.metadata->'writeoff'->>'date')::timestamp AT TIME ZONE 'UTC' AT TIME ZONE $4, 'DD/MM/YY')
						ELSE ''
					END AS "Write-off Date",
					CASE 
						WHEN i.metadata->>'writeoff' IS NOT NULL THEN 
							COALESCE(i.metadata->'writeoff'->>'reason', '')
						ELSE ''
					END AS "Write-off Reason",
					CASE 
						WHEN i.metadata->>'writeoff' IS NOT NULL THEN 
							COALESCE(i.metadata->'writeoff'->>'by', '')
						ELSE ''
					END AS "Write-off By"
				FROM public.invoices i
				LEFT JOIN public.patients p ON i.patient_id = p.id
				JOIN public.owner_brands ob ON i.owner_id = ob.id
				JOIN public.global_owners go ON ob.global_owner_id = go.id
				WHERE i.invoice_type = 'Invoice'
				AND i.clinic_id = $1
				AND i.deleted_at IS NULL
				AND (
					i.status = 'written_off'
					OR (
						i.metadata->>'writeoff' IS NOT NULL 
						AND (i.metadata->'writeoff'->>'amount')::numeric > 0
					)
				)
				AND (
					-- Filter by write-off date if writeoff metadata exists
					CASE 
						WHEN i.metadata->>'writeoff' IS NOT NULL THEN 
							(i.metadata->'writeoff'->>'date')::timestamp BETWEEN $2 AND $3
						ELSE 
							-- Fallback to invoice creation date for old data without writeoff date
							i.created_at BETWEEN $2 AND $3
					END
				)
				ORDER BY COALESCE((i.metadata->'writeoff'->>'date')::timestamp, i.created_at) DESC;
			`;

			const results: BadDebtDataRow[] =
				await this.invoiceRepository.query(query, [
					clinicId,
					startDate,
					endDate,
					timezone
				]);

			return results.map((row: BadDebtDataRow) =>
				this.mapBadDebtRow(row)
			);
		} catch (error: unknown) {
			const errorMessage =
				error instanceof Error ? error.message : 'Unknown error';
			const errorStack = error instanceof Error ? error.stack : undefined;
			this.logger.error(`Error in getBadDebtData: ${errorMessage}`);
			if (errorStack) {
				this.logger.error(errorStack);
			}
			throw error;
		}
	}

	private mapBadDebtRow(row: BadDebtDataRow): BadDebtData {
		return {
			'Patient Name': row['Patient Name'] || '',
			'Owner Name': row['Owner Name'] || '',
			'Owner Email': row['Owner Email'] || '',
			'Owner Phone': row['Owner Phone'] || '',
			'Invoice Date': row['Invoice Date'] || '',
			'Invoice ID': row['Invoice ID'] || '',
			'Total Invoice Amount': Number(row['Total Invoice Amount'] || 0),
			'Amount Paid': Number(row['Amount Paid'] || 0),
			'Write-off Amount': Number(row['Write-off Amount'] || 0),
			'Write-off Date': row['Write-off Date'] || '',
			'Write-off Reason': row['Write-off Reason'] || '',
			'Write-off By': row['Write-off By'] || ''
		};
	}

	private async getCollectedPaymentsLedgerData(
		clinicId: string,
		startDate: Date,
		endDate: Date,
		timezone: string
	): Promise<CollectedPaymentsLedgerData[]> {
		const query = `
			SELECT 
				COALESCE(p.patient_name, '') AS "Patient Name",
				CONCAT(ob.first_name, ' ', ob.last_name) AS "Owner Name",
				ob.email AS "Owner Email",
				go.phone_number AS "Owner Phone",
				TO_CHAR(i.created_at AT TIME ZONE 'UTC' AT TIME ZONE $4, 'DD/MM/YY HH24:MI:SS') AS "Date",
				i.reference_alpha_id AS "Invoice ID",
				i.invoice_amount::numeric AS "Amount",
				CASE 
					WHEN i.status = 'unknown' THEN NULL
					ELSE i.paid_amount::numeric
				END AS "Invoice Amount Cleared",
				CASE 
					WHEN i.status = 'unknown' THEN NULL
					ELSE i.balance_due::numeric
				END AS "Pending Balance",
				CASE 
					WHEN i.balance_due::numeric > 0 THEN 
						TO_CHAR(i.updated_at AT TIME ZONE 'UTC' AT TIME ZONE $4, 'DD/MM/YY')
					ELSE ''
				END AS "Balance Pending Since"
			FROM public.invoices i
			LEFT JOIN public.patients p ON i.patient_id = p.id
			JOIN public.owner_brands ob ON i.owner_id = ob.id
			JOIN public.global_owners go ON ob.global_owner_id = go.id
			WHERE i.invoice_type = 'Invoice'
			AND i.created_at BETWEEN $2 AND $3
			AND i.clinic_id = $1
			AND i.deleted_at IS NULL
			AND i.status != 'cancelled'  -- Exclude canceled invoices
			AND (i.metadata->>'source' IS NULL OR i.metadata->>'source' != 'old_balance_migration')
			ORDER BY i.created_at DESC;
		`;

		const results: CollectedPaymentsLedgerRow[] =
			await this.invoiceRepository.query(query, [
				clinicId,
				startDate,
				endDate,
				timezone
			]);

		return results.map((row: CollectedPaymentsLedgerRow) => ({
			'Patient Name': row['Patient Name'] || '',
			'Owner Name': row['Owner Name'] || '',
			'Owner Email': row['Owner Email'] || '',
			'Owner Phone': row['Owner Phone'] || '',
			Date: row['Date'] || '',
			'Invoice ID': row['Invoice ID'] || '',
			Amount: Number(row['Amount'] || 0),
			'Invoice Amount Cleared':
				row['Invoice Amount Cleared'] === null
					? '-'
					: Number(row['Invoice Amount Cleared'] || 0),
			'Pending Balance':
				row['Pending Balance'] === null
					? '-'
					: Number(row['Pending Balance'] || 0),
			'Balance Pending Since': row['Balance Pending Since'] || ''
		}));
	}

	private async getReturnedPaymentsLedgerData(
		clinicId: string,
		startDate: Date,
		endDate: Date,
		timezone: string
	): Promise<ReturnedPaymentsLedgerData[]> {
		const query = `
			SELECT 
				COALESCE(p.patient_name, '') AS "Patient Name",
				CONCAT(ob.first_name, ' ', ob.last_name) AS "Owner Name",
				ob.email AS "Owner Email",
				go.phone_number AS "Owner Phone",
				TO_CHAR(i.created_at AT TIME ZONE 'UTC' AT TIME ZONE $4, 'DD/MM/YY HH24:MI:SS') AS "Date",
				i.reference_alpha_id AS "Credit Note ID",
				i.invoice_amount::numeric AS "Amount"
			FROM public.invoices i
			LEFT JOIN public.patients p ON i.patient_id = p.id
			JOIN public.owner_brands ob ON i.owner_id = ob.id
			JOIN public.global_owners go ON ob.global_owner_id = go.id
			WHERE i.invoice_type = 'Refund'
			AND i.created_at BETWEEN $2 AND $3
			AND i.clinic_id = $1
			AND i.deleted_at IS NULL
			ORDER BY i.created_at DESC;
		`;

		const results: CreditNotesDataRow[] =
			await this.invoiceRepository.query(query, [
				clinicId,
				startDate,
				endDate,
				timezone
			]);

		return results.map((row: CreditNotesDataRow) => ({
			'Patient Name': row['Patient Name'] || '',
			'Owner Name': row['Owner Name'] || '',
			'Owner Email': row['Owner Email'] || '',
			'Owner Phone': row['Owner Phone'] || '',
			Date: row['Date'] || '',
			'Credit Note ID': row['Credit Note ID'] || '',
			Amount: Number(row['Amount'] || 0)
		}));
	}

	private _buildSectionData(
		title: string,
		paymentStats: PaymentSummaryStats,
		paymentModes: PaymentModeInfo[],
		type: 'collected' | 'returned' | 'net'
	) {
		const sectionData: (string | number)[][] = [[title, '', '']];
		let totalAmount = 0;
		let totalTransactions = 0;

		for (const mode of paymentModes) {
			let amount: number;
			if (type === 'collected') {
				amount = paymentStats.collectedByMode[mode.key];
			} else if (type === 'returned') {
				amount = paymentStats.returnedByMode[mode.key];
			} else {
				// type === 'net'
				amount =
					paymentStats.collectedByMode[mode.key] -
					paymentStats.returnedByMode[mode.key];
			}

			let transactions: number;
			if (type === 'net') {
				transactions =
					paymentStats.collectedCountByMode[mode.key] +
					paymentStats.returnedCountByMode[mode.key];
			} else if (type === 'collected') {
				transactions = paymentStats.collectedCountByMode[mode.key];
			} else {
				// type === 'returned'
				transactions = paymentStats.returnedCountByMode[mode.key];
			}

			sectionData.push([
				`Total amount ${type} through ${mode.label}`,
				amount,
				`${transactions} Transactions`
			]);

			totalAmount += amount;
			totalTransactions += transactions;
		}

		sectionData.push([
			`Total Payments ${type}`,
			totalAmount,
			`${totalTransactions} Transactions`
		]);
		sectionData.push(['', '', '']);

		return sectionData;
	}

	private _applyCellStyling(ws: XLSX.WorkSheet, data: ExcelDataArray) {
		for (let i = 0; i < data.length; i++) {
			this._applyHeaderStyling(ws, data[i], i);
			this._applyNumberFormatting(ws, data[i], i);
			this._applyTotalRowStyling(ws, data[i], i);
		}
	}

	private _applyHeaderStyling(
		ws: XLSX.WorkSheet,
		row: ExcelCellValue[],
		rowIndex: number
	) {
		const isHeaderRow = row[0] && !row[1] && !row[2];
		if (!isHeaderRow) return;

		const cellRef = XLSX.utils.encode_cell({ r: rowIndex, c: 0 });
		this._ensureCellStyle(ws, cellRef);
		ws[cellRef].s = {
			font: { bold: true, sz: 12 },
			fill: { fgColor: { rgb: 'DDDDDD' } }
		};
	}

	private _applyNumberFormatting(
		ws: XLSX.WorkSheet,
		row: ExcelCellValue[],
		rowIndex: number
	) {
		const hasNumericValue = typeof row[1] === 'number';
		if (!hasNumericValue) return;

		const cellRef = XLSX.utils.encode_cell({ r: rowIndex, c: 1 });
		this._ensureCellStyle(ws, cellRef);
		ws[cellRef].s = {
			numFmt: '#,##0.00',
			alignment: { horizontal: 'right' }
		};
	}

	private _applyTotalRowStyling(
		ws: XLSX.WorkSheet,
		row: ExcelCellValue[],
		rowIndex: number
	) {
		if (!row[0]) return;

		const cellText = String(row[0]);
		const isTotalRow = this._isTotalRow(cellText);
		if (!isTotalRow) return;

		this._makeTotalRowBold(ws, rowIndex);
	}

	private _isTotalRow(cellText: string): boolean {
		return (
			cellText.startsWith('Total Payments') ||
			cellText.startsWith('Total (net)')
		);
	}

	private _makeTotalRowBold(ws: XLSX.WorkSheet, rowIndex: number) {
		for (let j = 0; j < 3; j++) {
			const cellRef = XLSX.utils.encode_cell({ r: rowIndex, c: j });
			if (!ws[cellRef]) continue;

			this._ensureCellStyle(ws, cellRef);
			ws[cellRef].s.font = { bold: true };
		}
	}

	private _ensureCellStyle(ws: XLSX.WorkSheet, cellRef: string) {
		ws[cellRef].s ??= {};
	}

	private async createPaymentsSummarySheet(
		clinicId: string,
		startDate: Date,
		endDate: Date
	): Promise<XLSX.WorkSheet> {
		const clinicInfo = await this.getClinicInfo(clinicId);
		const paymentStats = await this.getPaymentSummaryStats(
			clinicId,
			startDate,
			endDate
		);

		const paymentModes = [
			{ key: 'wallet' as const, label: 'wallet' },
			{ key: 'card' as const, label: 'card' },
			{ key: 'cash' as const, label: 'cash' },
			{ key: 'cheque' as const, label: 'cheque' },
			{ key: 'bank_transfer' as const, label: 'bank transfer' }
		];

		const data = [
			['REPORT DETAILS', '', ''],
			['Report Type:', 'Collected Payments Report', ''],
			['', '', ''],
			['CLINIC INFORMATION', '', ''],
			['Clinic Name:', clinicInfo.name, ''],
			['Address Line 1:', clinicInfo.addressLine1, ''],
			['Address Line 2:', clinicInfo.addressLine2, ''],
			['City:', clinicInfo.city, ''],
			['State:', clinicInfo.state, ''],
			['Country:', clinicInfo.country, ''],
			['Pincode:', clinicInfo.pincode, ''],
			['', '', ''],
			['REPORT PARAMETERS', '', ''],
			[
				'Date Range:',
				`${startDate.toLocaleDateString()} to ${endDate.toLocaleDateString()}`,
				''
			],
			['Generated At:', new Date().toLocaleString(), ''],
			['', '', ''],
			...this._buildSectionData(
				'PAYMENTS COLLECTED',
				paymentStats,
				paymentModes,
				'collected'
			),
			...this._buildSectionData(
				'PAYMENTS RETURNED',
				paymentStats,
				paymentModes,
				'returned'
			),
			...this._buildSectionData(
				'NET AMOUNT',
				paymentStats,
				paymentModes,
				'net'
			)
		];

		const ws = XLSX.utils.aoa_to_sheet(data);
		ws['!cols'] = [{ wch: 40 }, { wch: 20 }, { wch: 20 }];
		this._applyCellStyling(ws, data);

		return ws;
	}

	private async getPaymentSummaryStats(
		clinicId: string,
		startDate: Date,
		endDate: Date
	) {
		// SQL query to get payment statistics grouped by mode
		const query = `
			SELECT
				payment_type,
				type,
				 SUM(amount::numeric) as total_amount,
				COUNT(*) as transaction_count
			FROM public.payment_details pd
			WHERE pd.clinic_id = $1
			AND pd.created_at BETWEEN $2 AND $3
			AND pd.deleted_at IS NULL
			GROUP BY payment_type, type
		`;

		const results = await this.paymentDetailsRepository.query(query, [
			clinicId,
			startDate,
			endDate
		]);

		// Define payment mode types
		type PaymentMode =
			| 'wallet'
			| 'card'
			| 'cash'
			| 'cheque'
			| 'bank_transfer';

		// Initialize objects to store payment stats with proper typing
		const collectedByMode: Record<PaymentMode, number> = {
			wallet: 0,
			card: 0,
			cash: 0,
			cheque: 0,
			bank_transfer: 0
		};

		const collectedCountByMode: Record<PaymentMode, number> = {
			wallet: 0,
			card: 0,
			cash: 0,
			cheque: 0,
			bank_transfer: 0
		};

		const returnedByMode: Record<PaymentMode, number> = {
			wallet: 0,
			card: 0,
			cash: 0,
			cheque: 0,
			bank_transfer: 0
		};

		const returnedCountByMode: Record<PaymentMode, number> = {
			wallet: 0,
			card: 0,
			cash: 0,
			cheque: 0,
			bank_transfer: 0
		};

		// Process the results and populate the stats objects
		for (const row of results) {
			const paymentType = (row.payment_type || '').toLowerCase();
			const type = row.type;
			const amount = Number(row.total_amount || 0);
			const count = Number(row.transaction_count || 0);

			// Map payment types to our standard keys
			let mappedType: PaymentMode | undefined;

			if (paymentType === 'wallet') {
				mappedType = 'wallet';
			} else if (paymentType === 'card') {
				mappedType = 'card';
			} else if (paymentType === 'cash') {
				mappedType = 'cash';
			} else if (paymentType === 'cheque') {
				mappedType = 'cheque';
			} else if (paymentType === 'bank transfer') {
				mappedType = 'bank_transfer';
			}

			// Only proceed if we have a valid payment mode
			if (mappedType) {
				// Check if it's a collected payment or a returned payment
				if (
					[
						'Invoice',
						'Reconcile Invoice',
						'Bulk Reconcile Invoice',
						'Collect'
					].includes(type)
				) {
					// Collected payment
					collectedByMode[mappedType] += amount;
					collectedCountByMode[mappedType] += count;
				} else if (['Credit Note', 'Return'].includes(type)) {
					// Returned payment
					returnedByMode[mappedType] += amount;
					returnedCountByMode[mappedType] += count;
				}
			}
		}

		// Calculate totals
		const totalCollected = Object.values(collectedByMode).reduce(
			(sum, val) => sum + val,
			0
		);
		const totalCollectedCount = Object.values(collectedCountByMode).reduce(
			(sum, val) => sum + val,
			0
		);
		const totalReturned = Object.values(returnedByMode).reduce(
			(sum, val) => sum + val,
			0
		);
		const totalReturnedCount = Object.values(returnedCountByMode).reduce(
			(sum, val) => sum + val,
			0
		);

		return {
			collectedByMode,
			collectedCountByMode,
			returnedByMode,
			returnedCountByMode,
			totalCollected,
			totalCollectedCount,
			totalReturned,
			totalReturnedCount
		};
	}

	private async getOwnerSummaryData(
		clinicId: string,
		startDate: Date,
		endDate: Date
	): Promise<OwnerSummaryData[]> {
		this.logger.log('Getting owner summary data...');
		const query = `
			WITH FilteredOwners AS (
				SELECT 
					ob.id as owner_id,
					ob.first_name,
					ob.last_name,
					ob.email,
					ob.owner_balance,
					go.phone_number
				FROM public.owner_brands ob
				JOIN public.global_owners go ON ob.global_owner_id = go.id
				WHERE EXISTS (
					SELECT 1
					FROM public.patient_owners po
					JOIN public.patients p ON po.patient_id = p.id
					WHERE po.owner_id = ob.id
					AND p.clinic_id = $1
				)
				AND ob.updated_at BETWEEN $2 AND $3
			),
			OwnerPets AS (
				SELECT 
					fo.owner_id,
					COUNT(DISTINCT po.patient_id) as pet_count
				FROM FilteredOwners fo
				LEFT JOIN public.patient_owners po ON fo.owner_id = po.owner_id
				GROUP BY fo.owner_id
			)
			SELECT 
				fo.first_name AS "Owner First Name",
				fo.last_name AS "Owner Last Name",
				COALESCE(op.pet_count, 0) AS "Number of Pets",
				fo.email AS "Owner Email",
				fo.phone_number AS "Owner Phone",
				fo.owner_balance AS "Total Current Balance"
			FROM FilteredOwners fo
			LEFT JOIN OwnerPets op ON fo.owner_id = op.owner_id
			ORDER BY fo.first_name ASC, fo.last_name ASC;
		`;

		const results: OwnerSummaryDataRow[] =
			await this.ownerBrandRepository.query(query, [
				clinicId,
				startDate,
				endDate
			]);

		return results.map((row: OwnerSummaryDataRow) => ({
			'Owner First Name': row['Owner First Name'],
			'Owner Last Name': row['Owner Last Name'],
			'Number of Pets': Number(row['Number of Pets'] || 0),
			'Owner Email': row['Owner Email'],
			'Owner Phone': row['Owner Phone'],
			'Total Current Balance': Number(row['Total Current Balance'] || 0)
		}));
	}

	async getRevenueChartData(
		dto: GetRevenueChartDataDto
	): Promise<RevenueChartDataPoint[]> {
		try {
			// Convert dates to start and end of day
			const startDateTime = new Date(dto.startDate);
			startDateTime.setHours(0, 0, 0, 0);

			const endDateTime = new Date(dto.endDate);
			endDateTime.setHours(23, 59, 59, 999);

			// Get date grouping strategy
			const { dateFormat, interval, truncPart } =
				this._getDateGroupingStrategy(startDateTime, endDateTime);

			// Determine grouping based on truncPart
			let grouping: string;
			if (truncPart === 'day') {
				grouping = 'DATE(invoices.created_at)';
			} else if (truncPart === 'week') {
				grouping = "DATE_TRUNC('week', invoices.created_at)";
			} else if (truncPart === 'month') {
				grouping = "DATE_TRUNC('month', invoices.created_at)";
			} else {
				grouping = "DATE_TRUNC('year', invoices.created_at)";
			}

			const query = `
				WITH dates AS (
					SELECT generate_series(
						DATE_TRUNC($4, $2::timestamp),
						DATE_TRUNC($4, $3::timestamp),
						$5::interval
					)::date AS date
				),
				products_revenue AS (
					SELECT 
						${grouping} as date,
						SUM(
							CASE 
								WHEN invoices.invoice_type = 'Invoice' AND invoices.status NOT IN ('cancelled') AND invoices.details IS NOT NULL THEN (
									SELECT COALESCE(SUM(CAST(items.value->>'quantity' AS integer) * CAST(items.value->>'actualPrice' AS numeric)), 0)
									FROM json_array_elements(invoices.details::json) AS items
									WHERE items.value->>'itemType' = 'Product' AND (items.value->>'isAddedToCart')::boolean = true
								)
								WHEN invoices.invoice_type = 'Refund' AND invoices.details IS NOT NULL THEN (
									SELECT COALESCE(-SUM(CAST(items.value->>'quantity' AS integer) * CAST(items.value->>'actualPrice' AS numeric)), 0)
									FROM json_array_elements(invoices.details::json) AS items
									WHERE items.value->>'itemType' = 'Product' AND (items.value->>'isAddedToCart')::boolean = true
								)
								ELSE 0
							END
						) as revenue
					FROM public.invoices
						INNER JOIN public.patients ON invoices.patient_id = patients.id
					WHERE patients.clinic_id = $1
						AND invoices.created_at BETWEEN $2 AND $3
						AND invoices.details IS NOT NULL
					GROUP BY ${grouping}
				),
				services_revenue AS (
					SELECT 
						${grouping} as date,
						SUM(
							CASE 
								WHEN invoices.invoice_type = 'Invoice' AND invoices.status NOT IN ('cancelled') AND invoices.details IS NOT NULL THEN (
									SELECT COALESCE(SUM(CAST(items.value->>'quantity' AS integer) * CAST(items.value->>'actualPrice' AS numeric)), 0)
									FROM json_array_elements(invoices.details::json) AS items
									WHERE items.value->>'itemType' = 'Service' AND (items.value->>'isAddedToCart')::boolean = true
								)
								WHEN invoices.invoice_type = 'Refund' AND invoices.details IS NOT NULL THEN (
									SELECT COALESCE(-SUM(CAST(items.value->>'quantity' AS integer) * CAST(items.value->>'actualPrice' AS numeric)), 0)
									FROM json_array_elements(invoices.details::json) AS items
									WHERE items.value->>'itemType' = 'Service' AND (items.value->>'isAddedToCart')::boolean = true
								)
								ELSE 0
							END
						) as revenue
					FROM public.invoices
						INNER JOIN public.patients ON invoices.patient_id = patients.id
					WHERE patients.clinic_id = $1
						AND invoices.created_at BETWEEN $2 AND $3
						AND invoices.details IS NOT NULL
					GROUP BY ${grouping}
				),
				diagnostics_revenue AS (
					SELECT 
						${grouping} as date,
						SUM(
							CASE 
								WHEN invoices.invoice_type = 'Invoice' AND invoices.status NOT IN ('cancelled') AND invoices.details IS NOT NULL THEN (
									SELECT COALESCE(SUM(CAST(items.value->>'quantity' AS integer) * CAST(items.value->>'actualPrice' AS numeric)), 0)
									FROM json_array_elements(invoices.details::json) AS items
									WHERE items.value->>'itemType' = 'Labreport' AND (items.value->>'isAddedToCart')::boolean = true
								)
								WHEN invoices.invoice_type = 'Refund' AND invoices.details IS NOT NULL THEN (
									SELECT COALESCE(-SUM(CAST(items.value->>'quantity' AS integer) * CAST(items.value->>'actualPrice' AS numeric)), 0)
									FROM json_array_elements(invoices.details::json) AS items
									WHERE items.value->>'itemType' = 'Labreport' AND (items.value->>'isAddedToCart')::boolean = true
								)
								ELSE 0
							END
						) as revenue
					FROM public.invoices
						INNER JOIN public.patients ON invoices.patient_id = patients.id
					WHERE patients.clinic_id = $1
						AND invoices.created_at BETWEEN $2 AND $3
						AND invoices.details IS NOT NULL
					GROUP BY ${grouping}
				),
				medications_revenue AS (
					SELECT 
						${grouping} as date,
						SUM(
							CASE 
								WHEN invoices.invoice_type = 'Invoice' AND invoices.status NOT IN ('cancelled') AND invoices.details IS NOT NULL THEN (
									SELECT COALESCE(SUM(CAST(items.value->>'quantity' AS integer) * CAST(items.value->>'actualPrice' AS numeric)), 0)
									FROM json_array_elements(invoices.details::json) AS items
									WHERE items.value->>'itemType' = 'Medication' AND (items.value->>'isAddedToCart')::boolean = true
								)
								WHEN invoices.invoice_type = 'Refund' AND invoices.details IS NOT NULL THEN (
									SELECT COALESCE(-SUM(CAST(items.value->>'quantity' AS integer) * CAST(items.value->>'actualPrice' AS numeric)), 0)
									FROM json_array_elements(invoices.details::json) AS items
									WHERE items.value->>'itemType' = 'Medication' AND (items.value->>'isAddedToCart')::boolean = true
								)
								ELSE 0
							END
						) as revenue
					FROM public.invoices
						INNER JOIN public.patients ON invoices.patient_id = patients.id
					WHERE patients.clinic_id = $1
						AND invoices.created_at BETWEEN $2 AND $3
						AND invoices.details IS NOT NULL
					GROUP BY ${grouping}
				),
				vaccinations_revenue AS (
					SELECT 
						${grouping} as date,
						SUM(
							CASE 
								WHEN invoices.invoice_type = 'Invoice' AND invoices.status NOT IN ('cancelled') AND invoices.details IS NOT NULL THEN (
									SELECT COALESCE(SUM(CAST(items.value->>'quantity' AS integer) * CAST(items.value->>'actualPrice' AS numeric)), 0)
									FROM json_array_elements(invoices.details::json) AS items
									WHERE items.value->>'itemType' = 'Vaccination' AND (items.value->>'isAddedToCart')::boolean = true
								)
								WHEN invoices.invoice_type = 'Refund' AND invoices.details IS NOT NULL THEN (
									SELECT COALESCE(-SUM(CAST(items.value->>'quantity' AS integer) * CAST(items.value->>'actualPrice' AS numeric)), 0)
									FROM json_array_elements(invoices.details::json) AS items
									WHERE items.value->>'itemType' = 'Vaccination' AND (items.value->>'isAddedToCart')::boolean = true
								)
								ELSE 0
							END
						) as revenue
					FROM public.invoices
						INNER JOIN public.patients ON invoices.patient_id = patients.id
					WHERE patients.clinic_id = $1
						AND invoices.created_at BETWEEN $2 AND $3
						AND invoices.details IS NOT NULL
					GROUP BY ${grouping}
				)
				SELECT 
					TO_CHAR(d.date, $6) as date,
					COALESCE(pr.revenue, 0) as products,
					COALESCE(sr.revenue, 0) as services,
					COALESCE(dr.revenue, 0) as diagnostics,
					COALESCE(mr.revenue, 0) as medications,
					COALESCE(vr.revenue, 0) as vaccinations
				FROM dates d
				LEFT JOIN products_revenue pr ON DATE_TRUNC($4, d.date) = pr.date
				LEFT JOIN services_revenue sr ON DATE_TRUNC($4, d.date) = sr.date
				LEFT JOIN diagnostics_revenue dr ON DATE_TRUNC($4, d.date) = dr.date
				LEFT JOIN medications_revenue mr ON DATE_TRUNC($4, d.date) = mr.date
				LEFT JOIN vaccinations_revenue vr ON DATE_TRUNC($4, d.date) = vr.date
				ORDER BY d.date;
			`;

			const queryParams = [
				dto.clinicId,
				startDateTime,
				endDateTime,
				truncPart,
				interval,
				dateFormat
			];

			const result: RevenueChartDataRow[] =
				await this.invoiceRepository.query(query, queryParams);

			const mappedResult = result.map((row: RevenueChartDataRow) => ({
				date: row.date,
				products: Number(row.products || 0),
				services: Number(row.services || 0),
				diagnostics: Number(row.diagnostics || 0),
				medications: Number(row.medications || 0),
				vaccinations: Number(row.vaccinations || 0)
			}));

			return mappedResult;
		} catch (error: unknown) {
			const errorMessage =
				error instanceof Error ? error.message : 'Unknown error';
			const errorStack = error instanceof Error ? error.stack : undefined;
			this.logger.error(`Error in getRevenueChartData: ${errorMessage}`);
			if (errorStack) {
				this.logger.error(errorStack);
			}
			throw error;
		}
	}

	async getCollectedPaymentsChartData(
		dto: GetRevenueChartDataDto
	): Promise<CollectedPaymentsChartDataPoint[]> {
		// Convert dates to start and end of day
		const startDateTime = new Date(dto.startDate);
		startDateTime.setHours(0, 0, 0, 0);

		const endDateTime = new Date(dto.endDate);
		endDateTime.setHours(23, 59, 59, 999);

		// Get date grouping strategy
		const { dateFormat, interval, truncPart } =
			this._getDateGroupingStrategy(startDateTime, endDateTime);

		const query = `
			WITH dates AS (
				SELECT generate_series(
					DATE_TRUNC($4, $2::timestamp),  -- Truncate start date to the provided interval (e.g., day, week, month)
					DATE_TRUNC($4, $3::timestamp),  -- Truncate end date to the provided interval
					$5::interval  -- The interval for grouping (e.g., '1 day', '1 week', '1 month')
				)::date AS date
			),
			CollectedPayments AS (
				SELECT DISTINCT ON (pd.id) 
					pd.id,
					pd.payment_type,
					pd.amount,
					DATE_TRUNC($4, pd.created_at) AS date  -- Truncate the payment date to the given interval
				FROM public.payment_details pd
				JOIN public.owner_brands ob ON pd.owner_id = ob.id
				JOIN public.patient_owners po ON ob.id = po.owner_id
				JOIN public.patients p ON po.patient_id = p.id
				WHERE pd.created_at BETWEEN $2 AND $3
					AND p.clinic_id = $1
					AND pd.type IN ('Invoice', 'Reconcile Invoice', 'Bulk Reconcile Invoice', 'Collect')
					AND pd.deleted_at IS NULL
			),
			RefundedPayments AS (
				SELECT DISTINCT ON (pd.id) 
					pd.id,
					pd.payment_type,
					pd.amount,
					DATE_TRUNC($4, pd.created_at) AS date  -- Truncate the payment date to the given interval
				FROM public.payment_details pd
				JOIN public.owner_brands ob ON pd.owner_id = ob.id
				JOIN public.patient_owners po ON ob.id = po.owner_id
				JOIN public.patients p ON po.patient_id = p.id
				WHERE pd.created_at BETWEEN $2 AND $3
					AND p.clinic_id = $1
					AND pd.type IN ('Return', 'Credit Note')
					AND pd.deleted_at IS NULL
			),
			collected_data AS (
				SELECT 
					date,
					SUM(CASE WHEN LOWER(payment_type) = LOWER('Cash') THEN amount ELSE 0 END) AS cash,
					SUM(CASE WHEN LOWER(payment_type) = LOWER('Card') THEN amount ELSE 0 END) AS card,
					SUM(CASE WHEN LOWER(payment_type) = LOWER('Wallet') THEN amount ELSE 0 END) AS wallet,
					SUM(CASE WHEN LOWER(payment_type) = LOWER('Cheque') THEN amount ELSE 0 END) AS cheque,
					SUM(CASE WHEN LOWER(payment_type) = LOWER('Bank Transfer') THEN amount ELSE 0 END) AS bankTransfer
				FROM CollectedPayments
				GROUP BY date
			),
			refunded_data AS (
				SELECT 
					date,
					SUM(CASE WHEN LOWER(payment_type) = LOWER('Cash') THEN amount ELSE 0 END) AS cash,
					SUM(CASE WHEN LOWER(payment_type) = LOWER('Card') THEN amount ELSE 0 END) AS card,
					SUM(CASE WHEN LOWER(payment_type) = LOWER('Wallet') THEN amount ELSE 0 END) AS wallet,
					SUM(CASE WHEN LOWER(payment_type) = LOWER('Cheque') THEN amount ELSE 0 END) AS cheque,
					SUM(CASE WHEN LOWER(payment_type) = LOWER('Bank Transfer') THEN amount ELSE 0 END) AS bankTransfer
				FROM RefundedPayments
				GROUP BY date
			)
			SELECT 
				TO_CHAR(d.date, $6) AS date,  -- Format the date for the chart (e.g., 'DD/MM/YY')
				COALESCE(cd.cash, 0) - COALESCE(rd.cash, 0) AS cash,
				COALESCE(cd.card, 0) - COALESCE(rd.card, 0) AS card,
				COALESCE(cd.wallet, 0) - COALESCE(rd.wallet, 0) AS wallet,
				COALESCE(cd.cheque, 0) - COALESCE(rd.cheque, 0) AS cheque,
				COALESCE(cd.bankTransfer, 0) - COALESCE(rd.bankTransfer, 0) AS bankTransfer
			FROM dates d
			LEFT JOIN collected_data cd ON d.date = cd.date
			LEFT JOIN refunded_data rd ON d.date = rd.date
			ORDER BY d.date;
		`;

		const result: CollectedPaymentsChartDataRow[] =
			await this.paymentDetailsRepository.query(query, [
				dto.clinicId,
				startDateTime,
				endDateTime,
				truncPart,
				interval,
				dateFormat
			]);

		return result.map((row: CollectedPaymentsChartDataRow) => ({
			date: row.date,
			cash: Number(row.cash) || 0,
			card: Number(row.card) || 0,
			wallet: Number(row.wallet) || 0,
			cheque: Number(row.cheque) || 0,
			bankTransfer: Number(row.banktransfer) || 0
		}));
	}

	async getAppointmentsChartData(
		dto: GetAppointmentsChartDataDto
	): Promise<AppointmentsChartResponse> {
		this.logger.log('Getting appointments chart data...');

		// Convert dates to start and end of day
		const startDateTime = new Date(dto.startDate);
		startDateTime.setHours(0, 0, 0, 0);

		const endDateTime = new Date(dto.endDate);
		endDateTime.setHours(23, 59, 59, 999);

		// Get clinic timezone and date grouping strategy
		const timezone = await this.getClinicTimezone(dto.clinicId);
		const { dateFormat, interval, truncPart } =
			this._getDateGroupingStrategy(startDateTime, endDateTime);

		const response: AppointmentsChartResponse = {
			total: [],
			missed: [],
			busiestDays: [],
			busiestHours: [],
			averageDuration: []
		};

		// Get base data for all types
		if (dto.type === AppointmentAnalyticsType.ALL) {
			const baseQuery = `
				WITH dates AS (
					SELECT generate_series(
						DATE_TRUNC($4, $2::timestamp),
						DATE_TRUNC($4, $3::timestamp),
						$5::interval
					)::date AS date
				),
				total_appointments AS (
					SELECT 
						DATE_TRUNC($4, appointments.date) as date,
						COUNT(*) as count
					FROM public.appointments
					WHERE clinic_id = $1
						AND date BETWEEN $2 AND $3
						AND deleted_at IS NULL
						AND status NOT IN ('Missed', 'MISSED')
					GROUP BY DATE_TRUNC($4, appointments.date)
				),
				missed_appointments AS (
					SELECT 
						DATE_TRUNC($4, appointments.date) as date,
						COUNT(*) as count
					FROM public.appointments
					WHERE clinic_id = $1
						AND date BETWEEN $2 AND $3
						AND deleted_at IS NULL
						AND status IN ('Missed', 'MISSED')
					GROUP BY DATE_TRUNC($4, appointments.date)
				)
				SELECT 
					TO_CHAR(d.date, $6) as date,
					COALESCE(ta.count, 0) as total,
					COALESCE(ma.count, 0) as missed
				FROM dates d
				LEFT JOIN total_appointments ta ON DATE_TRUNC($4, d.date) = ta.date
				LEFT JOIN missed_appointments ma ON DATE_TRUNC($4, d.date) = ma.date
				ORDER BY d.date;
			`;

			const result: AppointmentChartDataRow[] =
				await this.appointmentRepository.query(baseQuery, [
					dto.clinicId,
					startDateTime,
					endDateTime,
					truncPart,
					interval,
					dateFormat
				]);

			response.total = result.map((row: AppointmentChartDataRow) => ({
				date: row.date,
				total: Number(row.total || 0)
			}));

			response.missed = result.map((row: AppointmentChartDataRow) => ({
				date: row.date,
				missed: Number(row.missed || 0)
			}));
		}

		// Get busiest days data
		if (dto.type === AppointmentAnalyticsType.BUSIEST_DAYS) {
			const busiestDaysQuery = `
				WITH WeekBoundaries AS (
					-- Get the start of the week for startDate (previous Saturday)
					SELECT 
						CASE 
							WHEN EXTRACT(DOW FROM $2::timestamp) = 6 THEN $2::date
							ELSE $2::date - INTERVAL '1 day' * EXTRACT(DOW FROM $2::timestamp + INTERVAL '1 day')
						END AS week_start,
						CASE 
							WHEN EXTRACT(DOW FROM $3::timestamp) = 5 THEN $3::date
							ELSE $3::date + INTERVAL '1 day' * (5 - EXTRACT(DOW FROM $3::timestamp))
						END AS week_end
				),
				AllDays AS (
					-- Generate all days of the week
					SELECT 
						generate_series(0, 6) as day_number,
						TO_CHAR(
							'2000-01-02'::date + (generate_series(0, 6) || ' days')::interval, 
							'DY'
						) as day_name
				),
				WeekCount AS (
					-- Calculate number of weeks, considering only the actual date range
					SELECT 
						CEIL(
							EXTRACT(EPOCH FROM ($3::timestamp - $2::timestamp)) / 
							(24 * 60 * 60 * 7)
						)::int as total_weeks
				),
				DayStats AS (
					SELECT 
						ad.day_number,
						ad.day_name,
						COUNT(a.id) as total_appointments,
						wc.total_weeks as week_count
					FROM AllDays ad
					CROSS JOIN WeekCount wc
					LEFT JOIN public.appointments a ON 
						EXTRACT(DOW FROM a.date) = ad.day_number
						AND a.clinic_id = $1
						AND a.deleted_at IS NULL
						AND a.status NOT IN ('Missed', 'MISSED')
						AND a.date BETWEEN $2 AND $3
					GROUP BY 
						ad.day_number,
						ad.day_name,
						wc.total_weeks
				)
				SELECT 
					day_name as day,
					TRUNC(CAST(CAST(total_appointments AS DECIMAL) / GREATEST(week_count, 1) AS NUMERIC), 2) as count,
					week_count as weeks_counted,
					total_appointments as total
				FROM DayStats
				ORDER BY 
					day_number;
			`;

			const busiestDaysResult: BusiestDaysRow[] =
				await this.appointmentRepository.query(busiestDaysQuery, [
					dto.clinicId,
					startDateTime,
					endDateTime
				]);

			response.busiestDays = busiestDaysResult.map(
				(row: BusiestDaysRow) => ({
					day: row.day.trim(),
					count: Number(row.count || 0),
					weeksCount: Number(row.weeks_counted || 0),
					total: Number(row.total || 0)
				})
			);
		}

		// Get busiest hours data
		if (dto.type === AppointmentAnalyticsType.BUSIEST_HOURS) {
			const busiestHoursQuery = `
				WITH AppointmentHours AS (
					SELECT
						EXTRACT(HOUR FROM (start_time AT TIME ZONE $4))::integer as hour_number,
						date,
						COUNT(*) as daily_count
					FROM public.appointments
					WHERE clinic_id = $1
						AND date BETWEEN $2 AND $3
						AND deleted_at IS NULL
						AND status NOT IN ('Missed', 'MISSED')
					GROUP BY
						EXTRACT(HOUR FROM (start_time AT TIME ZONE $4)),
						date
				),
				HourStats AS (
					SELECT 
						hour_number,
						COUNT(DISTINCT date) as days_with_appointments,
						SUM(daily_count) as total_appointments
					FROM AppointmentHours
					GROUP BY hour_number
				),
				HourRange AS (
					SELECT 
						MIN(hour_number) as min_hour,
						MAX(hour_number) as max_hour
					FROM HourStats
					WHERE total_appointments > 0
				),
				AllHours AS (
					SELECT 
						generate_series(
							(SELECT min_hour FROM HourRange),
							(SELECT max_hour FROM HourRange)
						) as hour_number
				),
				DateRange AS (
					SELECT COUNT(DISTINCT date)::float as total_days
					FROM generate_series(
						$2::date,
						$3::date,
						'1 day'::interval
					) as date
				)
				SELECT 
					TO_CHAR(
						'2000-01-01 '::timestamp + (ah.hour_number || ' hours')::interval,
						'HH24:00'
					) as hour,
					COALESCE(hs.total_appointments, 0) as total,
					COALESCE(hs.days_with_appointments, 0) as days_count,
					TRUNC(CAST(CAST(COALESCE(hs.total_appointments, 0) AS DECIMAL) / 
						GREATEST(dr.total_days, 1) AS NUMERIC), 2) as count
				FROM AllHours ah
				CROSS JOIN DateRange dr
				LEFT JOIN HourStats hs ON ah.hour_number = hs.hour_number
				ORDER BY ah.hour_number;
			`;

			const busiestHoursResult: BusiestHoursRow[] =
				await this.appointmentRepository.query(busiestHoursQuery, [
					dto.clinicId,
					startDateTime,
					endDateTime,
					timezone
				]);

			response.busiestHours = busiestHoursResult.map(
				(row: BusiestHoursRow) => ({
					hour: String(row.hour),
					count: Number(row.count || 0),
					daysCount: Number(row.days_count || 0),
					total: Number(row.total || 0)
				})
			);
		}

		// Get average duration data
		if (dto.type === AppointmentAnalyticsType.AVERAGE_DURATION) {
			const avgDurationQuery = `
				WITH dates AS (
					SELECT generate_series(
						DATE_TRUNC($4, $2::timestamp),
						DATE_TRUNC($4, $3::timestamp),
						$5::interval
					)::date AS date
				),
				appointment_durations AS (
					SELECT 
						DATE_TRUNC($4, appointments.date) as date,
						AVG(
							CASE 
								WHEN EXTRACT(EPOCH FROM (receiving_care_time - checkin_time))/60 <= 1440 
								THEN EXTRACT(EPOCH FROM (receiving_care_time - checkin_time))/60 
							END
						) as avg_checkin_duration,
						AVG(
							CASE 
								WHEN EXTRACT(EPOCH FROM (checkout_time - receiving_care_time))/60 <= 1440 
								THEN EXTRACT(EPOCH FROM (checkout_time - receiving_care_time))/60 
							END
						) as avg_receiving_care_duration,
						AVG(
							CASE 
								WHEN EXTRACT(EPOCH FROM (updated_at - checkout_time))/60 <= 1440 
								THEN EXTRACT(EPOCH FROM (updated_at - checkout_time))/60 
							END
						) as avg_checkout_duration,
						AVG(
							CASE 
								WHEN EXTRACT(EPOCH FROM (updated_at - checkin_time))/60 <= 1440 
								THEN EXTRACT(EPOCH FROM (updated_at - checkin_time))/60 
							END
						) as avg_total_duration,
						COUNT(*) as total_appointments,
						COUNT(
							CASE 
								WHEN EXTRACT(EPOCH FROM (receiving_care_time - checkin_time))/60 <= 1440 
								AND EXTRACT(EPOCH FROM (checkout_time - receiving_care_time))/60 <= 1440
								AND EXTRACT(EPOCH FROM (updated_at - checkout_time))/60 <= 1440
								AND EXTRACT(EPOCH FROM (updated_at - checkin_time))/60 <= 1440
								THEN 1 
							END
						) as valid_appointments
					FROM public.appointments
					WHERE clinic_id = $1
						AND appointments.date BETWEEN $2 AND $3
						AND appointments.deleted_at IS NULL
						AND checkin_time IS NOT NULL 
						AND receiving_care_time IS NOT NULL 
						AND checkout_time IS NOT NULL
						-- Exclude appointments where any duration is negative
						AND receiving_care_time > checkin_time
						AND checkout_time > receiving_care_time
						AND updated_at > checkout_time
					GROUP BY DATE_TRUNC($4, appointments.date)
				)
				SELECT 
					TO_CHAR(d.date, $6) as date,
					COALESCE(ROUND(ad.avg_checkin_duration::numeric, 2), 0) as checkinDuration,
					COALESCE(ROUND(ad.avg_receiving_care_duration::numeric, 2), 0) as receivingCareDuration,
					COALESCE(ROUND(ad.avg_checkout_duration::numeric, 2), 0) as checkoutDuration,
					COALESCE(ROUND(ad.avg_total_duration::numeric, 2), 0) as totalDuration,
					COALESCE(ad.total_appointments, 0) as totalAppointments,
					COALESCE(ad.valid_appointments, 0) as validAppointments
				FROM dates d
				LEFT JOIN appointment_durations ad ON DATE_TRUNC($4, d.date) = ad.date
				ORDER BY d.date;
			`;

			const avgDurationResult: AverageDurationRow[] =
				await this.appointmentRepository.query(avgDurationQuery, [
					dto.clinicId,
					startDateTime,
					endDateTime,
					truncPart,
					interval,
					dateFormat
				]);

			response.averageDuration = avgDurationResult.map(
				(row: AverageDurationRow) => ({
					date: row.date,
					checkinDuration: Number(row.checkinduration || 0),
					receivingCareDuration: Number(
						row.receivingcareduration || 0
					),
					checkoutDuration: Number(row.checkoutduration || 0),
					totalDuration: Number(row.totalduration || 0),
					validAppointments: Number(row.validappointments || 0)
				})
			);
		}

		return response;
	}

	async getDoctorSummary(
		dto: GetDoctorSummaryDto
	): Promise<DoctorSummaryResponseDto[]> {
		try {
			this.logger.log('Getting doctor summary data...');

			// Convert dates to start and end of day
			const startDateTime = new Date(dto.startDate);
			startDateTime.setHours(0, 0, 0, 0);

			const endDateTime = new Date(dto.endDate);
			endDateTime.setHours(23, 59, 59, 999);

			const query = `
				WITH invoice_summary AS (
					SELECT 
						c.appointment_id,
						SUM(i.amount_payable::numeric) AS total_invoice_amount
					FROM public.carts c
					LEFT JOIN public.invoices i ON i.cart_id = c.id
					GROUP BY c.appointment_id
				),
				completed_appointments AS (
					SELECT 
						a.id,
						a.receiving_care_time,
						a.checkout_time,
						a.end_time,
						EXTRACT(EPOCH FROM (COALESCE(a.checkout_time, a.end_time) - a.receiving_care_time)) / 60 as duration_minutes
					FROM public.appointments a
					WHERE a.status = 'Completed'
					AND a.deleted_at IS NULL
					AND a.receiving_care_time IS NOT NULL
					AND (a.checkout_time IS NOT NULL OR a.end_time IS NOT NULL)
				)
				SELECT 
					u.first_name || ' ' || u.last_name AS doctor_name,
					COUNT(DISTINCT ca.id) AS num_appointments,
					COALESCE(SUM(inv.total_invoice_amount), 0) AS total_revenue,
					COALESCE(SUM(inv.total_invoice_amount) / NULLIF(COUNT(DISTINCT ca.id), 0), 0) AS revenue_per_appointment,
					COALESCE(AVG(ca.duration_minutes), 0) AS avg_appointment_duration_minutes
				FROM public.appointment_doctors ad
				JOIN public.clinic_users cu ON ad.clinic_user_id = cu.id
				JOIN public.users u ON cu.user_id = u.id
				JOIN completed_appointments ca ON ad.appointment_id = ca.id
				LEFT JOIN invoice_summary inv ON inv.appointment_id = ca.id
				WHERE (u.role_id = 'ea907601-9a89-4551-94a6-8e58bbeeb0c4' OR u.role_id = '3623c5c1-7621-473e-a2b8-667df02200e3')
				AND ad.appointment_id IN (
					SELECT id FROM public.appointments 
					WHERE clinic_id = $1 
					AND date BETWEEN $2 AND $3
				)
				GROUP BY u.id, u.first_name, u.last_name
				ORDER BY total_revenue DESC;
			`;

			const result: DoctorSummaryRow[] =
				await this.appointmentRepository.query(query, [
					dto.clinicId,
					startDateTime,
					endDateTime
				]);

			return result.map((row: DoctorSummaryRow) => ({
				doctorName: row.doctor_name,
				numAppointments: Number(row.num_appointments || 0),
				totalRevenue: Number(row.total_revenue || 0),
				revenuePerAppointment: Number(row.revenue_per_appointment || 0),
				avgAppointmentDurationMinutes: Number(
					row.avg_appointment_duration_minutes || 0
				)
			}));
		} catch (error) {
			this.logger.error('Error in getDoctorSummary:', error);
			throw error;
		}
	}

	async getSummary(dto: GetSummaryDto): Promise<SummaryResponseDto> {
		try {
			this.logger.log('Getting summary data...');

			// Convert dates to start and end of day
			const startDateTime = new Date(dto.startDate);
			startDateTime.setHours(0, 0, 0, 0);

			const endDateTime = new Date(dto.endDate);
			endDateTime.setHours(23, 59, 59, 999);

			// Get appointments completed
			const appointmentsQuery = `
				SELECT COUNT(*) as count
				FROM public.appointments
				WHERE clinic_id = $1
				AND date BETWEEN $2 AND $3
				AND status = 'Completed'
				AND deleted_at IS NULL
			`;

			const appointmentsResult = await this.appointmentRepository.query(
				appointmentsQuery,
				[dto.clinicId, startDateTime, endDateTime]
			);
			const appointmentsCompleted = Number(
				appointmentsResult[0]?.count || 0
			);

			// ------------------------------------------------------------------
			// INVOICES GENERATED (exclude cancelled invoices)
			// ------------------------------------------------------------------
			const invoicesQuery = `
				SELECT COUNT(*) AS count
				FROM public.invoices i
				WHERE i.clinic_id = $1
				AND i.created_at BETWEEN $2 AND $3
				AND i.invoice_type = 'Invoice'
				AND i.status <> 'cancelled'
			`;

			const invoicesResult = await this.invoiceRepository.query(
				invoicesQuery,
				[dto.clinicId, startDateTime, endDateTime]
			);
			const invoicesGenerated = Number(invoicesResult[0]?.count || 0);

			// ------------------------------------------------------------------
			// TOTAL BILLING
			// For all invoices (fully paid, partially paid, written off) – include full invoice_amount
			// Only cancelled invoices contribute 0
			// ------------------------------------------------------------------
			const billingQuery = `
				SELECT COALESCE(SUM(
					CASE 
						WHEN i.status = 'cancelled' THEN 0
						ELSE COALESCE(i.invoice_amount::numeric,0)
					END
				),0) AS total
				FROM public.invoices i
					WHERE i.clinic_id = $1
					AND i.created_at BETWEEN $2 AND $3
					AND i.invoice_type = 'Invoice'
			`;

			const billingResult = await this.invoiceRepository.query(
				billingQuery,
				[dto.clinicId, startDateTime, endDateTime]
			);
			const totalBilling = Number(billingResult[0]?.total || 0);

			// Get credit notes generated
			const creditNotesQuery = `
				SELECT COUNT(*) as count
				FROM public.invoices i
				WHERE i.clinic_id = $1
				AND i.created_at BETWEEN $2 AND $3
				AND i.invoice_type = 'Refund'	
			`;

			const creditNotesResult = await this.invoiceRepository.query(
				creditNotesQuery,
				[dto.clinicId, startDateTime, endDateTime]
			);
			const creditNotesGenerated = Number(
				creditNotesResult[0]?.count || 0
			);

			// Get total credit notes amount
			const creditNotesTotalQuery = `
				SELECT COALESCE(SUM(i.invoice_amount::numeric), 0) as total
				FROM public.invoices i
				WHERE i.clinic_id = $1
				AND i.created_at BETWEEN $2 AND $3
				AND i.invoice_type = 'Refund'
			`;

			const creditNotesTotalResult = await this.invoiceRepository.query(
				creditNotesTotalQuery,
				[dto.clinicId, startDateTime, endDateTime]
			);
			const totalCreditNotes = Number(
				creditNotesTotalResult[0]?.total || 0
			);

			// Get amount collected by payment type - revised to only include actual collections
			const paymentsQuery = `
				SELECT 
					pd.payment_type as payment_mode,
					COALESCE(SUM(pd.amount::numeric), 0) as total
				FROM public.payment_details pd
				WHERE pd.clinic_id = $1
				AND pd.created_at BETWEEN $2 AND $3
				AND pd.type IN ('Invoice', 'Reconcile Invoice', 'Bulk Reconcile Invoice', 'Collect')
				AND pd.payment_type IN ('Cash', 'Card', 'Wallet', 'Cheque', 'Bank Transfer')
				AND pd.amount > 0
				AND pd.deleted_at IS NULL
				GROUP BY pd.payment_type
			`;

			const paymentsResult = await this.paymentDetailsRepository.query(
				paymentsQuery,
				[dto.clinicId, startDateTime, endDateTime]
			);

			const amountCollected = {
				cash: 0,
				card: 0,
				wallet: 0,
				cheque: 0,
				bankTransfer: 0,
				total: 0
			};

			paymentsResult.forEach((row: PaymentModeRow) => {
				const amount = Number(row.total || 0);
				switch (row.payment_mode?.toLowerCase()) {
					case 'cash':
						amountCollected.cash = amount;
						break;
					case 'card':
						amountCollected.card = amount;
						break;
					case 'wallet':
						amountCollected.wallet = amount;
						break;
					case 'cheque':
						amountCollected.cheque = amount;
						break;
					case 'bank transfer':
						amountCollected.bankTransfer = amount;
						break;
				}
			});

			// Calculate total amount collected
			amountCollected.total =
				amountCollected.cash +
				amountCollected.card +
				amountCollected.wallet +
				amountCollected.cheque +
				amountCollected.bankTransfer;

			// Get amount refunded by payment type
			const refundsQuery = `
				SELECT 
					pd.payment_type as payment_mode,
					COALESCE(SUM(pd.amount::numeric), 0) as total
				FROM public.payment_details pd
				WHERE pd.clinic_id = $1
				AND pd.created_at BETWEEN $2 AND $3
				AND pd.type IN ('Credit Note', 'Return')
				AND pd.payment_type IN ('Cash', 'Card', 'Wallet', 'Cheque', 'Bank Transfer')
				AND pd.amount > 0
				AND pd.deleted_at IS NULL
				GROUP BY pd.payment_type
			`;

			const refundsResult = await this.paymentDetailsRepository.query(
				refundsQuery,
				[dto.clinicId, startDateTime, endDateTime]
			);

			const amountRefunded = {
				cash: 0,
				card: 0,
				wallet: 0,
				cheque: 0,
				bankTransfer: 0,
				total: 0
			};

			refundsResult.forEach((row: PaymentModeRow) => {
				const amount = Number(row.total || 0);
				switch (row.payment_mode?.toLowerCase()) {
					case 'cash':
						amountRefunded.cash = amount;
						break;
					case 'card':
						amountRefunded.card = amount;
						break;
					case 'wallet':
						amountRefunded.wallet = amount;
						break;
					case 'cheque':
						amountRefunded.cheque = amount;
						break;
					case 'bank transfer':
						amountRefunded.bankTransfer = amount;
						break;
				}
			});

			// Calculate total amount refunded
			amountRefunded.total =
				amountRefunded.cash +
				amountRefunded.card +
				amountRefunded.wallet +
				amountRefunded.cheque +
				amountRefunded.bankTransfer;

			// ------------------------------------------------------------------
			// BAD DEBTS (WRITE OFF) CALCULATION
			// Only use payment details with type 'Write Off Invoice'
			// ------------------------------------------------------------------
			const badDebtsQuery = `
				SELECT COALESCE(SUM(pd.amount::numeric), 0) as total
				FROM public.payment_details pd
				WHERE pd.clinic_id = $1
				AND pd.created_at BETWEEN $2 AND $3
				AND pd.type = 'Write Off Invoice'
				AND pd.deleted_at IS NULL
			`;

			const badDebtsResult = await this.paymentDetailsRepository.query(
				badDebtsQuery,
				[dto.clinicId, startDateTime, endDateTime]
			);

			const badDebts = Number(badDebtsResult[0]?.total || 0);

			return {
				appointmentsCompleted,
				invoicesGenerated,
				totalBilling,
				creditNotesGenerated,
				totalCreditNotes,
				amountCollected,
				amountRefunded,
				badDebts
			};
		} catch (error) {
			this.logger.error('Error in getSummary:', error);
			throw error;
		}
	}

	private async _addDataSheet(
		workbook: XLSX.WorkBook,
		sheetName: string,
		clinicId: string,
		startDate: Date,
		endDate: Date,
		fetchData: DataFetcherFunction,
		timezone: string
	) {
		try {
			const data = await fetchData(
				clinicId,
				startDate,
				endDate,
				timezone
			);
			if (data.length > 0) {
				const ws = await this.createDataSheet(data);
				XLSX.utils.book_append_sheet(workbook, ws, sheetName);
			}
		} catch (error: unknown) {
			const errorMessage =
				error instanceof Error ? error.message : 'Unknown error';
			const errorStack = error instanceof Error ? error.stack : undefined;
			this.logger.error(
				`Error processing ${sheetName} data: ${errorMessage}`
			);
			if (errorStack) {
				this.logger.error(errorStack);
			}
			throw error;
		}
	}

	private async _generateRevenueByBillingReport(
		workbook: XLSX.WorkBook,
		clinicId: string,
		startDate: Date,
		endDate: Date,
		timezone: string
	) {
		try {
			const metadataWs = await this.createMetadataSheet(
				clinicId,
				startDate,
				endDate,
				'Revenue By Billing Report'
			);
			XLSX.utils.book_append_sheet(workbook, metadataWs, 'Report Info');

			await this._addDataSheet(
				workbook,
				'Products',
				clinicId,
				startDate,
				endDate,
				this.getProductsBillingData.bind(this),
				timezone
			);
			await this._addDataSheet(
				workbook,
				'Services',
				clinicId,
				startDate,
				endDate,
				this.getServicesBillingData.bind(this),
				timezone
			);
			await this._addDataSheet(
				workbook,
				'Vaccinations',
				clinicId,
				startDate,
				endDate,
				this.getVaccinationsBillingData.bind(this),
				timezone
			);
			await this._addDataSheet(
				workbook,
				'Medications',
				clinicId,
				startDate,
				endDate,
				this.getMedicationsBillingData.bind(this),
				timezone
			);
			await this._addDataSheet(
				workbook,
				'Diagnostics',
				clinicId,
				startDate,
				endDate,
				this.getLabReportsBillingData.bind(this),
				timezone
			);
			await this._addDataSheet(
				workbook,
				'Bad Debt (Write-offs)',
				clinicId,
				startDate,
				endDate,
				this.getBadDebtData.bind(this),
				timezone
			);
		} catch (error: unknown) {
			const errorMessage =
				error instanceof Error ? error.message : 'Unknown error';
			const errorStack = error instanceof Error ? error.stack : undefined;
			this.logger.error(
				`Error creating Revenue By Billing report: ${errorMessage}`
			);
			if (errorStack) {
				this.logger.error(errorStack);
			}
			throw error;
		}
	}

	private async _generateRevenueByPatientReport(
		workbook: XLSX.WorkBook,
		clinicId: string,
		startDate: Date,
		endDate: Date,
		timezone: string
	) {
		try {
			const metadataWs = await this.createMetadataSheet(
				clinicId,
				startDate,
				endDate,
				'Revenue By Patient Report'
			);
			XLSX.utils.book_append_sheet(workbook, metadataWs, 'Report Info');

			await this._addDataSheet(
				workbook,
				'Patient Transactions',
				clinicId,
				startDate,
				endDate,
				this.getPatientsBillingData.bind(this),
				timezone
			);

			await this._addDataSheet(
				workbook,
				'Bad Debt (Write-offs)',
				clinicId,
				startDate,
				endDate,
				this.getBadDebtData.bind(this),
				timezone
			);
		} catch (error: unknown) {
			const errorMessage =
				error instanceof Error ? error.message : 'Unknown error';
			const errorStack = error instanceof Error ? error.stack : undefined;
			this.logger.error(
				`Error creating Revenue By Patient report: ${errorMessage}`
			);
			if (errorStack) {
				this.logger.error(errorStack);
			}
			throw error;
		}
	}

	private async _generateCollectedPaymentsReport(
		workbook: XLSX.WorkBook,
		clinicId: string,
		startDate: Date,
		endDate: Date,
		timezone: string
	) {
		try {
			const summaryWs = await this.createPaymentsSummarySheet(
				clinicId,
				startDate,
				endDate
			);
			XLSX.utils.book_append_sheet(
				workbook,
				summaryWs,
				'Payment Summary'
			);

			const paymentModes = [
				'Wallet',
				'Card',
				'Cash',
				'Cheque',
				'Bank Transfer'
			];

			const collectedPaymentsByMode = await this.getAllPaymentsByMode(
				clinicId,
				startDate,
				endDate,
				'collect',
				timezone
			);

			for (const mode of paymentModes) {
				const lowerMode = mode.toLowerCase();
				const collectedData =
					collectedPaymentsByMode.get(lowerMode) || [];
				if (collectedData.length > 0) {
					const ws = await this.createPaymentModeSheet(
						collectedData,
						mode,
						'collected'
					);
					XLSX.utils.book_append_sheet(
						workbook,
						ws,
						`${mode} (Collected)`
					);
				}
			}

			const returnedPaymentsByMode = await this.getAllPaymentsByMode(
				clinicId,
				startDate,
				endDate,
				'return',
				timezone
			);

			for (const mode of paymentModes) {
				const lowerMode = mode.toLowerCase();
				const returnedData =
					returnedPaymentsByMode.get(lowerMode) || [];
				if (returnedData.length > 0) {
					const ws = await this.createPaymentModeSheet(
						returnedData,
						mode,
						'returned'
					);
					XLSX.utils.book_append_sheet(
						workbook,
						ws,
						`${mode} (Returned)`
					);
				}
			}

			await this._addDataSheet(
				workbook,
				'Bad Debt (Write-offs)',
				clinicId,
				startDate,
				endDate,
				this.getBadDebtData.bind(this),
				timezone
			);
		} catch (error: unknown) {
			const errorMessage =
				error instanceof Error ? error.message : 'Unknown error';
			const errorStack = error instanceof Error ? error.stack : undefined;
			this.logger.error(
				`Error creating Collected Payments report: ${errorMessage}`
			);
			if (errorStack) {
				this.logger.error(errorStack);
			}
			throw error;
		}
	}

	private async _createCollectedPaymentsLedgerSheet(
		workbook: XLSX.WorkBook,
		clinicId: string,
		startDate: Date,
		endDate: Date,
		timezone: string
	) {
		const collectedPaymentsData = await this.getCollectedPaymentsLedgerData(
			clinicId,
			startDate,
			endDate,
			timezone
		);
		if (collectedPaymentsData.length > 0) {
			const ws = XLSX.utils.aoa_to_sheet([]);
			const startDateFormatted = startDate.toLocaleDateString('en-GB', {
				day: 'numeric',
				month: 'long',
				year: 'numeric'
			});
			const endDateFormatted = endDate.toLocaleDateString('en-GB', {
				day: 'numeric',
				month: 'long',
				year: 'numeric'
			});
			const summaryText = `A total of ${collectedPaymentsData.length} invoices were issued between ${startDateFormatted} and ${endDateFormatted}`;
			XLSX.utils.sheet_add_aoa(ws, [[summaryText]], { origin: 'A1' });
			XLSX.utils.sheet_add_aoa(ws, [[]], { origin: 'A2' });
			XLSX.utils.sheet_add_json(ws, collectedPaymentsData, {
				origin: 'A3',
				skipHeader: false
			});
			ws['!merges'] ??= [];
			ws['!merges'].push({ s: { r: 0, c: 0 }, e: { r: 0, c: 10 } });
			ws['!cols'] = [
				{ wch: 20 },
				{ wch: 20 },
				{ wch: 25 },
				{ wch: 15 },
				{ wch: 18 },
				{ wch: 15 },
				{ wch: 12 },
				{ wch: 20 },
				{ wch: 15 },
				{ wch: 20 }
			];
			const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');
			const headerRow = 2;
			if (!ws['!styles']) ws['!styles'] = [];
			for (let col = range.s.c; col <= range.e.c; col++) {
				const cellRef = XLSX.utils.encode_cell({
					r: headerRow,
					c: col
				});
				if (ws[cellRef]) {
					ws[cellRef].s = {
						fill: { fgColor: { rgb: 'FFFF00' } },
						font: { bold: true, sz: 12 }
					};
				}
			}
			XLSX.utils.book_append_sheet(workbook, ws, 'Invoice Summary');
		}
	}

	private async _createReturnedPaymentsLedgerSheet(
		workbook: XLSX.WorkBook,
		clinicId: string,
		startDate: Date,
		endDate: Date,
		timezone: string
	) {
		const returnedPaymentsData = await this.getReturnedPaymentsLedgerData(
			clinicId,
			startDate,
			endDate,
			timezone
		);
		if (returnedPaymentsData.length > 0) {
			const ws = XLSX.utils.aoa_to_sheet([]);
			const startDateFormatted = startDate.toLocaleDateString('en-GB', {
				day: 'numeric',
				month: 'long',
				year: 'numeric'
			});
			const endDateFormatted = endDate.toLocaleDateString('en-GB', {
				day: 'numeric',
				month: 'long',
				year: 'numeric'
			});
			const summaryText = `A total of ${returnedPaymentsData.length} credit notes were issued between ${startDateFormatted} and ${endDateFormatted}`;
			XLSX.utils.sheet_add_aoa(ws, [[summaryText]], { origin: 'A1' });
			XLSX.utils.sheet_add_aoa(ws, [[]], { origin: 'A2' });
			XLSX.utils.sheet_add_json(ws, returnedPaymentsData, {
				origin: 'A3',
				skipHeader: false
			});
			ws['!merges'] ??= [];
			ws['!merges'].push({ s: { r: 0, c: 0 }, e: { r: 0, c: 6 } });
			ws['!cols'] = [
				{ wch: 20 },
				{ wch: 20 },
				{ wch: 25 },
				{ wch: 15 },
				{ wch: 18 },
				{ wch: 15 },
				{ wch: 12 }
			];
			const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');
			const headerRow = 2;
			if (!ws['!styles']) ws['!styles'] = [];
			for (let col = range.s.c; col <= range.e.c; col++) {
				const cellRef = XLSX.utils.encode_cell({
					r: headerRow,
					c: col
				});
				if (ws[cellRef]) {
					ws[cellRef].s = {
						fill: { fgColor: { rgb: 'FFFF00' } },
						font: { bold: true, sz: 12 }
					};
				}
			}
			XLSX.utils.book_append_sheet(workbook, ws, 'Credit Notes Summary');
		}
	}
	/**
	 * Optimized method that fetches payment data for all modes in a single query
	 * Replaces the need to call getPaymentsByMode multiple times
	 */
	private async getAllPaymentsByMode(
		clinicId: string,
		startDate: Date,
		endDate: Date,
		type: 'collect' | 'return',
		timezone: string
	): Promise<Map<string, AllPaymentsModeData[]>> {
		this.logger.log('Fetching all payments by mode in a single query', {
			clinicId,
			startDate,
			endDate,
			type
		});

		// This query retrieves payment data for all payment modes in one go
		// We use CASE statements to categorize each payment by its payment_type
		const query = `
			WITH PaymentData AS (
				SELECT 
					pd.id,
					pd.owner_id,
					pd.reference_alpha_id as receipt_number,
					pd.amount::numeric as amount,
					pd.type,
					LOWER(pd.payment_type) as payment_type,
					pd.patient_id,
					pd.invoice_id,
					TO_CHAR(
						pd.created_at AT TIME ZONE 'UTC' AT TIME ZONE $5,
						'DD/MM/YY HH24:MI:SS'
					) AS payment_date,
					pd.created_at,
					CONCAT(ob.first_name, ' ', ob.last_name) as owner_name,
					CASE 
						WHEN pd.patient_id IS NOT NULL THEN 
							(SELECT p.patient_name 
							FROM public.patients p 
							WHERE p.id = pd.patient_id)
						ELSE NULL
					END as patient_name,
					CASE 
						WHEN pd.invoice_id IS NOT NULL THEN 
							(SELECT i.reference_alpha_id 
							FROM public.invoices i 
							WHERE i.id = pd.invoice_id)
						ELSE NULL
					END as invoice_reference,
					CASE 
						WHEN pd.type IN ('Invoice', 'Reconcile Invoice', 'Bulk Reconcile Invoice') THEN 'Clear Invoice Balance'
						WHEN pd.type IN ('Credit Note') THEN 'Clear Credit Note Balance'
						WHEN pd.type = 'Collect' THEN 'Add Credits'
						WHEN pd.type = 'Return' THEN 'Return Credits'
					END as action
				FROM public.payment_details pd
				JOIN public.owner_brands ob ON pd.owner_id = ob.id
				WHERE pd.clinic_id = $1
				AND pd.created_at BETWEEN $2 AND $3
				AND pd.amount > 0
				AND pd.deleted_at IS NULL
				AND (
					CASE 
						WHEN $4 = 'collect' THEN pd.type IN ('Invoice', 'Reconcile Invoice', 'Bulk Reconcile Invoice', 'Collect')
						ELSE pd.type IN ('Credit Note', 'Return')
					END
				)
			),
			GroupedPayments AS (
				SELECT 
					owner_name,
					payment_type,
					receipt_number,
					MIN(payment_date) as payment_date,
					MIN(created_at) as created_at,
					SUM(amount) as total_amount,
					STRING_AGG(DISTINCT action, ', ' ORDER BY action) as actions,
					STRING_AGG(DISTINCT patient_name, ', ' ORDER BY patient_name) FILTER (WHERE patient_name IS NOT NULL) as pets,
					STRING_AGG(DISTINCT invoice_reference, ', ' ORDER BY invoice_reference) FILTER (WHERE invoice_reference IS NOT NULL) as invoice_references
				FROM PaymentData
				GROUP BY 
					owner_name,
					payment_type,
					receipt_number
			)
			SELECT 
				payment_type,
				owner_name as "Owner Name",
				payment_type as "Payment Mode",
				receipt_number as "Receipt Number",
				payment_date as "Payment Date",
				total_amount as "Amount",
				actions as "Action",
				COALESCE(pets, '-') as "Pet",
				COALESCE(invoice_references, '-') as "Invoice Cleared",
				created_at
			FROM GroupedPayments
			ORDER BY created_at DESC;
		`;

		const results = await this.paymentDetailsRepository.query(query, [
			clinicId,
			startDate,
			endDate,
			type,
			timezone
		]);

		// Organize results by payment mode
		const paymentsByMode = new Map<string, AllPaymentsModeData[]>();

		// Initialize standard payment modes
		const standardModes = [
			'wallet',
			'card',
			'cash',
			'cheque',
			'bank transfer'
		];
		standardModes.forEach(mode => paymentsByMode.set(mode, []));

		// Group results by payment mode
		results.forEach((row: PaymentTypeRow) => {
			const mode = row.payment_type.toLowerCase();
			if (!paymentsByMode.has(mode)) {
				paymentsByMode.set(mode, []);
			}

			// Format the row object to match the expected output format
			const formattedRow = {
				'Owner Name': row['Owner Name'],
				'Payment Mode': row['Payment Mode'],
				'Receipt Number': row['Receipt Number'],
				'Payment Date': row['Payment Date'],
				Amount: Number(row['Amount'] || 0),
				Action: row['Action'],
				Pet: row['Pet'],
				'Invoice Cleared': row['Invoice Cleared']
			};

			paymentsByMode.get(mode)?.push(formattedRow);
		});

		return paymentsByMode;
	}

	private async createPaymentModeSheet(
		data: ExcelDataRow[],
		mode: string,
		type: 'collected' | 'returned'
	): Promise<XLSX.WorkSheet> {
		// Calculate total amount and count
		const totalAmount = data.reduce(
			(sum, row) => sum + Number(row['Amount'] || 0),
			0
		);
		const count = data.length;

		// Create header rows
		const headerRows = [
			[
				{
					v: `Total amount ${type} through ${mode.toLowerCase()} payments = ${totalAmount.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} (${count} transactions)`,
					s: {
						font: { bold: true },
						alignment: { horizontal: 'left' }
					}
				}
			],
			[''] // Empty row for spacing
		];

		// Define column headers - conditional naming for the last column
		const lastColumnHeader =
			type === 'returned' ? 'Credit Note' : 'Invoice Cleared';
		const columnHeaders = [
			'Owner Name',
			'Payment Mode',
			'Receipt Number',
			'Payment Date',
			type === 'collected' ? 'Amount Collected' : 'Amount Returned',
			'Action',
			'Pet',
			lastColumnHeader
		];

		// Add column headers with styling
		headerRows.push(
			columnHeaders.map(header => ({
				v: header,
				s: {
					font: { bold: true },
					border: {
						top: { style: 'thin' },
						bottom: { style: 'thin' },
						left: { style: 'thin' },
						right: { style: 'thin' }
					},
					alignment: { horizontal: 'center' }
				}
			}))
		);

		// Create worksheet
		const ws = XLSX.utils.aoa_to_sheet(headerRows);

		// Add data rows
		const dataRows = data.map(row => [
			row['Owner Name'],
			row['Payment Mode'],
			row['Receipt Number'],
			row['Payment Date'],
			Number(row['Amount'] || 0),
			row['Action'],
			row['Pet'],
			row['Invoice Cleared'] // The data key remains the same, only the header changes
		]);

		// Append data rows to worksheet
		XLSX.utils.sheet_add_aoa(ws, dataRows, { origin: headerRows.length });

		// Set column widths
		ws['!cols'] = [
			{ wch: 25 }, // Owner Name
			{ wch: 15 }, // Payment Mode
			{ wch: 15 }, // Receipt Number
			{ wch: 20 }, // Payment Date
			{ wch: 15 }, // Amount
			{ wch: 30 }, // Action
			{ wch: 25 }, // Pet
			{ wch: 15 } // Invoice Cleared/Credit Note
		];

		// Apply number format to amount column
		const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');
		for (let R = headerRows.length; R <= range.e.r; ++R) {
			const amountCell = XLSX.utils.encode_cell({ r: R, c: 4 }); // Column E (Amount)
			if (ws[amountCell]) {
				ws[amountCell].z = '#,##0.00';
				ws[amountCell].s = {
					alignment: { horizontal: 'right' }
				};
			}
		}

		return ws;
	}
}

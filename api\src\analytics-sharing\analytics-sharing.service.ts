import {
  Injectable,
  HttpException,
  HttpStatus,
  forwardRef,
  Inject
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { AnalyticsDocumentRequest } from './entities/analytics-document-request.entity';
import { ShareAnalyticsDocumentsDto } from './dto/share-analytics-documents.dto';
import { AnalyticsDocumentGenerationJob } from './interfaces/analytics-sharing.interface';
import { SqsService } from '../utils/aws/sqs/sqs.service';
import { WinstonLogger } from '../utils/logger/winston-logger.service';

@Injectable()
export class AnalyticsSharingService {
  constructor(
    @InjectRepository(AnalyticsDocumentRequest)
    private readonly requestRepository: Repository<AnalyticsDocumentRequest>,
    private readonly sqsService: SqsService,
    private readonly logger: WinstonLogger
  ) {}

  async initiateDocumentSharing(
    dto: ShareAnalyticsDocumentsDto,
    userId: string
  ): Promise<{ requestId: string; message: string }> {
    try {
      // Generate unique request ID
      const requestId = uuidv4();

      // Validate date range
      const startDate = new Date(dto.startDate);
      const endDate = new Date(dto.endDate);
      
      if (startDate >= endDate) {
        throw new HttpException(
          'Start date must be before end date',
          HttpStatus.BAD_REQUEST
        );
      }

      // Set expiration for 24 hours
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24);

      // Create request record
      const request = await this.requestRepository.save({
        requestId,
        clinicId: dto.clinicId,
        userId,
        startDate,
        endDate,
        documentTypes: dto.documentTypes,
        shareMode: dto.shareMode,
        recipientType: dto.recipientType,
        email: dto.email,
        phoneNumber: dto.phoneNumber,
        includeExcelReport: dto.includeExcelReport,
        status: 'pending',
        expiresAt
      });

      // Create SQS job for document generation
      const job: AnalyticsDocumentGenerationJob = {
        requestId,
        clinicId: dto.clinicId,
        userId,
        startDate: dto.startDate,
        endDate: dto.endDate,
        documentTypes: dto.documentTypes,
        shareMode: dto.shareMode,
        recipientType: dto.recipientType,
        email: dto.email,
        phoneNumber: dto.phoneNumber,
        includeExcelReport: dto.includeExcelReport
      };

      // Send to SQS queue for processing
      await this.sqsService.sendMessage({
        queueName: 'NidanaAnalyticsDocuments',
        messageBody: {
          serviceType: 'generateAnalyticsDocuments',
          data: job
        },
        deduplicationId: requestId,
        messageGroupId: `analytics-${dto.clinicId}`
      });

      this.logger.log('Analytics document sharing request initiated', {
        requestId,
        clinicId: dto.clinicId,
        userId,
        documentTypes: dto.documentTypes
      });

      return {
        requestId,
        message: 'Document sharing request initiated successfully'
      };

    } catch (error) {
      this.logger.error('Error initiating document sharing', {
        error: error.message,
        dto,
        userId
      });

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        'Failed to initiate document sharing',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async getRequestStatus(requestId: string) {
    const request = await this.requestRepository.findOne({
      where: { requestId }
    });

    if (!request) {
      throw new HttpException(
        'Request not found',
        HttpStatus.NOT_FOUND
      );
    }

    return {
      requestId: request.requestId,
      status: request.status,
      createdAt: request.createdAt,
      updatedAt: request.updatedAt,
      expiresAt: request.expiresAt,
      errorMessage: request.errorMessage
    };
  }

  async getRequestsForClinic(
    clinicId: string,
    page: number = 1,
    limit: number = 10
  ) {
    const [requests, total] = await this.requestRepository.findAndCount({
      where: { clinicId },
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit
    });

    return {
      requests: requests.map(req => ({
        requestId: req.requestId,
        status: req.status,
        documentTypes: req.documentTypes,
        shareMode: req.shareMode,
        recipientType: req.recipientType,
        createdAt: req.createdAt,
        expiresAt: req.expiresAt
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  async updateRequestStatus(
    requestId: string,
    status: 'processing' | 'completed' | 'failed',
    s3DocumentKey?: string,
    s3ExcelKey?: string,
    errorMessage?: string
  ) {
    await this.requestRepository.update(
      { requestId },
      {
        status,
        s3DocumentKey,
        s3ExcelKey,
        errorMessage,
        updatedAt: new Date()
      }
    );
  }
}

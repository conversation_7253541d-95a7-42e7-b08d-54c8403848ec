'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Modal } from '../Modal';
import { Button } from '../../atoms/Button';
import { Input } from '../../atoms/Input';
import { Checkbox } from '../../atoms/Checkbox';
import { RadioGroup } from '../../molecules/RadioGroup';
import { useShareAnalyticsDocuments } from '../../services/analytics-sharing.queries';
import { toast } from 'react-toastify';

interface ShareAnalyticsModalProps {
  isOpen: boolean;
  onClose: () => void;
  timeRange: {
    startDate: string;
    endDate: string;
  };
  clinicId: string;
}

interface FormData {
  documentTypes: string[];
  shareMode: string[];
  recipientType: 'client' | 'other';
  email?: string;
  phoneNumber?: string;
  includeExcelReport: boolean;
}

const documentTypeOptions = [
  { value: 'invoices', label: 'Invoices' },
  { value: 'receipts', label: 'Receipts' },
  { value: 'credit_notes', label: 'Credit Notes' }
];

const shareModeOptions = [
  { value: 'email', label: 'Email' },
  { value: 'whatsapp', label: 'WhatsApp' }
];

const recipientTypeOptions = [
  { value: 'client', label: 'Send to my email' },
  { value: 'other', label: 'Send to different email/phone' }
];

export const ShareAnalyticsModal: React.FC<ShareAnalyticsModalProps> = ({
  isOpen,
  onClose,
  timeRange,
  clinicId
}) => {
  const [step, setStep] = useState(1);
  const shareDocumentsMutation = useShareAnalyticsDocuments();

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
    reset
  } = useForm<FormData>({
    defaultValues: {
      documentTypes: [],
      shareMode: ['email'],
      recipientType: 'client',
      includeExcelReport: true
    }
  });

  const watchedValues = watch();

  const handleClose = () => {
    reset();
    setStep(1);
    onClose();
  };

  const handleNext = () => {
    if (step === 1 && watchedValues.documentTypes.length === 0) {
      toast.error('Please select at least one document type');
      return;
    }
    if (step === 2 && watchedValues.shareMode.length === 0) {
      toast.error('Please select at least one sharing method');
      return;
    }
    setStep(step + 1);
  };

  const handleBack = () => {
    setStep(step - 1);
  };

  const onSubmit = async (data: FormData) => {
    try {
      await shareDocumentsMutation.mutateAsync({
        clinicId,
        startDate: timeRange.startDate,
        endDate: timeRange.endDate,
        documentTypes: data.documentTypes,
        shareMode: data.shareMode,
        recipientType: data.recipientType,
        email: data.email,
        phoneNumber: data.phoneNumber,
        includeExcelReport: data.includeExcelReport
      });

      toast.success('Document sharing request submitted successfully!');
      handleClose();
    } catch (error) {
      toast.error('Failed to submit sharing request');
    }
  };

  const renderStep1 = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Select Document Types</h3>
      <p className="text-sm text-gray-600">
        Choose which types of documents to include in your analytics report.
      </p>
      
      <div className="space-y-3">
        {documentTypeOptions.map((option) => (
          <Checkbox
            key={option.value}
            id={option.value}
            label={option.label}
            checked={watchedValues.documentTypes.includes(option.value)}
            onChange={(checked) => {
              const current = watchedValues.documentTypes;
              if (checked) {
                setValue('documentTypes', [...current, option.value]);
              } else {
                setValue('documentTypes', current.filter(type => type !== option.value));
              }
            }}
          />
        ))}
      </div>

      <div className="mt-6">
        <Checkbox
          id="includeExcelReport"
          label="Include Excel summary report"
          checked={watchedValues.includeExcelReport}
          onChange={(checked) => setValue('includeExcelReport', checked)}
        />
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Time Period Confirmation</h3>
      <div className="bg-gray-50 p-4 rounded-lg">
        <p className="text-sm">
          <strong>Selected Period:</strong> {timeRange.startDate} to {timeRange.endDate}
        </p>
        <p className="text-sm mt-2">
          <strong>Document Types:</strong> {watchedValues.documentTypes.join(', ')}
        </p>
      </div>

      <h4 className="text-md font-medium mt-6">How would you like to receive the documents?</h4>
      <div className="space-y-3">
        {shareModeOptions.map((option) => (
          <Checkbox
            key={option.value}
            id={option.value}
            label={option.label}
            checked={watchedValues.shareMode.includes(option.value)}
            onChange={(checked) => {
              const current = watchedValues.shareMode;
              if (checked) {
                setValue('shareMode', [...current, option.value]);
              } else {
                setValue('shareMode', current.filter(mode => mode !== option.value));
              }
            }}
          />
        ))}
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Recipient Details</h3>
      
      <RadioGroup
        options={recipientTypeOptions}
        value={watchedValues.recipientType}
        onChange={(value) => setValue('recipientType', value as 'client' | 'other')}
        name="recipientType"
      />

      {watchedValues.recipientType === 'other' && (
        <div className="space-y-4 mt-4">
          {watchedValues.shareMode.includes('email') && (
            <Input
              {...register('email', {
                required: watchedValues.shareMode.includes('email'),
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Invalid email address'
                }
              })}
              label="Email Address"
              type="email"
              placeholder="Enter email address"
              error={errors.email?.message}
            />
          )}

          {watchedValues.shareMode.includes('whatsapp') && (
            <Input
              {...register('phoneNumber', {
                required: watchedValues.shareMode.includes('whatsapp')
              })}
              label="Phone Number"
              type="tel"
              placeholder="Enter phone number with country code"
              error={errors.phoneNumber?.message}
            />
          )}
        </div>
      )}
    </div>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Share Analytics Documents"
      size="md"
    >
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {step === 1 && renderStep1()}
        {step === 2 && renderStep2()}
        {step === 3 && renderStep3()}

        <div className="flex justify-between pt-4 border-t">
          <div>
            {step > 1 && (
              <Button
                type="button"
                variant="secondary"
                onClick={handleBack}
              >
                Back
              </Button>
            )}
          </div>

          <div className="flex space-x-2">
            <Button
              type="button"
              variant="secondary"
              onClick={handleClose}
            >
              Cancel
            </Button>

            {step < 3 ? (
              <Button
                type="button"
                onClick={handleNext}
              >
                Next
              </Button>
            ) : (
              <Button
                type="submit"
                loading={shareDocumentsMutation.isPending}
              >
                Share Documents
              </Button>
            )}
          </div>
        </div>
      </form>
    </Modal>
  );
};

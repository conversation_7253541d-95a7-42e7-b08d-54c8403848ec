import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from './auth.service';
import { UsersService } from '../users/users.service';
import { JwtService } from '@nestjs/jwt';
import { RoleService } from '../roles/role.service';
import { NotFoundException, UnauthorizedException } from '@nestjs/common';
import { Role } from '../roles/role.enum';
import * as bcrypt from 'bcrypt';
import { UserOtpsService } from '../user-otps/user-otps.service';
import { User } from '../users/entities/user.entity';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { SESMailService } from '../utils/aws/ses/send-mail-service';
import { SessionService } from '../session/session.service';

jest.mock('bcrypt');

describe('AuthService', () => {
  let service: AuthService;
  let usersService: jest.Mocked<UsersService>;
  let jwtService: jest.Mocked<JwtService>;
  let roleService: jest.Mocked<RoleService>;
  let userOtpsService: jest.Mocked<UserOtpsService>;
  let usersRepository: jest.Mocked<Repository<User>>;
  let sesMailService: jest.Mocked<SESMailService>;
  let sessionService: jest.Mocked<SessionService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: UsersService,
          useValue: {
            createUser: jest.fn(),
            findOneByEmail: jest.fn(),
            findByRoleId: jest.fn(),
            findAll: jest.fn(),
            save: jest.fn(),
            findFirstByUserId: jest.fn(),
            generateUniquePin: jest.fn(),
            findByUserId: jest.fn(),
          }
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
          }
        },
        {
          provide: JwtService,
          useValue: {
            sign: jest.fn(),
          }
        },
        {
          provide: RoleService,
          useValue: {
            findByName: jest.fn(),
            findById: jest.fn(),
          }
        },
        {
          provide: UserOtpsService,
          useValue: {
            validateOtp: jest.fn(),
          }
        },
        {
          provide: SESMailService,
          useValue: {
            sendMail: jest.fn(),
          }
        },
        {
          provide: SessionService,
          useValue: {
            setUserSession: jest.fn(),
          }
        }
      ]
    }).compile();

    service = module.get<AuthService>(AuthService);
    usersService = module.get(UsersService) as jest.Mocked<UsersService>;
    jwtService = module.get(JwtService) as jest.Mocked<JwtService>;
    roleService = module.get(RoleService) as jest.Mocked<RoleService>;
    userOtpsService = module.get(UserOtpsService) as jest.Mocked<UserOtpsService>;
    usersRepository = module.get(getRepositoryToken(User)) as jest.Mocked<Repository<User>>;
    sesMailService = module.get(SESMailService) as jest.Mocked<SESMailService>;
    sessionService = module.get(SessionService) as jest.Mocked<SessionService>;
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('loginByEmail', () => {
    it('should call validateOtp from userOtpsService', async () => {
      const mockValidateOtpDto = {
        email: '<EMAIL>',
        otp: '123456'
      };
      const mockResponse = { token: 'mockToken', roleName: 'super_admin' };

      userOtpsService.validateOtp.mockResolvedValue(mockResponse);

      const result = await service.loginByEmail(mockValidateOtpDto);

      expect(userOtpsService.validateOtp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        otp: '123456'
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('verifyOtp', () => {
    it('should throw UnauthorizedException if user is not found', async () => {
      usersService.findOneByEmail.mockResolvedValue(null);

      await expect(
        service.verifyOtp({
          email: '<EMAIL>',
          otp: '123456'
        })
      ).rejects.toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException if user is not admin or super admin', async () => {
      usersService.findOneByEmail.mockResolvedValue({
        id: 'userId',
        roleId: 'staffRoleId'
      } as User);
      roleService.findById.mockResolvedValue({ name: Role.RECEPTIONIST } as any);

      await expect(
        service.verifyOtp({ email: '<EMAIL>', otp: '123456' })
      ).rejects.toThrow(UnauthorizedException);
    });

    it('should return access token for valid OTP', async () => {
      usersService.findOneByEmail.mockResolvedValue({
        id: 'userId',
        email: '<EMAIL>',
        roleId: 'adminRoleId'
      } as User);
      roleService.findById.mockResolvedValue({ name: Role.ADMIN } as any);
      jwtService.sign.mockReturnValue('mock_token');

      const result = await service.verifyOtp({
        email: '<EMAIL>',
        otp: '123456'
      });

      expect(result).toEqual({ access_token: 'mock_token' });
      expect(jwtService.sign).toHaveBeenCalledWith({
        email: '<EMAIL>',
        sub: 'userId',
        roleId: 'adminRoleId'
      });
    });
  });

  describe('loginPin', () => {
    const mockPinLoginDto = { pin: '1234', brandId: 'test-brand-id' };
    const mockUser = {
      id: 'user-id',
      pin: 'hashed-pin',
      roleId: 'role-id',
      registered: true,
      email: '<EMAIL>',
      firstName: 'John'
    } as User;
    const mockRole = { name: Role.RECEPTIONIST };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should throw UnauthorizedException with invalid_pin error code if PIN is invalid', async () => {
      usersService.findAll.mockResolvedValue([mockUser]);
      jest.spyOn(bcrypt, 'compare').mockResolvedValue(false as never);

      await expect(service.loginPin(mockPinLoginDto)).rejects.toThrow(
        expect.objectContaining({
          response: expect.objectContaining({
            error: 'invalid_pin',
            message: 'The PIN is invalid'
          })
        })
      );
    });

    it('should throw UnauthorizedException with brand_not_associated error code if user not associated with brand', async () => {
      usersService.findAll.mockResolvedValue([mockUser]);
      jest.spyOn(bcrypt, 'compare').mockResolvedValue(true as never);
      roleService.findById.mockResolvedValue(mockRole as any);
      
      // Mock clinic users but with different brandId
      usersService.findByUserId.mockResolvedValue([{
        id: 'clinic-user-id',
        brandId: 'different-brand-id',
        clinic: { id: 'clinic-id', name: 'Test Clinic', isActive: true, deletedAt: null },
        isOnboarded: true
      }] as any);

      await expect(service.loginPin(mockPinLoginDto)).rejects.toThrow(
        expect.objectContaining({
          response: expect.objectContaining({
            error: 'brand_not_associated',
            message: 'User not associated with the provided brand'
          })
        })
      );
    });

    it('should throw UnauthorizedException with clinic_deleted error code if clinic is soft deleted', async () => {
      usersService.findAll.mockResolvedValue([mockUser]);
      jest.spyOn(bcrypt, 'compare').mockResolvedValue(true as never);
      roleService.findById.mockResolvedValue(mockRole as any);
      
      // Mock clinic users with deleted clinic
      usersService.findByUserId.mockResolvedValue([{
        id: 'clinic-user-id',
        brandId: 'test-brand-id',
        clinic: { 
          id: 'clinic-id', 
          name: 'Test Clinic', 
          isActive: true, 
          deletedAt: new Date() // Clinic is soft deleted
        },
        isOnboarded: true
      }] as any);

      await expect(service.loginPin(mockPinLoginDto)).rejects.toThrow(
        expect.objectContaining({
          response: expect.objectContaining({
            error: 'clinic_deleted',
            message: 'This clinic is no longer available. Please contact your administrator.'
          })
        })
      );
    });

    it('should throw UnauthorizedException with clinic_inactive error code if clinic is inactive', async () => {
      usersService.findAll.mockResolvedValue([mockUser]);
      jest.spyOn(bcrypt, 'compare').mockResolvedValue(true as never);
      roleService.findById.mockResolvedValue(mockRole as any);
      
      // Mock clinic users with inactive clinic
      usersService.findByUserId.mockResolvedValue([{
        id: 'clinic-user-id',
        brandId: 'test-brand-id',
        clinic: { 
          id: 'clinic-id', 
          name: 'Test Clinic', 
          isActive: false, // Clinic is inactive
          deletedAt: null
        },
        isOnboarded: true
      }] as any);

      await expect(service.loginPin(mockPinLoginDto)).rejects.toThrow(
        expect.objectContaining({
          response: expect.objectContaining({
            error: 'clinic_inactive',
            message: 'This clinic is currently inactive. Please contact your administrator.'
          })
        })
      );
    });

    it('should return user data if PIN is valid and clinic is active', async () => {
      usersService.findAll.mockResolvedValue([mockUser]);
      jest.spyOn(bcrypt, 'compare').mockResolvedValue(true as never);
      roleService.findById.mockResolvedValue(mockRole as any);
      jwtService.sign.mockReturnValue('mock-jwt-token');
      sessionService.setUserSession.mockResolvedValue(undefined);
      
      // Mock valid clinic users
      const mockClinicUser = {
        id: 'clinic-user-id',
        brandId: 'test-brand-id',
        clinic: { 
          id: 'clinic-id', 
          name: 'Test Clinic', 
          isActive: true, 
          deletedAt: null,
          brand: { name: 'Test Brand' }
        },
        isOnboarded: true
      };
      usersService.findByUserId.mockResolvedValue([mockClinicUser] as any);

      const result = await service.loginPin(mockPinLoginDto);

      expect(result).toEqual(expect.objectContaining({
        token: 'mock-jwt-token',
        userId: 'clinic-user-id',
        isFirstLogin: false,
        isMultiClinic: false,
        isFullyOnboarded: true,
        clinicName: 'Test Clinic',
        clinicId: 'clinic-id',
        role: Role.RECEPTIONIST,
        brandId: 'test-brand-id',
        username: 'John',
        globalUserId: 'user-id',
        brandName: 'Test Brand'
      }));

      expect(sessionService.setUserSession).toHaveBeenCalledWith('user-id', expect.any(String), 60 * 60 * 24);
      expect(jwtService.sign).toHaveBeenCalledWith(expect.objectContaining({
        sub: 'user-id',
        email: '<EMAIL>',
        role: Role.RECEPTIONIST,
        clinicId: 'clinic-id',
        brandId: 'test-brand-id',
        sid: expect.any(String)
      }));
    });

    it('should handle multi-clinic scenario correctly', async () => {
      usersService.findAll.mockResolvedValue([mockUser]);
      jest.spyOn(bcrypt, 'compare').mockResolvedValue(true as never);
      roleService.findById.mockResolvedValue(mockRole as any);
      jwtService.sign.mockReturnValue('mock-jwt-token');
      sessionService.setUserSession.mockResolvedValue(undefined);
      
      // Mock multiple clinic users
      const mockClinicUsers = [
        {
          id: 'clinic-user-id-1',
          brandId: 'test-brand-id',
          clinic: { 
            id: 'clinic-id-1', 
            name: 'Test Clinic 1', 
            isActive: true, 
            deletedAt: null,
            brand: { name: 'Test Brand 1' }
          },
          isOnboarded: true
        },
        {
          id: 'clinic-user-id-2',
          brandId: 'another-brand-id',
          clinic: { 
            id: 'clinic-id-2', 
            name: 'Test Clinic 2', 
            isActive: true, 
            deletedAt: null,
            brand: { name: 'Test Brand 2' }
          },
          isOnboarded: true
        }
      ];
      usersService.findByUserId.mockResolvedValue(mockClinicUsers as any);

      const result = await service.loginPin(mockPinLoginDto);

      expect(result).toEqual(expect.objectContaining({
        isMultiClinic: true,
        clinicName: 'Test Clinic 1', // Should use the matching brandId clinic
        brandName: 'Test Brand 1'
      }));
    });
  });

  describe('resetPin', () => {
    it('should reset PIN and send email', async () => {
      const mockUser = { id: 'userId', email: '<EMAIL>' } as User;
      usersRepository.findOne.mockResolvedValue(mockUser);
      usersService.generateUniquePin.mockResolvedValue('1234');
      jest.spyOn(bcrypt, 'hash').mockImplementation(() => Promise.resolve('hashedPin'));

      await service.resetPin('<EMAIL>');

      expect(usersRepository.save).toHaveBeenCalledWith(expect.objectContaining({
        id: 'userId',
        pin: 'hashedPin'
      }));
      expect(sesMailService.sendMail).toHaveBeenCalled();
    });

    it('should throw NotFoundException if user is not found', async () => {
      usersRepository.findOne.mockResolvedValue(null);

      await expect(service.resetPin('<EMAIL>')).rejects.toThrow(NotFoundException);
    });
  });
});
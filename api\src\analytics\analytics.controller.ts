import {
	Controller,
	Get,
	Query,
	UseGuards,
	Res,
	ValidationPipe
} from '@nestjs/common';
import {
	ApiBearerAuth,
	ApiOperation,
	ApiTags,
	ApiResponse
} from '@nestjs/swagger';
import { Response } from 'express';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../roles/roles.decorator';
import { Role } from '../roles/role.enum';
import { AnalyticsService } from './analytics.service';
import { SendDocuments } from '../utils/common/send-document.service';
import {
	DownloadAnalyticsReportDto,
	GetRevenueChartDataDto,
	RevenueChartDataPoint,
	CollectedPaymentsChartDataPoint,
	GetAppointmentsChartDataDto,
	AppointmentsChartResponse,
	DoctorSummaryResponseDto,
	GetDoctorSummaryDto,
	SummaryResponseDto,
	GetSummaryDto
} from './dto/analytics.dto';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';

@ApiTags('Analytics')
@Controller('analytics')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class AnalyticsController {
	constructor(private readonly analyticsService: AnalyticsService) {}

	@Get('revenue-chart-data')
	@Roles(Role.ADMIN)
	@ApiOperation({ summary: 'Get revenue chart data' })
	@TrackMethod('get-revenue-chart-data')
	async getRevenueChartData(
		@Query(new ValidationPipe({ transform: true }))
		dto: GetRevenueChartDataDto
	): Promise<RevenueChartDataPoint[]> {
		return await this.analyticsService.getRevenueChartData(dto);
	}

	@Get('collected-payments-chart-data')
	@Roles(Role.ADMIN)
	@ApiOperation({ summary: 'Get collected payments chart data' })
	@TrackMethod('get-collected-payments-chart-data')
	async getCollectedPaymentsChartData(
		@Query(new ValidationPipe({ transform: true }))
		dto: GetRevenueChartDataDto
	): Promise<CollectedPaymentsChartDataPoint[]> {
		return await this.analyticsService.getCollectedPaymentsChartData(dto);
	}

	@Get('appointments-chart-data')
	@Roles(Role.ADMIN)
	@ApiOperation({ summary: 'Get appointments chart data' })
	@TrackMethod('get-appointments-chart-data')
	async getAppointmentsChartData(
		@Query(new ValidationPipe({ transform: true }))
		dto: GetAppointmentsChartDataDto
	): Promise<AppointmentsChartResponse> {
		return await this.analyticsService.getAppointmentsChartData(dto);
	}

	@Get('download-report')
	@Roles(Role.ADMIN)
	@ApiOperation({ summary: 'Download analytics report' })
	@TrackMethod('download-analytics-report')
	async downloadReport(
		@Query(new ValidationPipe({ transform: true }))
		dto: DownloadAnalyticsReportDto,
		@Res() res: Response
	): Promise<void> {
		const buffer = await this.analyticsService.generateReport(dto);

		res.set({
			'Content-Type':
				'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			'Content-Disposition': `attachment; filename="${dto.type}-report.xlsx"`,
			'Content-Length': buffer.length,
			'Cache-Control': 'no-cache'
		});

		res.end(buffer);
	}

	@Get('doctor-summary')
	@Roles(Role.ADMIN)
	@ApiOperation({ summary: 'Get doctor performance summary' })
	@TrackMethod('get-doctor-summary')
	async getDoctorSummary(
		@Query(new ValidationPipe({ transform: true }))
		dto: GetDoctorSummaryDto
	): Promise<DoctorSummaryResponseDto[]> {
		return await this.analyticsService.getDoctorSummary(dto);
	}

	@Get('summary')
	@Roles(Role.ADMIN)
	@ApiOperation({ summary: 'Get clinic performance summary' })
	@ApiResponse({
		status: 200,
		description: 'Returns summary data for clinic'
	})
	@TrackMethod('get-clinic-summary')
	async getSummary(
		@Query(new ValidationPipe({ transform: true })) dto: GetSummaryDto
	): Promise<SummaryResponseDto> {
		return await this.analyticsService.getSummary(dto);
	}
}

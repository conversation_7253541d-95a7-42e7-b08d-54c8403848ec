import * as yup from 'yup';
export const treatmentValidationSchema = yup.object().shape({
    subjective: yup
        .string()
        .required('Subjective is required')
        .max(500, 'subjective must not exceed 500 characters'),
    objective: yup.object().shape({
        physicalExam: yup.object().shape({
            oralTeeth: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(
                        250,
                        'oralTeeth - text should not exceed 50 characters'
                    ),
            }),
            eyes: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(250, 'eyes - text should not exceed 50 characters'),
            }),
            throat: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(250, 'throat - text should not exceed 50 characters'),
            }),
            respiratory: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(
                        250,
                        'respiratory - text should not exceed 50 characters'
                    ),
            }),
            musculoskeletal: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(
                        250,
                        'musculoskeletal - text should not exceed 50 characters'
                    ),
            }),
            urogenital: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(
                        250,
                        'urogenital - text should not exceed 50 characters'
                    ),
            }),
            mucous_membranes: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(
                        250,
                        'mucous_membranes - text should not exceed 50 characters'
                    ),
            }),
            ears: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(250, 'ears - text should not exceed 50 characters'),
            }),
            cardiovascular: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(
                        250,
                        'cardiovascular - text should not exceed 50 characters'
                    ),
            }),
            abdomen: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(250, 'abdomen - text should not exceed 50 characters'),
            }),
            glands: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(250, 'glands - text should not exceed 50 characters'),
            }),
            rectal: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(250, 'rectal - text should not exceed 50 characters'),
            }),
        }),

        ultrasoundExam: yup.object().shape({
            liver: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(
                        1000,
                        'liver - text should not exceed 1000 characters'
                    ),
            }),
            portalVein: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(
                        1000,
                        'portalVein - text should not exceed 1000 characters'
                    ),
            }),
            gallbladder: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(
                        1000,
                        'gallbladder - text should not exceed 1000 characters'
                    ),
            }),
            spleen: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(
                        1000,
                        'spleen - text should not exceed 1000 characters'
                    ),
            }),
            pancreas: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(
                        1000,
                        'pancreas - text should not exceed 1000 characters'
                    ),
            }),
            stomach: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(
                        1000,
                        'stomach - text should not exceed 1000 characters'
                    ),
            }),
            smallIntestines: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(
                        1000,
                        'smallIntestines - text should not exceed 1000 characters'
                    ),
            }),
            colon: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(
                        1000,
                        'colon - text should not exceed 1000 characters'
                    ),
            }),
            kidneys: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(
                        1000,
                        'kidneys - text should not exceed 1000 characters'
                    ),
            }),
            adrenalGlands: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(
                        1000,
                        'adrenalGlands - text should not exceed 1000 characters'
                    ),
            }),
            aorta: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(
                        1000,
                        'aorta - text should not exceed 1000 characters'
                    ),
            }),
            caudalVenaCava: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(
                        1000,
                        'caudalVenaCava - text should not exceed 1000 characters'
                    ),
            }),
            urinaryBladder: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(
                        1000,
                        'urinaryBladder - text should not exceed 1000 characters'
                    ),
            }),
            prostateGlandTestes: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(
                        1000,
                        'prostateGlandTestes - text should not exceed 1000 characters'
                    ),
            }),
            uterusOvaries: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(
                        1000,
                        'uterusOvaries - text should not exceed 1000 characters'
                    ),
            }),
            lymphNodes: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(
                        1000,
                        'lymphNodes - text should not exceed 1000 characters'
                    ),
            }),
            peritoneum: yup.object().shape({
                status: yup.string().default('Not Captured'),
                text: yup
                    .string()
                    .max(
                        1000,
                        'peritoneum - text should not exceed 1000 characters'
                    ),
            }),
        }),
        notes: yup
            .string()
            .max(500, 'notes - text should not exceed 200 characters'),
        vitals: yup.array(
            yup.object().shape({
                time: yup.string(),
                weight: yup.string(),
                temperature: yup.string(),
                heartRate: yup.string(),
                respRate: yup.string(),
                attitude: yup.string(),
                painScore: yup.string(),
                mucousMembrane: yup.string(),
                capillaryRefill: yup.string(),
                hydrationStatus: yup.string(),
                bcs: yup.string(),
                bp: yup.string(),
                map: yup.string(),
            })
        ),
        bodyMaps: yup.array(
            yup.object().shape({
                type: yup.string(),
                bodymapImage: yup.string(),
                notes: yup.string(),
                paths: yup.array(),
                image: yup.string(),
            })
        ),
        labReports: yup.array(
            yup.object().shape({
                value: yup.string(),
                label: yup.string(),
            })
        ),
    }),
    assessment: yup.object().shape({
        list: yup.array(
            yup.object().shape({
                value: yup.string(),
                label: yup.string(),
            })
        ),
        notes: yup
            .string()
            .max(
                500,
                'assessment notes - text should not exceed 500 characters'
            ),
        // bodyMaps: yup.array(
        //     yup.object().shape({
        //         type: yup.object().shape({
        //             value: yup.string(),
        //             label: yup.string(),
        //         }),
        //         notes: yup.string(),
        //         bodymapImage: yup.string(),
        //         paths: yup.string(),
        //         refs: yup.object(),
        //         image: yup.string(),
        //     })
        // ),
    }),
    plans: yup.object().shape({
        list: yup.array(
            yup.object().shape({
                value: yup.string(),
                label: yup.string(),
                // dosage: yup.string(),
                // frequency: yup.string(),
                // duration: yup.string(),
                quantity: yup.number(),
            })
        ),
        notes: yup
            .string()
            .max(500, 'plans notes - text should not exceed 500 characters'),
    }),
    prescription: yup.object().shape({
        list: yup.array(
            yup.object().shape({
                value: yup.string(),
                label: yup.string(),
                comment: yup.string(),
                isRestricted: yup.boolean(),
                isLongTerm: yup.boolean(),
            })
        ),
        notes: yup
            .string()
            .max(
                500,
                'prescription notes - text should not exceed 200 characters'
            ),
    }),
    followup: yup
        .object()
        .shape({
            label: yup.string(),
            type: yup.string(),
        })
        .nullable(),
    attachments: yup.object().shape({
        list: yup.array(
            yup.object().shape({
                attachementName: yup.string(),
                fileName: yup.string(),
                fileKey: yup.string(),
            })
        ),
    }),
    invoiceAmount: yup.number(),
});

import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Logger } from '@nestjs/common';
import { AnalyticsService } from './analytics.service';
import { InvoiceEntity } from '../invoice/entities/invoice.entity';
import { PaymentDetailsEntity } from '../payment-details/entities/payment-details.entity';
import { OwnerBrand } from '../owners/entities/owner-brand.entity';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { ClinicEntity } from '../clinics/entities/clinic.entity';
import {
	GetRevenueChartDataDto,
	DownloadAnalyticsReportDto,
	GetAppointmentsChartDataDto,
	GetDoctorSummaryDto,
	GetSummaryDto,
	AnalyticsType,
	AnalyticsReportType,
	AppointmentAnalyticsType,
	AnalyticsTimeFrame
} from './dto/analytics.dto';

describe('AnalyticsService', () => {
	let service: AnalyticsService;
	let invoiceRepository: Repository<InvoiceEntity>;
	let paymentDetailsRepository: Repository<PaymentDetailsEntity>;

	let ownerBrandRepository: Repository<OwnerBrand>;
	let appointmentRepository: Repository<AppointmentEntity>;

	const mockRepository = {
		query: jest.fn(),
		find: jest.fn(),
		findOne: jest.fn(),
		create: jest.fn(),
		save: jest.fn(),
		update: jest.fn(),
		delete: jest.fn(),
		createQueryBuilder: jest.fn(() => ({
			select: jest.fn().mockReturnThis(),
			from: jest.fn().mockReturnThis(),
			where: jest.fn().mockReturnThis(),
			andWhere: jest.fn().mockReturnThis(),
			groupBy: jest.fn().mockReturnThis(),
			orderBy: jest.fn().mockReturnThis(),
			getRawMany: jest.fn(),
			getRawOne: jest.fn(),
			getMany: jest.fn(),
			getOne: jest.fn()
		}))
	};

	const mockLogger = {
		log: jest.fn(),
		error: jest.fn(),
		warn: jest.fn(),
		debug: jest.fn(),
		verbose: jest.fn()
	};

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				AnalyticsService,
				{
					provide: getRepositoryToken(InvoiceEntity),
					useValue: mockRepository
				},
				{
					provide: getRepositoryToken(PaymentDetailsEntity),
					useValue: mockRepository
				},
				{
					provide: getRepositoryToken(OwnerBrand),
					useValue: mockRepository
				},
				{
					provide: getRepositoryToken(AppointmentEntity),
					useValue: mockRepository
				},
				{
					provide: getRepositoryToken(ClinicEntity),
					useValue: mockRepository
				},
				{
					provide: Logger,
					useValue: mockLogger
				}
			]
		}).compile();

		service = module.get<AnalyticsService>(AnalyticsService);
		invoiceRepository = module.get<Repository<InvoiceEntity>>(
			getRepositoryToken(InvoiceEntity)
		);
		paymentDetailsRepository = module.get<Repository<PaymentDetailsEntity>>(
			getRepositoryToken(PaymentDetailsEntity)
		);

		ownerBrandRepository = module.get<Repository<OwnerBrand>>(
			getRepositoryToken(OwnerBrand)
		);
		appointmentRepository = module.get<Repository<AppointmentEntity>>(
			getRepositoryToken(AppointmentEntity)
		);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it('should be defined', () => {
		expect(service).toBeDefined();
	});

	describe('getRevenueChartData', () => {
		it('should return revenue chart data for 1D timeframe', async () => {
			const dto: GetRevenueChartDataDto = {
				startDate: '2023-01-01',
				endDate: '2023-01-01',
				clinicId: 'clinic-123'
			};

			const mockQueryResult = [
				{
					date: '2023-01-01',
					products: '100',
					services: '200',
					diagnostics: '50',
					medications: '75',
					vaccinations: '25'
				}
			];

			mockRepository.query.mockResolvedValue(mockQueryResult);

			const result = await service.getRevenueChartData(dto);

			expect(result).toEqual([
				{
					date: '2023-01-01',
					products: 100,
					services: 200,
					diagnostics: 50,
					medications: 75,
					vaccinations: 25
				}
			]);
			expect(mockRepository.query).toHaveBeenCalled();
		});

		it('should return revenue chart data for 1W timeframe', async () => {
			const dto: GetRevenueChartDataDto = {
				startDate: '2023-01-01',
				endDate: '2023-01-07',
				clinicId: 'clinic-123'
			};

			const mockQueryResult = [
				{
					date: '2023-01-01',
					products: '100',
					services: '200',
					diagnostics: '50',
					medications: '75',
					vaccinations: '25'
				}
			];

			mockRepository.query.mockResolvedValue(mockQueryResult);

			const result = await service.getRevenueChartData(dto);

			expect(result).toHaveLength(1);
			expect(result[0].date).toBe('2023-01-01');
			expect(mockRepository.query).toHaveBeenCalled();
		});

		it('should return revenue chart data for 1M timeframe', async () => {
			const dto: GetRevenueChartDataDto = {
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123'
			};

			const mockQueryResult = [
				{
					date: '2023-01-01',
					products: '100',
					services: '200',
					diagnostics: '50',
					medications: '75',
					vaccinations: '25'
				}
			];

			mockRepository.query.mockResolvedValue(mockQueryResult);

			const result = await service.getRevenueChartData(dto);

			expect(result).toHaveLength(1);
			expect(result[0].date).toBe('2023-01-01');
			expect(mockRepository.query).toHaveBeenCalled();
		});

		it('should return revenue chart data for 1Y timeframe', async () => {
			const dto: GetRevenueChartDataDto = {
				startDate: '2023-01-01',
				endDate: '2023-12-31',
				clinicId: 'clinic-123'
			};

			const mockQueryResult = [
				{
					date: '2023-01-01',
					products: '100',
					services: '200',
					diagnostics: '50',
					medications: '75',
					vaccinations: '25'
				}
			];

			mockRepository.query.mockResolvedValue(mockQueryResult);

			const result = await service.getRevenueChartData(dto);

			expect(result).toHaveLength(1);
			expect(result[0].date).toBe('2023-01-01');
			expect(mockRepository.query).toHaveBeenCalled();
		});

		it('should handle empty query results', async () => {
			const dto: GetRevenueChartDataDto = {
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123'
			};

			mockRepository.query.mockResolvedValue([]);

			const result = await service.getRevenueChartData(dto);

			expect(result).toEqual([]);
			expect(mockRepository.query).toHaveBeenCalled();
		});

		it('should handle query errors', async () => {
			const dto: GetRevenueChartDataDto = {
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123'
			};

			const error = new Error('Database error');
			mockRepository.query.mockRejectedValue(error);

			await expect(service.getRevenueChartData(dto)).rejects.toThrow(
				'Database error'
			);
		});
	});

	describe('getCollectedPaymentsChartData', () => {
		it('should return collected payments chart data', async () => {
			const dto: GetRevenueChartDataDto = {
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123'
			};

			const mockQueryResult = [
				{
					date: '2023-01-01',
					cash: '100',
					card: '200',
					wallet: '50',
					cheque: '75',
					banktransfer: '25'
				}
			];

			mockRepository.query.mockResolvedValue(mockQueryResult);

			const result = await service.getCollectedPaymentsChartData(dto);

			expect(result).toEqual([
				{
					date: '2023-01-01',
					cash: 100,
					card: 200,
					wallet: 50,
					cheque: 75,
					bankTransfer: 25
				}
			]);
			expect(mockRepository.query).toHaveBeenCalled();
		});

		it('should handle empty query results', async () => {
			const dto: GetRevenueChartDataDto = {
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123'
			};

			mockRepository.query.mockResolvedValue([]);

			const result = await service.getCollectedPaymentsChartData(dto);

			expect(result).toEqual([]);
			expect(mockRepository.query).toHaveBeenCalled();
		});
	});

	describe('getAppointmentsChartData', () => {
		it('should return appointments chart data for ALL type', async () => {
			const dto: GetAppointmentsChartDataDto = {
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123',
				type: AppointmentAnalyticsType.ALL
			};

			const mockQueryResult = [
				{
					date: '2023-01-01',
					total: '10',
					missed: '2'
				}
			];

			mockRepository.query.mockResolvedValue(mockQueryResult);

			const result = await service.getAppointmentsChartData(dto);

			expect(result.total).toEqual([
				{
					date: '2023-01-01',
					total: 10
				}
			]);
			expect(result.missed).toEqual([
				{
					date: '2023-01-01',
					missed: 2
				}
			]);
			expect(mockRepository.query).toHaveBeenCalled();
		});

		it('should return busiest days data', async () => {
			const dto: GetAppointmentsChartDataDto = {
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123',
				type: AppointmentAnalyticsType.BUSIEST_DAYS
			};

			const mockQueryResult = [
				{
					day: 'Monday',
					count: '15',
					weeks_counted: '4',
					total: '60'
				}
			];

			mockRepository.query.mockResolvedValue(mockQueryResult);

			const result = await service.getAppointmentsChartData(dto);

			expect(result.busiestDays).toEqual([
				{
					day: 'Monday',
					count: 15,
					weeksCount: 4,
					total: 60
				}
			]);
			expect(mockRepository.query).toHaveBeenCalled();
		});

		it('should return busiest hours data', async () => {
			const dto: GetAppointmentsChartDataDto = {
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123',
				type: AppointmentAnalyticsType.BUSIEST_HOURS
			};

			const mockQueryResult = [
				{
					hour: 10,
					count: '5',
					days_count: '20',
					total: '100'
				}
			];

			mockRepository.query.mockResolvedValue(mockQueryResult);

			const result = await service.getAppointmentsChartData(dto);

			expect(result.busiestHours).toEqual([
				{
					hour: '10',
					count: 5,
					daysCount: 20,
					total: 100
				}
			]);
			expect(mockRepository.query).toHaveBeenCalled();
		});

		it('should return average duration data', async () => {
			const dto: GetAppointmentsChartDataDto = {
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123',
				type: AppointmentAnalyticsType.AVERAGE_DURATION
			};

			const mockQueryResult = [
				{
					date: '2023-01-01',
					checkinduration: '5',
					receivingcareduration: '20',
					checkoutduration: '5',
					totalduration: '30',
					validappointments: '10'
				}
			];

			mockRepository.query.mockResolvedValue(mockQueryResult);

			const result = await service.getAppointmentsChartData(dto);

			expect(result.averageDuration).toEqual([
				{
					date: '2023-01-01',
					checkinDuration: 5,
					receivingCareDuration: 20,
					checkoutDuration: 5,
					totalDuration: 30,
					validAppointments: 10
				}
			]);
			expect(mockRepository.query).toHaveBeenCalled();
		});
	});

	describe('getDoctorSummary', () => {
		it('should return doctor summary data', async () => {
			const dto: GetDoctorSummaryDto = {
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123'
			};

			const mockQueryResult = [
				{
					doctor_name: 'Dr. Smith',
					num_appointments: '50',
					total_revenue: '5000',
					revenue_per_appointment: '100',
					avg_appointment_duration_minutes: '30'
				}
			];

			mockRepository.query.mockResolvedValue(mockQueryResult);

			const result = await service.getDoctorSummary(dto);

			expect(result).toEqual([
				{
					doctorName: 'Dr. Smith',
					numAppointments: 50,
					totalRevenue: 5000,
					revenuePerAppointment: 100,
					avgAppointmentDurationMinutes: 30
				}
			]);
			expect(mockRepository.query).toHaveBeenCalled();
		});

		it('should handle empty query results', async () => {
			const dto: GetDoctorSummaryDto = {
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123'
			};

			mockRepository.query.mockResolvedValue([]);

			const result = await service.getDoctorSummary(dto);

			expect(result).toEqual([]);
			expect(mockRepository.query).toHaveBeenCalled();
		});
	});

	describe('getSummary', () => {
		it('should return summary data', async () => {
			const dto: GetSummaryDto = {
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123'
			};

			// Mock multiple query results for different parts of the summary
			// The service makes 8 queries in this order:
			// 1. appointments completed (appointmentRepository)
			// 2. invoices generated (invoiceRepository)
			// 3. total billing (invoiceRepository)
			// 4. credit notes generated (invoiceRepository)
			// 5. total credit notes (invoiceRepository)
			// 6. collected payments (paymentDetailsRepository)
			// 7. refunded payments (paymentDetailsRepository)
			// 8. bad debts (paymentDetailsRepository)
			mockRepository.query
				.mockResolvedValueOnce([{ count: '100' }]) // 1. appointments completed
				.mockResolvedValueOnce([{ count: '95' }]) // 2. invoices generated
				.mockResolvedValueOnce([{ total: '10000' }]) // 3. total billing
				.mockResolvedValueOnce([{ count: '5' }]) // 4. credit notes generated
				.mockResolvedValueOnce([{ total: '500' }]) // 5. total credit notes
				.mockResolvedValueOnce([
					// 6. collected payments
					{ payment_mode: 'cash', total: '2000' },
					{ payment_mode: 'card', total: '3000' },
					{ payment_mode: 'wallet', total: '1000' },
					{ payment_mode: 'cheque', total: '500' },
					{ payment_mode: 'bank transfer', total: '1500' }
				])
				.mockResolvedValueOnce([
					// 7. refunded payments
					{ payment_mode: 'cash', total: '100' },
					{ payment_mode: 'card', total: '200' },
					{ payment_mode: 'wallet', total: '50' },
					{ payment_mode: 'cheque', total: '25' },
					{ payment_mode: 'bank transfer', total: '75' }
				])
				.mockResolvedValueOnce([{ total: '1000' }]); // 8. bad debts

			const result = await service.getSummary(dto);

			expect(result).toEqual({
				appointmentsCompleted: 100,
				invoicesGenerated: 95,
				totalBilling: 10000,
				creditNotesGenerated: 5,
				totalCreditNotes: 500,
				badDebts: 1000,
				amountCollected: {
					cash: 2000,
					card: 3000,
					wallet: 1000,
					cheque: 500,
					bankTransfer: 1500,
					total: 8000
				},
				amountRefunded: {
					cash: 100,
					card: 200,
					wallet: 50,
					cheque: 25,
					bankTransfer: 75,
					total: 450
				}
			});
			expect(mockRepository.query).toHaveBeenCalledTimes(8);
		});

		it('should handle missing payment modes in collected payments', async () => {
			const dto: GetSummaryDto = {
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123'
			};

			mockRepository.query
				.mockResolvedValueOnce([{ count: '100' }]) // 1. appointments completed
				.mockResolvedValueOnce([{ count: '95' }]) // 2. invoices generated
				.mockResolvedValueOnce([{ total: '10000' }]) // 3. total billing
				.mockResolvedValueOnce([{ count: '5' }]) // 4. credit notes generated
				.mockResolvedValueOnce([{ total: '500' }]) // 5. total credit notes
				.mockResolvedValueOnce([
					// 6. collected payments - only cash payments
					{ payment_mode: 'cash', total: '2000' }
				])
				.mockResolvedValueOnce([]) // 7. refunded payments - empty
				.mockResolvedValueOnce([{ total: '0' }]); // 8. bad debts

			const result = await service.getSummary(dto);

			expect(result.amountCollected).toEqual({
				cash: 2000,
				card: 0,
				wallet: 0,
				cheque: 0,
				bankTransfer: 0,
				total: 2000
			});
			expect(result.amountRefunded).toEqual({
				cash: 0,
				card: 0,
				wallet: 0,
				cheque: 0,
				bankTransfer: 0,
				total: 0
			});
		});
	});

	describe('generateReport', () => {
		it('should generate revenue report by billing', async () => {
			const dto: DownloadAnalyticsReportDto = {
				type: AnalyticsType.REVENUE,
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123',
				reportType: AnalyticsReportType.BY_BILLING
			};

			// Mock clinic info query
			mockRepository.query.mockResolvedValueOnce([
				{
					name: 'Test Clinic',
					addressLine1: '123 Main St',
					addressLine2: 'Suite 100',
					city: 'Test City',
					state: 'Test State',
					country: 'Test Country',
					pincode: '12345'
				}
			]);

			// Mock billing data queries
			mockRepository.query
				.mockResolvedValueOnce([]) // products
				.mockResolvedValueOnce([]) // services
				.mockResolvedValueOnce([]) // vaccinations
				.mockResolvedValueOnce([]) // medications
				.mockResolvedValueOnce([]); // lab reports

			const result = await service.generateReport(dto);

			expect(result).toBeInstanceOf(Buffer);
			expect(mockRepository.query).toHaveBeenCalled();
		});

		it('should generate revenue report by patient', async () => {
			const dto: DownloadAnalyticsReportDto = {
				type: AnalyticsType.REVENUE,
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123',
				reportType: AnalyticsReportType.BY_PATIENT
			};

			// Mock clinic info query
			mockRepository.query.mockResolvedValueOnce([
				{
					name: 'Test Clinic',
					addressLine1: '123 Main St',
					addressLine2: 'Suite 100',
					city: 'Test City',
					state: 'Test State',
					country: 'Test Country',
					pincode: '12345'
				}
			]);

			// Mock patient billing data query
			mockRepository.query.mockResolvedValueOnce([]);

			const result = await service.generateReport(dto);

			expect(result).toBeInstanceOf(Buffer);
			expect(mockRepository.query).toHaveBeenCalled();
		});

		it('should generate appointments report', async () => {
			const dto: DownloadAnalyticsReportDto = {
				type: AnalyticsType.APPOINTMENTS,
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123'
			};

			// Mock clinic info query
			mockRepository.query.mockResolvedValueOnce([
				{
					name: 'Test Clinic',
					addressLine1: '123 Main St',
					addressLine2: 'Suite 100',
					city: 'Test City',
					state: 'Test State',
					country: 'Test Country',
					pincode: '12345'
				}
			]);

			// Mock appointment data query
			mockRepository.query.mockResolvedValueOnce([]);

			const result = await service.generateReport(dto);

			expect(result).toBeInstanceOf(Buffer);
			expect(mockRepository.query).toHaveBeenCalled();
		});

		it('should generate doctor performance report', async () => {
			const dto: DownloadAnalyticsReportDto = {
				type: AnalyticsType.DOCTOR_PERFORMANCE,
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123'
			};

			// Mock clinic info query
			mockRepository.query.mockResolvedValueOnce([
				{
					name: 'Test Clinic',
					addressLine1: '123 Main St',
					addressLine2: 'Suite 100',
					city: 'Test City',
					state: 'Test State',
					country: 'Test Country',
					pincode: '12345'
				}
			]);

			// Mock doctor summary data query
			mockRepository.query.mockResolvedValueOnce([]);

			const result = await service.generateReport(dto);

			expect(result).toBeInstanceOf(Buffer);
			expect(mockRepository.query).toHaveBeenCalled();
		});

		it('should generate outstanding balance report', async () => {
			const dto: DownloadAnalyticsReportDto = {
				type: AnalyticsType.OUTSTANDING_BALANCE,
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123'
			};

			// Mock clinic info query
			mockRepository.query.mockResolvedValueOnce([
				{
					name: 'Test Clinic',
					addressLine1: '123 Main St',
					addressLine2: 'Suite 100',
					city: 'Test City',
					state: 'Test State',
					country: 'Test Country',
					pincode: '12345'
				}
			]);

			// Mock owner summary data query
			mockRepository.query.mockResolvedValueOnce([]);

			const result = await service.generateReport(dto);

			expect(result).toBeInstanceOf(Buffer);
			expect(mockRepository.query).toHaveBeenCalled();
		});

		it('should generate collected payments report', async () => {
			const dto: DownloadAnalyticsReportDto = {
				type: AnalyticsType.COLLECTED_PAYMENTS,
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123'
			};

			// Mock clinic info query
			mockRepository.query.mockResolvedValueOnce([
				{
					name: 'Test Clinic',
					addressLine1: '123 Main St',
					addressLine2: 'Suite 100',
					city: 'Test City',
					state: 'Test State',
					country: 'Test Country',
					pincode: '12345'
				}
			]);

			// Mock payment summary stats
			mockRepository.query
				.mockResolvedValueOnce([]) // collected payments
				.mockResolvedValueOnce([]) // returned payments
				.mockResolvedValueOnce([]) // all payments by mode
				.mockResolvedValueOnce([]); // all returned payments by mode

			const result = await service.generateReport(dto);

			expect(result).toBeInstanceOf(Buffer);
			expect(mockRepository.query).toHaveBeenCalled();
		});

		it('should handle report generation errors', async () => {
			const dto: DownloadAnalyticsReportDto = {
				type: AnalyticsType.REVENUE,
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123'
			};

			const error = new Error('Database error');
			mockRepository.query.mockRejectedValue(error);

			await expect(service.generateReport(dto)).rejects.toThrow(
				'Workbook is empty'
			);
		});
	});

	describe('Private Methods Coverage', () => {
		// Note: _determineTimeFrame method doesn't exist in the service
		// The timeframe logic is implemented inline in each method
		// These tests were removed as they test a non-existent method

		describe('cleanDataForExcel', () => {
			it('should clean data for Excel export', () => {
				const data = [
					{ name: 'Test', value: null, amount: 100 },
					{ name: 'Test2', value: undefined, amount: 200 }
				];

				const cleanedData = (service as any).cleanDataForExcel(data);

				expect(cleanedData).toEqual([
					{ name: 'Test', value: '', amount: 100 },
					{ name: 'Test2', value: '', amount: 200 }
				]);
			});

			it('should handle empty data array', () => {
				const data: any[] = [];
				const cleanedData = (service as any).cleanDataForExcel(data);
				expect(cleanedData).toEqual([]);
			});
		});

		describe('getClinicInfo', () => {
			it('should return clinic information', async () => {
				const mockClinicInfo = {
					name: 'Test Clinic',
					addressLine1: '123 Main St',
					addressLine2: 'Suite 100',
					city: 'Test City',
					state: 'Test State',
					country: 'Test Country',
					pincode: '12345'
				};

				// Reset the mock completely and set up new implementation
				mockRepository.query.mockReset();
				mockRepository.query.mockResolvedValue([mockClinicInfo]);

				const result = await (service as any).getClinicInfo(
					'clinic-123'
				);

				expect(result).toEqual(mockClinicInfo);
				expect(mockRepository.query).toHaveBeenCalledWith(
					expect.stringContaining('SELECT'),
					['clinic-123']
				);
			});

			it('should handle empty query results', async () => {
				// Reset the mock completely and set up new implementation
				mockRepository.query.mockReset();
				mockRepository.query.mockResolvedValue([]);

				const result = await (service as any).getClinicInfo(
					'clinic-123'
				);

				expect(result).toBeUndefined();
				expect(mockRepository.query).toHaveBeenCalledWith(
					expect.stringContaining('SELECT'),
					['clinic-123']
				);
			});

			it('should handle database errors', async () => {
				// Reset the mock completely and set up new implementation
				mockRepository.query.mockReset();
				const error = new Error('Database connection failed');
				mockRepository.query.mockRejectedValue(error);

				await expect(
					(service as any).getClinicInfo('clinic-123')
				).rejects.toThrow('Database connection failed');
				expect(mockRepository.query).toHaveBeenCalledWith(
					expect.stringContaining('SELECT'),
					['clinic-123']
				);
			});
		});

		describe('Error Handling', () => {
			it('should handle database connection errors', async () => {
				const dto: GetRevenueChartDataDto = {
					startDate: '2023-01-01',
					endDate: '2023-01-31',
					clinicId: 'clinic-123'
				};

				const connectionError = new Error('Connection failed');
				mockRepository.query.mockRejectedValue(connectionError);

				await expect(service.getRevenueChartData(dto)).rejects.toThrow(
					'Connection failed'
				);
			});

			it('should handle SQL syntax errors', async () => {
				const dto: GetDoctorSummaryDto = {
					startDate: '2023-01-01',
					endDate: '2023-01-31',
					clinicId: 'clinic-123'
				};

				const sqlError = new Error('SQL syntax error');
				mockRepository.query.mockRejectedValue(sqlError);

				await expect(service.getDoctorSummary(dto)).rejects.toThrow(
					'SQL syntax error'
				);
			});
		});

		describe('Data Transformation', () => {
			it('should handle string to number conversion in revenue data', async () => {
				const dto: GetRevenueChartDataDto = {
					startDate: '2023-01-01',
					endDate: '2023-01-31',
					clinicId: 'clinic-123'
				};

				const mockQueryResult = [
					{
						date: '2023-01-01',
						products: '100.50',
						services: '200.75',
						diagnostics: '50.25',
						medications: '75.00',
						vaccinations: '25.99'
					}
				];

				mockRepository.query.mockResolvedValue(mockQueryResult);

				const result = await service.getRevenueChartData(dto);

				expect(result[0].products).toBe(100.5);
				expect(result[0].services).toBe(200.75);
				expect(result[0].diagnostics).toBe(50.25);
				expect(result[0].medications).toBe(75);
				expect(result[0].vaccinations).toBe(25.99);
			});

			it('should handle null values in query results', async () => {
				const dto: GetRevenueChartDataDto = {
					startDate: '2023-01-01',
					endDate: '2023-01-31',
					clinicId: 'clinic-123'
				};

				const mockQueryResult = [
					{
						date: '2023-01-01',
						products: null,
						services: '200',
						diagnostics: undefined,
						medications: '75',
						vaccinations: '25'
					}
				];

				mockRepository.query.mockResolvedValue(mockQueryResult);

				const result = await service.getRevenueChartData(dto);

				expect(result[0].products).toBe(0); // Number(null || 0) = 0
				expect(result[0].services).toBe(200);
				expect(result[0].diagnostics).toBe(0); // Number(undefined || 0) = 0
				expect(result[0].medications).toBe(75);
				expect(result[0].vaccinations).toBe(25);
			});
		});

		describe('Date Handling', () => {
			it('should handle different date formats', async () => {
				const dto: GetRevenueChartDataDto = {
					startDate: '2023-01-01T00:00:00.000Z',
					endDate: '2023-01-31T23:59:59.999Z',
					clinicId: 'clinic-123'
				};

				mockRepository.query.mockResolvedValue([]);

				await service.getRevenueChartData(dto);

				expect(mockRepository.query).toHaveBeenCalled();
			});

			it('should handle invalid date ranges gracefully', async () => {
				const dto: GetRevenueChartDataDto = {
					startDate: '2023-01-31',
					endDate: '2023-01-01', // End date before start date
					clinicId: 'clinic-123'
				};

				mockRepository.query.mockResolvedValue([]);

				const result = await service.getRevenueChartData(dto);

				expect(result).toEqual([]);
			});
		});

		describe('getCollectedPaymentsChartData', () => {
			it('should handle empty query results', async () => {
				const dto: GetRevenueChartDataDto = {
					startDate: '2023-01-01',
					endDate: '2023-01-31',
					clinicId: 'clinic-123'
				};

				mockRepository.query.mockReset();
				mockRepository.query.mockResolvedValue([]);

				const result = await service.getCollectedPaymentsChartData(dto);

				expect(result).toEqual([]);
				expect(mockRepository.query).toHaveBeenCalled();
			});

			it('should handle null and undefined payment mode values', async () => {
				const dto: GetRevenueChartDataDto = {
					startDate: '2023-01-01',
					endDate: '2023-01-31',
					clinicId: 'clinic-123'
				};

				const mockQueryResult = [
					{
						date: '2023-01-01',
						cash: '100',
						card: null,
						wallet: '50',
						cheque: undefined,
						banktransfer: '75' // Note: PostgreSQL returns lowercase field names
					}
				];

				mockRepository.query.mockReset();
				mockRepository.query.mockResolvedValue(mockQueryResult);

				const result = await service.getCollectedPaymentsChartData(dto);

				expect(result).toEqual([
					{
						date: '2023-01-01',
						cash: 100,
						card: 0, // Number(null) || 0 = 0
						wallet: 50,
						cheque: 0, // Number(undefined) || 0 = 0
						bankTransfer: 75
					}
				]);
			});
		});

		describe('getAppointmentsChartData - Edge Cases', () => {
			it('should handle edge case with zero appointments in busiest hours', async () => {
				const dto: GetAppointmentsChartDataDto = {
					startDate: '2023-01-01',
					endDate: '2023-01-31',
					clinicId: 'clinic-123',
					type: AppointmentAnalyticsType.BUSIEST_HOURS
				};

				const mockQueryResult = [
					{
						hour: 9,
						count: '0',
						days_count: '0',
						total: '0'
					}
				];

				mockRepository.query.mockReset();
				mockRepository.query.mockResolvedValue(mockQueryResult);

				const result = await service.getAppointmentsChartData(dto);

				expect(result.busiestHours).toEqual([
					{
						hour: '9', // The service returns the hour as string without zero padding for single digits
						count: 0,
						daysCount: 0,
						total: 0
					}
				]);
			});

			it('should handle edge case with zero duration in average duration', async () => {
				const dto: GetAppointmentsChartDataDto = {
					startDate: '2023-01-01',
					endDate: '2023-01-31',
					clinicId: 'clinic-123',
					type: AppointmentAnalyticsType.AVERAGE_DURATION
				};

				const mockQueryResult = [
					{
						date: '2023-01-01',
						checkinduration: '0',
						receivingcareduration: '0',
						checkoutduration: '0',
						totalduration: '0',
						validappointments: '0'
					}
				];

				mockRepository.query.mockReset();
				mockRepository.query.mockResolvedValue(mockQueryResult);

				const result = await service.getAppointmentsChartData(dto);

				expect(result.averageDuration).toEqual([
					{
						date: '2023-01-01',
						checkinDuration: 0,
						receivingCareDuration: 0,
						checkoutDuration: 0,
						totalDuration: 0,
						validAppointments: 0
					}
				]);
			});
		});

		describe('getDoctorSummary - Edge Cases', () => {
			it('should handle null values in query results', async () => {
				const dto: GetDoctorSummaryDto = {
					startDate: '2023-01-01',
					endDate: '2023-01-31',
					clinicId: 'clinic-123'
				};

				const mockQueryResult = [
					{
						doctor_name: 'Dr. Smith',
						num_appointments: null,
						total_revenue: '5000',
						revenue_per_appointment: null,
						avg_appointment_duration_minutes: '30'
					}
				];

				mockRepository.query.mockReset();
				mockRepository.query.mockResolvedValue(mockQueryResult);

				const result = await service.getDoctorSummary(dto);

				expect(result).toEqual([
					{
						doctorName: 'Dr. Smith',
						numAppointments: 0,
						totalRevenue: 5000,
						revenuePerAppointment: 0,
						avgAppointmentDurationMinutes: 30
					}
				]);
			});
		});

		describe('getSummary - Edge Cases', () => {
			it('should handle missing payment modes in collected and refunded payments', async () => {
				const dto: GetSummaryDto = {
					startDate: '2023-01-01',
					endDate: '2023-01-31',
					clinicId: 'clinic-123'
				};

				mockRepository.query.mockReset();
				mockRepository.query
					.mockResolvedValueOnce([{ count: '100' }]) // appointments completed
					.mockResolvedValueOnce([{ count: '95' }]) // invoices generated
					.mockResolvedValueOnce([{ total: '10000' }]) // total billing
					.mockResolvedValueOnce([{ count: '5' }]) // credit notes generated
					.mockResolvedValueOnce([{ total: '500' }]) // total credit notes
					.mockResolvedValueOnce([
						// collected payments - only cash payments
						{ payment_mode: 'cash', total: '2000' }
					])
					.mockResolvedValueOnce([
						// refunded payments - only card refunds
						{ payment_mode: 'card', total: '100' }
					])
					.mockResolvedValueOnce([{ total: '1000' }]); // bad debts

				const result = await service.getSummary(dto);

				expect(result.amountCollected).toEqual({
					cash: 2000,
					card: 0,
					wallet: 0,
					cheque: 0,
					bankTransfer: 0,
					total: 2000
				});

				expect(result.amountRefunded).toEqual({
					cash: 0,
					card: 100,
					wallet: 0,
					cheque: 0,
					bankTransfer: 0,
					total: 100
				});
			});
		});

		describe('Excel Generation Methods', () => {
			describe('createDataSheet', () => {
				it('should handle empty data array', async () => {
					const result = await (service as any).createDataSheet([]);

					expect(result).toBeDefined();
					// The method should return a sheet with "No data available" message
				});

				it('should handle null data', async () => {
					const result = await (service as any).createDataSheet(null);

					expect(result).toBeDefined();
				});

				it('should create sheet with valid data', async () => {
					const mockData = [
						{ name: 'Test', value: 100 },
						{ name: 'Test2', value: 200 }
					];

					const result = await (service as any).createDataSheet(
						mockData
					);

					expect(result).toBeDefined();
				});
			});

			describe('_applyCellStyle', () => {
				it('should apply numeric style for numeric columns', () => {
					const mockWorksheet = { A1: { v: 100 } } as any;
					const numericColumns = new Set([0]);
					const styles = {
						numeric: { numFmt: '#,##0.00' },
						text: { font: { bold: false } }
					};

					(service as any)._applyCellStyle(
						mockWorksheet,
						'A1',
						0,
						numericColumns,
						styles
					);

					// Should call _applyNumericCellStyle for numeric columns
				});

				it('should apply text style for non-numeric columns', () => {
					const mockWorksheet = { A1: { v: 'text', s: {} } } as any;
					const numericColumns = new Set([1]); // Column 0 is not numeric
					const styles = {
						numeric: { numFmt: '#,##0.00' },
						text: { font: { bold: false } }
					};

					(service as any)._applyCellStyle(
						mockWorksheet,
						'A1',
						0,
						numericColumns,
						styles
					);

					expect(mockWorksheet['A1'].s).toEqual(styles.text);
				});
			});

			describe('_applyNumericCellStyle', () => {
				it('should handle empty string values', () => {
					const mockWorksheet = { A1: { v: '' } } as any;
					const numericStyle = { numFmt: '#,##0.00' };

					(service as any)._applyNumericCellStyle(
						mockWorksheet,
						'A1',
						numericStyle
					);

					expect(mockWorksheet['A1'].t).toBe('s');
					expect(mockWorksheet['A1'].s).toBe(numericStyle);
				});

				it('should convert and style numeric values', () => {
					const mockWorksheet = { A1: { v: '123.45' } } as any;
					const numericStyle = { numFmt: '#,##0.00' };

					(service as any)._applyNumericCellStyle(
						mockWorksheet,
						'A1',
						numericStyle
					);

					expect(mockWorksheet['A1'].t).toBe('n');
					expect(mockWorksheet['A1'].s).toBe(numericStyle);
				});
			});

			describe('_convertToNumericIfPossible', () => {
				it('should convert valid numeric strings to numbers', () => {
					const cell = { v: '123.45' };

					(service as any)._convertToNumericIfPossible(cell);

					expect(cell.v).toBe(123.45);
				});

				it('should not convert non-numeric strings', () => {
					const cell = { v: 'not a number' };

					(service as any)._convertToNumericIfPossible(cell);

					expect(cell.v).toBe('not a number');
				});

				it('should not convert non-string values', () => {
					const cell = { v: 123 };

					(service as any)._convertToNumericIfPossible(cell);

					expect(cell.v).toBe(123);
				});
			});
		});

		describe('Payment Summary Sheet Methods', () => {
			describe('_buildSectionData', () => {
				it('should build collected payments section data', () => {
					const paymentStats = {
						collectedByMode: { cash: 1000, card: 500 },
						returnedByMode: { cash: 100, card: 50 },
						collectedCountByMode: { cash: 10, card: 5 },
						returnedCountByMode: { cash: 2, card: 1 }
					};
					const paymentModes = [
						{ key: 'cash', label: 'Cash' },
						{ key: 'card', label: 'Card' }
					];

					const result = (service as any)._buildSectionData(
						'PAYMENTS COLLECTED',
						paymentStats,
						paymentModes,
						'collected'
					);

					expect(result).toEqual([
						['PAYMENTS COLLECTED', '', ''],
						[
							'Total amount collected through Cash',
							1000,
							'10 Transactions'
						],
						[
							'Total amount collected through Card',
							500,
							'5 Transactions'
						],
						['Total Payments collected', 1500, '15 Transactions'],
						['', '', '']
					]);
				});

				it('should build returned payments section data', () => {
					const paymentStats = {
						collectedByMode: { cash: 1000, card: 500 },
						returnedByMode: { cash: 100, card: 50 },
						collectedCountByMode: { cash: 10, card: 5 },
						returnedCountByMode: { cash: 2, card: 1 }
					};
					const paymentModes = [
						{ key: 'cash', label: 'Cash' },
						{ key: 'card', label: 'Card' }
					];

					const result = (service as any)._buildSectionData(
						'PAYMENTS RETURNED',
						paymentStats,
						paymentModes,
						'returned'
					);

					expect(result).toEqual([
						['PAYMENTS RETURNED', '', ''],
						[
							'Total amount returned through Cash',
							100,
							'2 Transactions'
						],
						[
							'Total amount returned through Card',
							50,
							'1 Transactions'
						],
						['Total Payments returned', 150, '3 Transactions'],
						['', '', '']
					]);
				});

				it('should build net payments section data', () => {
					const paymentStats = {
						collectedByMode: { cash: 1000, card: 500 },
						returnedByMode: { cash: 100, card: 50 },
						collectedCountByMode: { cash: 10, card: 5 },
						returnedCountByMode: { cash: 2, card: 1 }
					};
					const paymentModes = [
						{ key: 'cash', label: 'Cash' },
						{ key: 'card', label: 'Card' }
					];

					const result = (service as any)._buildSectionData(
						'NET AMOUNT',
						paymentStats,
						paymentModes,
						'net'
					);

					expect(result).toEqual([
						['NET AMOUNT', '', ''],
						[
							'Total amount net through Cash',
							900,
							'12 Transactions'
						],
						[
							'Total amount net through Card',
							450,
							'6 Transactions'
						],
						['Total Payments net', 1350, '18 Transactions'],
						['', '', '']
					]);
				});
			});

			describe('Cell Styling Methods', () => {
				describe('_applyHeaderStyling', () => {
					it('should apply header styling to header rows', () => {
						const mockWorksheet = {
							A1: { v: 'Header', s: {} }
						} as any;
						const headerRow = ['Header', '', ''];

						(service as any)._applyHeaderStyling(
							mockWorksheet,
							headerRow,
							0
						);

						expect(mockWorksheet['A1'].s).toEqual({
							font: { bold: true, sz: 12 },
							fill: { fgColor: { rgb: 'DDDDDD' } }
						});
					});

					it('should not apply styling to non-header rows', () => {
						const mockWorksheet = {
							A1: { v: 'Data', s: {} }
						} as any;
						const dataRow = ['Data', 'Value', 'More'];

						(service as any)._applyHeaderStyling(
							mockWorksheet,
							dataRow,
							0
						);

						expect(mockWorksheet['A1'].s).toEqual({});
					});
				});

				describe('_applyNumberFormatting', () => {
					it('should apply number formatting to rows with numeric values', () => {
						const mockWorksheet = {
							B1: { v: 123.45, s: {} }
						} as any;
						const numericRow = ['Label', 123.45, 'Text'];

						(service as any)._applyNumberFormatting(
							mockWorksheet,
							numericRow,
							0
						);

						expect(mockWorksheet['B1'].s).toEqual({
							numFmt: '#,##0.00',
							alignment: { horizontal: 'right' }
						});
					});

					it('should not apply formatting to rows without numeric values', () => {
						const mockWorksheet = {
							B1: { v: 'text', s: {} }
						} as any;
						const textRow = ['Label', 'text', 'More text'];

						(service as any)._applyNumberFormatting(
							mockWorksheet,
							textRow,
							0
						);

						expect(mockWorksheet['B1'].s).toEqual({});
					});
				});

				describe('_applyTotalRowStyling', () => {
					it('should apply bold styling to total payment rows', () => {
						const mockWorksheet = {
							A1: { v: 'Total Payments collected', s: {} },
							B1: { v: 1000, s: {} },
							C1: { v: '10 Transactions', s: {} }
						} as any;
						const totalRow = [
							'Total Payments collected',
							1000,
							'10 Transactions'
						];

						(service as any)._applyTotalRowStyling(
							mockWorksheet,
							totalRow,
							0
						);

						expect(mockWorksheet['A1'].s.font).toEqual({
							bold: true
						});
						expect(mockWorksheet['B1'].s.font).toEqual({
							bold: true
						});
						expect(mockWorksheet['C1'].s.font).toEqual({
							bold: true
						});
					});

					it('should apply bold styling to total net rows', () => {
						const mockWorksheet = {
							A1: { v: 'Total (net) amount', s: {} }
						} as any;
						const totalRow = [
							'Total (net) amount',
							900,
							'8 Transactions'
						];

						(service as any)._applyTotalRowStyling(
							mockWorksheet,
							totalRow,
							0
						);

						expect(mockWorksheet['A1'].s.font).toEqual({
							bold: true
						});
					});

					it('should not apply styling to non-total rows', () => {
						const mockWorksheet = {
							A1: { v: 'Regular row', s: {} }
						} as any;
						const regularRow = ['Regular row', 100, 'Text'];

						(service as any)._applyTotalRowStyling(
							mockWorksheet,
							regularRow,
							0
						);

						expect(mockWorksheet['A1'].s).toEqual({});
					});
				});

				describe('_isTotalRow', () => {
					it('should identify total payment rows', () => {
						const result1 = (service as any)._isTotalRow(
							'Total Payments collected'
						);
						const result2 = (service as any)._isTotalRow(
							'Total Payments returned'
						);

						expect(result1).toBe(true);
						expect(result2).toBe(true);
					});

					it('should identify total net rows', () => {
						const result = (service as any)._isTotalRow(
							'Total (net) amount'
						);

						expect(result).toBe(true);
					});

					it('should not identify regular rows as total rows', () => {
						const result = (service as any)._isTotalRow(
							'Regular data row'
						);

						expect(result).toBe(false);
					});
				});

				describe('_ensureCellStyle', () => {
					it('should initialize style object if not present', () => {
						const mockWorksheet = { A1: { v: 'test' } } as any;

						(service as any)._ensureCellStyle(mockWorksheet, 'A1');

						expect(mockWorksheet['A1'].s).toEqual({});
					});

					it('should not overwrite existing style object', () => {
						const existingStyle = { font: { bold: true } };
						const mockWorksheet = {
							A1: { v: 'test', s: existingStyle }
						};

						(service as any)._ensureCellStyle(mockWorksheet, 'A1');

						expect(mockWorksheet['A1'].s).toBe(existingStyle);
					});
				});
			});
		});

		describe('Date Range Logic Coverage', () => {
			describe('getAppointmentsChartData - Date Grouping', () => {
				it('should use daily grouping for date ranges <= 35 days', async () => {
					const dto: GetAppointmentsChartDataDto = {
						startDate: '2023-01-01',
						endDate: '2023-01-30', // 29 days
						clinicId: 'clinic-123',
						type: AppointmentAnalyticsType.ALL
					};

					const mockQueryResult = [
						{
							date: '2023-01-01',
							total: '10',
							missed: '2'
						}
					];

					mockRepository.query.mockReset();
					mockRepository.query.mockResolvedValue(mockQueryResult);

					const result = await service.getAppointmentsChartData(dto);

					// Verify that the method returns data
					expect(result).toBeDefined();
					expect(result.total).toBeDefined();
					// Verify that the query was called with the clinic ID and dates
					expect(mockRepository.query).toHaveBeenCalledWith(
						expect.any(String),
						expect.arrayContaining([
							dto.clinicId,
							expect.any(Date),
							expect.any(Date)
						])
					);
				});

				it('should use weekly grouping for date ranges 36-210 days', async () => {
					const dto: GetAppointmentsChartDataDto = {
						startDate: '2023-01-01',
						endDate: '2023-03-15', // ~73 days
						clinicId: 'clinic-123',
						type: AppointmentAnalyticsType.ALL
					};

					const mockQueryResult = [
						{
							date: '2023-W01',
							total: '50',
							missed: '5'
						}
					];

					mockRepository.query.mockReset();
					mockRepository.query.mockResolvedValue(mockQueryResult);

					const result = await service.getAppointmentsChartData(dto);

					// Verify that the method returns data
					expect(result).toBeDefined();
					expect(result.total).toBeDefined();
					// Verify that the query was called with the clinic ID and dates
					expect(mockRepository.query).toHaveBeenCalledWith(
						expect.any(String),
						expect.arrayContaining([
							dto.clinicId,
							expect.any(Date),
							expect.any(Date)
						])
					);
				});

				it('should use monthly grouping for date ranges 211-730 days', async () => {
					const dto: GetAppointmentsChartDataDto = {
						startDate: '2023-01-01',
						endDate: '2023-12-31', // 364 days
						clinicId: 'clinic-123',
						type: AppointmentAnalyticsType.ALL
					};

					const mockQueryResult = [
						{
							date: '2023-01',
							total: '300',
							missed: '30'
						}
					];

					mockRepository.query.mockReset();
					mockRepository.query.mockResolvedValue(mockQueryResult);

					const result = await service.getAppointmentsChartData(dto);

					// Verify that the method returns data
					expect(result).toBeDefined();
					expect(result.total).toBeDefined();
					// Verify that the query was called with the clinic ID and dates
					expect(mockRepository.query).toHaveBeenCalledWith(
						expect.any(String),
						expect.arrayContaining([
							dto.clinicId,
							expect.any(Date),
							expect.any(Date)
						])
					);
				});

				it('should use yearly grouping for date ranges > 730 days', async () => {
					const dto: GetAppointmentsChartDataDto = {
						startDate: '2021-01-01',
						endDate: '2023-12-31', // ~1095 days
						clinicId: 'clinic-123',
						type: AppointmentAnalyticsType.ALL
					};

					const mockQueryResult = [
						{
							date: '2023',
							total: '3650',
							missed: '365'
						}
					];

					mockRepository.query.mockReset();
					mockRepository.query.mockResolvedValue(mockQueryResult);

					const result = await service.getAppointmentsChartData(dto);

					// Verify that the method returns data
					expect(result).toBeDefined();
					expect(result.total).toBeDefined();
					// Verify that the query was called with the clinic ID and dates
					expect(mockRepository.query).toHaveBeenCalledWith(
						expect.any(String),
						expect.arrayContaining([
							dto.clinicId,
							expect.any(Date),
							expect.any(Date)
						])
					);
				});
			});

			describe('getCollectedPaymentsChartData - Date Grouping', () => {
				it('should use daily grouping for date ranges <= 35 days', async () => {
					const dto: GetRevenueChartDataDto = {
						startDate: '2023-01-01',
						endDate: '2023-01-30', // 29 days
						clinicId: 'clinic-123'
					};

					const mockQueryResult = [
						{
							date: '2023-01-01',
							cash: '100',
							card: '200',
							wallet: '50',
							cheque: '25',
							banktransfer: '75'
						}
					];

					mockRepository.query.mockReset();
					mockRepository.query.mockResolvedValue(mockQueryResult);

					const result =
						await service.getCollectedPaymentsChartData(dto);

					expect(result).toEqual([
						{
							date: '2023-01-01',
							cash: 100,
							card: 200,
							wallet: 50,
							cheque: 25,
							bankTransfer: 75
						}
					]);
					// Verify that the query was called with the clinic ID and dates
					expect(mockRepository.query).toHaveBeenCalledWith(
						expect.any(String),
						expect.arrayContaining([
							dto.clinicId,
							expect.any(Date),
							expect.any(Date)
						])
					);
				});

				it('should use weekly grouping for date ranges 36-210 days', async () => {
					const dto: GetRevenueChartDataDto = {
						startDate: '2023-01-01',
						endDate: '2023-03-15', // ~73 days
						clinicId: 'clinic-123'
					};

					const mockQueryResult = [
						{
							date: '2023-W01',
							cash: '1000',
							card: '2000',
							wallet: '500',
							cheque: '250',
							banktransfer: '750'
						}
					];

					mockRepository.query.mockReset();
					mockRepository.query.mockResolvedValue(mockQueryResult);

					const result =
						await service.getCollectedPaymentsChartData(dto);

					expect(result).toEqual([
						{
							date: '2023-W01',
							cash: 1000,
							card: 2000,
							wallet: 500,
							cheque: 250,
							bankTransfer: 750
						}
					]);
					// Verify that the query was called with the clinic ID and dates
					expect(mockRepository.query).toHaveBeenCalledWith(
						expect.any(String),
						expect.arrayContaining([
							dto.clinicId,
							expect.any(Date),
							expect.any(Date)
						])
					);
				});

				it('should use monthly grouping for date ranges 211-730 days', async () => {
					const dto: GetRevenueChartDataDto = {
						startDate: '2023-01-01',
						endDate: '2023-12-31', // 364 days
						clinicId: 'clinic-123'
					};

					const mockQueryResult = [
						{
							date: '2023-01',
							cash: '10000',
							card: '20000',
							wallet: '5000',
							cheque: '2500',
							banktransfer: '7500'
						}
					];

					mockRepository.query.mockReset();
					mockRepository.query.mockResolvedValue(mockQueryResult);

					const result =
						await service.getCollectedPaymentsChartData(dto);

					expect(result).toEqual([
						{
							date: '2023-01',
							cash: 10000,
							card: 20000,
							wallet: 5000,
							cheque: 2500,
							bankTransfer: 7500
						}
					]);
					// Verify that the query was called with the clinic ID and dates
					expect(mockRepository.query).toHaveBeenCalledWith(
						expect.any(String),
						expect.arrayContaining([
							dto.clinicId,
							expect.any(Date),
							expect.any(Date)
						])
					);
				});

				it('should use yearly grouping for date ranges > 730 days', async () => {
					const dto: GetRevenueChartDataDto = {
						startDate: '2021-01-01',
						endDate: '2023-12-31', // ~1095 days
						clinicId: 'clinic-123'
					};

					const mockQueryResult = [
						{
							date: '2023',
							cash: '120000',
							card: '240000',
							wallet: '60000',
							cheque: '30000',
							banktransfer: '90000'
						}
					];

					mockRepository.query.mockReset();
					mockRepository.query.mockResolvedValue(mockQueryResult);

					const result =
						await service.getCollectedPaymentsChartData(dto);

					expect(result).toEqual([
						{
							date: '2023',
							cash: 120000,
							card: 240000,
							wallet: 60000,
							cheque: 30000,
							bankTransfer: 90000
						}
					]);
					// Verify that the query was called with the clinic ID and dates
					expect(mockRepository.query).toHaveBeenCalledWith(
						expect.any(String),
						expect.arrayContaining([
							dto.clinicId,
							expect.any(Date),
							expect.any(Date)
						])
					);
				});
			});
		});

		describe('Error Handling Coverage', () => {
			it('should handle errors in getProductsBillingData with stack trace', async () => {
				const clinicId = 'clinic-123';
				const startDate = new Date('2023-01-01');
				const endDate = new Date('2023-01-31');

				const error = new Error('Database connection failed');
				error.stack = 'Error stack trace here';

				mockRepository.query.mockReset();
				mockRepository.query.mockRejectedValue(error);

				await expect(
					(service as any).getProductsBillingData(
						clinicId,
						startDate,
						endDate
					)
				).rejects.toThrow('Database connection failed');
			});

			it('should handle errors in getProductsBillingData without stack trace', async () => {
				const clinicId = 'clinic-123';
				const startDate = new Date('2023-01-01');
				const endDate = new Date('2023-01-31');

				const error = new Error('Database connection failed');
				delete error.stack;

				mockRepository.query.mockReset();
				mockRepository.query.mockRejectedValue(error);

				await expect(
					(service as any).getProductsBillingData(
						clinicId,
						startDate,
						endDate
					)
				).rejects.toThrow('Database connection failed');
			});

			it('should handle unknown errors in getProductsBillingData', async () => {
				const clinicId = 'clinic-123';
				const startDate = new Date('2023-01-01');
				const endDate = new Date('2023-01-31');

				mockRepository.query.mockReset();
				mockRepository.query.mockRejectedValue('Unknown error string');

				await expect(
					(service as any).getProductsBillingData(
						clinicId,
						startDate,
						endDate
					)
				).rejects.toBe('Unknown error string');
			});
		});
	});
});

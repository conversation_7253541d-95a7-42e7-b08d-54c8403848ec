type Env = 'development' | 'qa' | 'uat' | 'prod';

export const getLoginUrl = (brandSlug: string) => {
	const baseUrl: Record<Env, string> = {
		development: 'http://:slug.localhost:4201/signin/pin',
		qa: 'https://:slug.nidanaqa-api.napses.in/signin/pin',
		uat: 'https://:slug.nidana.tech/signin/pin',
		prod: 'https://:slug.nidana.io/signin/pin'
	};
	const env = (process.env.NODE_ENV as Env) || 'development';
	const url = baseUrl[env].replace(':slug', brandSlug);
	return url;
};
export const getClientBookingUrl = (brandSlug: string) => {
	const baseUrl: Record<Env, string> = {
		development: 'http://:slug.localhost:4202/patientportal/',
		qa: 'https://:slug.nidanaqa-api.napses.in/patientportal/',
		uat: 'https://:slug.nidana.tech/patientportal/',
		prod: 'https://:slug.nidana.io/patientportal/'
	};
	const env = (process.env.NODE_ENV as Env) || 'development';
	const url = baseUrl[env].replace(':slug', brandSlug);
	return url;
};
export const isProduction = () => {
	return process.env.NODE_ENV === 'prod';
};

export const getFallbackLoginUrl = () => {
	const baseUrl: Record<Env, string> = {
		development: 'http://localhost:4201/signin/pin',
		qa: 'https://nidanaqa-api.napses.in/signin/pin',
		uat: 'https://nidana.tech/signin/pin',
		prod: 'https://nidana.io/signin/pin'
	};
	const env = (process.env.NODE_ENV as Env) || 'development';
	return baseUrl[env];
};

export const isProductionOrUat = () => {
	// return true; //for testing
	return process.env.NODE_ENV === 'prod' || process.env.NODE_ENV === 'uat';
};

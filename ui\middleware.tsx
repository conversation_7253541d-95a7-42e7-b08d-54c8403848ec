import { NextRequest, NextResponse } from 'next/server';
import { getBrandBySlug } from './app/services/brands.services';
import { Role } from './app/types/roles';
import {
    getBrandSlugFromHostname,
    isProduction,
    isQA,
    isUAT,
    isLocal,
} from './app/utils/environment';

// Define path permissions mapping
const PATH_PERMISSIONS: Record<string, Role[]> = {
    // Admin routes - restricted to admin roles
    '/admin/analytics': [Role.SUPER_ADMIN, Role.ADMIN],
    // Brands page - only super admin can access
    '/brands': [Role.SUPER_ADMIN],
    // Onboard page - super admin should not access (all roles except super_admin)
    '/onboard': [
        Role.ADMIN,
        Role.RECEPTIONIST,
        Role.DOCTOR,
        Role.VET_TECHNICIAN,
        Role.LAB_TECHNICIAN,
    ],
    // Dashboard - accessible to all authenticated users (no specific role restriction)
};

export const config = {
    // Update matcher to include all routes that need middleware processing
    matcher: [
        '/',
        '/about',
        '/_sites/:path',
        '/signin/pin',
        '/signin/email',
        '/signin/forgot-password',
        '/admin/:path*',
        '/brands/:path*',
        '/dashboard',
        '/onboard',
        '/appointments/:path*',
        '/patients/:path*',
        '/owners/:path*',
        '/diagnostics/:path*',
        '/profile/:path*',
        '/chats/:path*',
        '/tasks/:path*',
    ],
};

export default async function middleware(req: NextRequest) {
    // Prevent middleware bypass attacks
    if (req.headers.get('x-middleware-subrequest')) {
        return new Response('Unauthorized', { status: 403 });
    }

    const url = req.nextUrl;
    const pathname = url.pathname;
    const hostname = req.headers.get('host') ?? '';

    // Use environment utilities for clean hostname parsing
    const currentHost = getBrandSlugFromHostname(hostname);
    const isProd = isProduction(hostname);
    const isQAEnv = isQA(hostname);
    const isUATEnv = isUAT(hostname);
    const isLocalEnv = isLocal(hostname);

    // Public routes that don't need authentication
    const publicRoutes = [
        '/signin/pin',
        '/signin/email',
        '/signin/forgot-password',
    ];

    if (publicRoutes.includes(pathname)) {
        return NextResponse.next();
    }

    // Get auth from cookies
    const auth = req.cookies.get('AUTH');
    if (!auth?.value) {
        return NextResponse.redirect(new URL('/signin/pin', req.url));
    }

    try {
        const userData = JSON.parse(auth.value);
        const userRole = userData.role || userData.roleName; // Backward compatibility

        // Check permissions against defined path rules
        for (const [path, allowedRoles] of Object.entries(PATH_PERMISSIONS)) {
            if (pathname.startsWith(path)) {
                const hasAccess = allowedRoles.includes(userRole as Role);
                if (!hasAccess) {
                    return NextResponse.redirect(
                        new URL('/dashboard', req.url)
                    );
                }
            }
        }
    } catch (error) {
        // If there's any error parsing the auth cookie, redirect to login
        return NextResponse.redirect(new URL('/signin/pin', req.url));
    }

    const response = NextResponse.next();
    if (currentHost === hostname && url.pathname === '/signin/pin') {
        return NextResponse.redirect(new URL('/', req.url));
    }

    if (!currentHost || currentHost === hostname) {
        if (url.pathname !== '/') {
            return NextResponse.redirect(new URL('/', req.url));
        }
    }

    // console.log(hostname,currentHost)
    // if (currentHost !== 'superadmin') {
    //     if (currentHost !== '') {
    //         console.log(hostname,currentHost)
    //         const brandInfo: any = await getBrandBySlug(currentHost);
    //         const brandId = brandInfo?.data.id;
    //         const isSecure = req.headers.get('x-forwarded-proto') === 'https';
    //         if (brandId) {
    //             response.cookies.set('BRAND_ID', brandId, {
    //                 secure: isProd || isQAEnv || isUATEnv ? isSecure : false,
    //                 path: '/',
    //                 maxAge: 60 * 60 * 24 * 7,
    //                 sameSite: 'Lax',
    //             });
    //         }
    //     }
    // }

    if (url.pathname === '/signin/pin') {
        return response;
    }

    if (currentHost === hostname && url.pathname === '/signin/pin') {
        return NextResponse.redirect(new URL('/', req.url));
    }

    if (!currentHost || currentHost === hostname) {
        if (url.pathname !== '/') {
            return NextResponse.redirect(new URL('/', req.url));
        }
    }

    return response;
}

import {
	BadRequestException,
	ConflictException,
	Injectable,
	InternalServerErrorException,
	NotFoundException
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { Brand } from './entities/brand.entity';
import { CreateBrandDto } from './dto/create-brand.dto';
import { UpdateBrandDto } from './dto/update-brand.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { BrandWithSettingsDto } from './dto/brand-with-settings.dto';
import { HandleServiceExceptions } from '../utils/common/service-exception-handler';
import { plainToInstance } from 'class-transformer';

@Injectable()
export class BrandService {
	constructor(
		@InjectRepository(Brand)
		private brandRepository: Repository<Brand>,
		private readonly logger: WinstonLogger
	) {}

	@HandleServiceExceptions('An unexpected error occurred while creating the brand')
	async createBrand(createBrandDto: CreateBrandDto): Promise<Brand> {
		this.logger.log('Creating Brand', { dto: createBrandDto });
		const existingBrand = await this.brandRepository.findOne({
			where: { name: createBrandDto.name }
		});
		if (existingBrand) {
			this.logger.error('Brand already exists', {
				email: createBrandDto.name
			});
			throw new ConflictException(
				'Brand with this name already exists'
			);
		}
		const brand = this.brandRepository.create(createBrandDto);
		return await this.brandRepository.save(brand);
	}

	@HandleServiceExceptions('An unexpected error occurred while fetching the brands')
	async getAllBrands(
		page: number = 1,
		limit: number = 10,
		search: string = '',
		orderBy: string = 'DESC'
	): Promise<{ brands: Brand[]; total: number }> {
		this.logger.log('Fetching all Brands', {
			page,
			limit,
			search,
			orderBy
		});

		const whereCondition = search
			? { name: Like(`%${search}%`) }
			: {};

		const [brands, total] = await this.brandRepository.findAndCount({
			where: whereCondition,
			skip: (page - 1) * limit,
			take: limit,
			order:
				orderBy === 'ASC' ? { createdAt: 'ASC' } : { createdAt: 'DESC' }
		});

		this.logger.log('Fetched all Brands:', {
			brandsCount: brands.length,
			total,
			page,
			limit
		});

		return { brands, total };
	}

	// Backward compatibility method for non-paginated calls
	@HandleServiceExceptions('An unexpected error occurred while fetching the brands')
	async getAllBrandsSimple(): Promise<Brand[]> {
		this.logger.log('Fetching all Brands (simple)');
		const brands = await this.brandRepository.find({
			order: { createdAt: 'DESC' }
		});
		this.logger.log('Fetched all Brands (simple):', { brands });
		return brands;
	}

	@HandleServiceExceptions('An unexpected error occurred while fetching the brand')
	async getBrandById(id: string): Promise<Brand | null> {
		this.logger.log('Fetching a Brand');
		const brand = await this.brandRepository.findOne({ where: { id } });
		this.logger.log('Fetched a Brand:', { brand });
		return brand;
	}

	@HandleServiceExceptions('An unexpected error occurred while fetching the brand')
	async getBrandBySlug(slug: string): Promise<BrandWithSettingsDto | null> {
		this.logger.log('Fetching a Brand');
		const brand = await this.brandRepository.findOne({
			where: { slug },
			relations: ['clinics']
		});

		if (!brand) {
			return null;
		}

		// Calculate the client booking enabled flag
		const hasClientBookingEnabled =
			brand.clinics?.some(
				clinic =>
					clinic.customRule?.clientBookingSettings?.isEnabled ===
					true
			) || false;

		// Create a plain object with all the data needed for transformation
		const brandWithSettings = {
			...brand, // This includes id, name, slug, createdAt, updatedAt, createdBy, updatedBy, clinics
			hasClientBookingEnabled
		};

		// Use class-transformer to automatically map to DTO
		const brandDto = plainToInstance(BrandWithSettingsDto, brandWithSettings, {
			excludeExtraneousValues: true // Only include properties marked with @Expose()
		});

		this.logger.log('Fetched a Brand with settings:', {
			brandId: brand.id,
			hasClientBookingEnabled,
			clinicsCount: brand.clinics?.length || 0
		});

		return brandDto;
	}

	@HandleServiceExceptions('An unexpected error occurred while updating the brand')
	async updateBrand(id: string, updateBrandDto: UpdateBrandDto): Promise<Brand> {
		this.logger.log('Updating Brand', { id, dto: updateBrandDto });
		
		// Check if brand exists
		const existingBrand = await this.brandRepository.findOne({
			where: { id }
		});
		
		if (!existingBrand) {
			this.logger.error('Brand not found', { id });
			throw new NotFoundException('Brand not found');
		}

		// Check if another brand with the same name exists (excluding current brand)
		const brandWithSameName = await this.brandRepository.findOne({
			where: { name: updateBrandDto.name }
		});
		
		if (brandWithSameName && brandWithSameName.id !== id) {
			this.logger.error('Brand with this name already exists', {
				name: updateBrandDto.name,
				existingBrandId: brandWithSameName.id
			});
			throw new ConflictException('Brand with this name already exists');
		}

		// Update the brand
		await this.brandRepository.update(id, {
			name: updateBrandDto.name,
			updatedAt: new Date()
		});

		// Return the updated brand
		const updatedBrand = await this.brandRepository.findOne({
			where: { id }
		});

		this.logger.log('Brand updated successfully', { id, updatedBrand });
		return updatedBrand!;
	}
}

import { Test, TestingModule } from '@nestjs/testing';
import { DiagnosticTemplatesController } from './diagnostic-note.controlller';
import { DiagnosticTemplatesService } from './diagnostic-note.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { CreateDiagnosticTemplateDto } from './dto/create-template.dto';
import {
	CreateDiagnosticNoteDto,
	UpdateDiagnosticNoteDto
} from './dto/diagnostic-note.dto';
import { Role } from '../roles/role.enum';
import { Roles } from '../roles/roles.decorator';
import { HttpException, HttpStatus } from '@nestjs/common';
import { TemplateType } from './entities/diagnostic-template.entity';
import { RoleService } from '../roles/role.service';

describe('DiagnosticTemplatesController', () => {
	let controller: DiagnosticTemplatesController;
	let service: DiagnosticTemplatesService;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [DiagnosticTemplatesController],
			providers: [
				{
					provide: DiagnosticTemplatesService,
					useValue: {
						create: jest.fn(),
						findAll: jest.fn(),
						findOne: jest.fn(),
						update: jest.fn(),
						remove: jest.fn(),
						createNote: jest.fn(),
						getTemplatesForLabReport: jest.fn(),
						findTemplatesByDiagnostic: jest.fn(),
						getPatientNotes: jest.fn(),
						getNote: jest.fn(),
						updateNote: jest.fn(),
						deleteNote: jest.fn()
					}
				},
				{
					provide: RoleService,
					useValue: {
						find: jest.fn(),
						findOne: jest.fn()
					}
				}
			]
		}).compile();

		controller = module.get<DiagnosticTemplatesController>(
			DiagnosticTemplatesController
		);
		service = module.get<DiagnosticTemplatesService>(
			DiagnosticTemplatesService
		);
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	describe('create', () => {
		it('should create a new diagnostic template', async () => {
			const createDto: CreateDiagnosticTemplateDto = {
				templateName: 'Template 1',
				clinicId: 'clinic-id',
				assignedDiagnostics: [],
				templateType: TemplateType.NOTES
			};
			const result = { id: '1', ...createDto };
			jest.spyOn(service, 'create').mockResolvedValue(result as any);

			expect(
				await controller.create(createDto, { user: { id: 'user-id' } })
			).toBe(result);
			expect(service.create).toHaveBeenCalledWith(createDto, 'user-id');
		});

		it('should throw an error if template creation fails', async () => {
			const createDto: CreateDiagnosticTemplateDto = {
				templateName: 'Template 1',
				clinicId: 'clinic-id',
				assignedDiagnostics: [],
				templateType: TemplateType.NOTES
			};
			jest.spyOn(service, 'create').mockRejectedValue(
				new HttpException('Error', HttpStatus.BAD_REQUEST)
			);

			await expect(
				controller.create(createDto, { user: { id: 'user-id' } })
			).rejects.toThrow(HttpException);
		});
	});

	describe('findAll', () => {
		it('should return all templates for a clinic', async () => {
			const clinicId = 'clinic-id';
			const result = [{ id: '1', templateName: 'Template 1' }];
			jest.spyOn(service, 'findAll').mockResolvedValue(result as any);

			expect(await controller.findAll(clinicId)).toBe(result);
			expect(service.findAll).toHaveBeenCalledWith(clinicId);
		});
	});

	describe('findOne', () => {
		it('should return a template by id', async () => {
			const id = '1';
			const clinicId = 'clinic-id';
			const result = { id, templateName: 'Template 1' };
			jest.spyOn(service, 'findOne').mockResolvedValue(result as any);

			expect(await controller.findOne(id, clinicId)).toBe(result);
			expect(service.findOne).toHaveBeenCalledWith(id, clinicId);
		});
	});

	describe('update', () => {
		it('should update a template', async () => {
			const id = '1';
			const updateDto: Partial<CreateDiagnosticTemplateDto> = {
				templateName: 'Updated Template'
			};
			const clinicId = 'clinic-id';
			const result = { id, ...updateDto };
			jest.spyOn(service, 'update').mockResolvedValue(result as any);

			expect(
				await controller.update(id, updateDto, clinicId, {
					user: { id: 'user-id' }
				})
			).toBe(result);
			expect(service.update).toHaveBeenCalledWith(
				id,
				updateDto,
				'user-id',
				clinicId
			);
		});
	});

	describe('remove', () => {
		it('should delete a template', async () => {
			const id = '1';
			const clinicId = 'clinic-id';
			jest.spyOn(service, 'remove').mockResolvedValue(undefined);

			await controller.remove(id, clinicId);
			expect(service.remove).toHaveBeenCalledWith(id, clinicId);
		});
	});

	describe('createNote', () => {
		it('should create a new diagnostic note', async () => {
			const createNoteDto: CreateDiagnosticNoteDto = {
				labReportId: 'lab-report-id',
				clinicId: 'clinic-id',
				templateId: 'template-id',
				templateName: 'Template 1',
				noteData: { notes: 'Some notes' }
			};
			const result = { id: '1', ...createNoteDto };
			jest.spyOn(service, 'createNote').mockResolvedValue(result as any);

			expect(
				await controller.createNote(createNoteDto, {
					user: { userId: 'user-id' }
				})
			).toBe(result);
			expect(service.createNote).toHaveBeenCalledWith(
				createNoteDto,
				'user-id'
			);
		});
	});

	describe('updateNote', () => {
		it('should update a diagnostic note', async () => {
			const id = '1';
			const updateNoteDto: UpdateDiagnosticNoteDto = {
				noteData: { notes: 'Updated notes' }
			};
			const result = { id, ...updateNoteDto };
			jest.spyOn(service, 'updateNote').mockResolvedValue(result as any);

			expect(
				await controller.updateNote(id, updateNoteDto, {
					user: { id: 'user-id' }
				})
			).toBe(result);
			expect(service.updateNote).toHaveBeenCalledWith(
				id,
				updateNoteDto,
				'user-id'
			);
		});
	});

	describe('deleteNote', () => {
		it('should delete a diagnostic note', async () => {
			const noteId = '1';
			jest.spyOn(service, 'deleteNote').mockResolvedValue({
				success: true
			});

			expect(await controller.deleteNote(noteId)).toEqual({
				success: true
			});
			expect(service.deleteNote).toHaveBeenCalledWith(noteId);
		});
	});

	describe('getTemplatesForLabReport', () => {
		it('should return templates for a lab report', async () => {
			const labReportId = 'lab-report-id';
			const clinicId = 'clinic-id';
			const result = [
				{
					id: '1',
					templateName: 'Template 1',
					templateType: TemplateType.NOTES
				},
				{
					id: '2',
					templateName: 'Template 2',
					templateType: TemplateType.TABLE
				}
			];
			jest.spyOn(service, 'getTemplatesForLabReport').mockResolvedValue(
				result as any
			);

			expect(
				await controller.getTemplatesForLabReport(labReportId, clinicId)
			).toBe(result);
			expect(service.getTemplatesForLabReport).toHaveBeenCalledWith(
				labReportId,
				clinicId
			);
		});

		it('should handle errors when getting templates for lab report', async () => {
			const labReportId = 'lab-report-id';
			const clinicId = 'clinic-id';
			jest.spyOn(service, 'getTemplatesForLabReport').mockRejectedValue(
				new HttpException('Lab report not found', HttpStatus.NOT_FOUND)
			);

			await expect(
				controller.getTemplatesForLabReport(labReportId, clinicId)
			).rejects.toThrow(HttpException);
			expect(service.getTemplatesForLabReport).toHaveBeenCalledWith(
				labReportId,
				clinicId
			);
		});
	});

	describe('getNotesByLabReport', () => {
		it('should return notes for a lab report', async () => {
			const clinicLabReportId = 'clinic-lab-report-id';
			const clinicId = 'clinic-id';
			const result = [
				{
					id: '1',
					templateName: 'Template 1',
					noteData: { notes: 'Test notes 1' }
				},
				{
					id: '2',
					templateName: 'Template 2',
					noteData: { notes: 'Test notes 2' }
				}
			];
			jest.spyOn(service, 'findTemplatesByDiagnostic').mockResolvedValue(
				result as any
			);

			expect(
				await controller.getNotesByLabReport(
					clinicLabReportId,
					clinicId
				)
			).toBe(result);
			expect(service.findTemplatesByDiagnostic).toHaveBeenCalledWith(
				clinicLabReportId,
				clinicId
			);
		});

		it('should handle errors when getting notes by lab report', async () => {
			const clinicLabReportId = 'clinic-lab-report-id';
			const clinicId = 'clinic-id';
			jest.spyOn(service, 'findTemplatesByDiagnostic').mockRejectedValue(
				new HttpException(
					'Clinic lab report not found',
					HttpStatus.NOT_FOUND
				)
			);

			await expect(
				controller.getNotesByLabReport(clinicLabReportId, clinicId)
			).rejects.toThrow(HttpException);
			expect(service.findTemplatesByDiagnostic).toHaveBeenCalledWith(
				clinicLabReportId,
				clinicId
			);
		});
	});

	describe('getPatientNotes', () => {
		it('should return diagnostic notes for a patient', async () => {
			const patientId = 'patient-id';
			const result = [
				{
					id: '1',
					templateName: 'Template 1',
					noteData: { notes: 'Patient notes 1' },
					createdAt: new Date(),
					diagnosticNumber: 'DN001'
				},
				{
					id: '2',
					templateName: 'Template 2',
					noteData: { notes: 'Patient notes 2' },
					createdAt: new Date(),
					diagnosticNumber: 'DN002'
				}
			];
			jest.spyOn(service, 'getPatientNotes').mockResolvedValue(
				result as any
			);

			expect(await controller.getPatientNotes(patientId)).toBe(result);
			expect(service.getPatientNotes).toHaveBeenCalledWith(patientId);
		});

		it('should handle errors when getting patient notes', async () => {
			const patientId = 'patient-id';
			jest.spyOn(service, 'getPatientNotes').mockRejectedValue(
				new HttpException('Patient not found', HttpStatus.NOT_FOUND)
			);

			await expect(controller.getPatientNotes(patientId)).rejects.toThrow(
				HttpException
			);
			expect(service.getPatientNotes).toHaveBeenCalledWith(patientId);
		});
	});

	describe('getNote', () => {
		it('should return a specific diagnostic note', async () => {
			const noteId = 'note-id';
			const result = {
				id: noteId,
				templateName: 'Template 1',
				noteData: { notes: 'Specific note content' },
				createdAt: new Date(),
				diagnosticNumber: 'DN001',
				patientId: 'patient-id',
				labReportId: 'lab-report-id'
			};
			jest.spyOn(service, 'getNote').mockResolvedValue(result as any);

			expect(await controller.getNote(noteId)).toBe(result);
			expect(service.getNote).toHaveBeenCalledWith(noteId);
		});

		it('should handle errors when getting a specific note', async () => {
			const noteId = 'note-id';
			jest.spyOn(service, 'getNote').mockRejectedValue(
				new HttpException('Note not found', HttpStatus.NOT_FOUND)
			);

			await expect(controller.getNote(noteId)).rejects.toThrow(
				HttpException
			);
			expect(service.getNote).toHaveBeenCalledWith(noteId);
		});
	});

	// Additional controller tests for complete coverage
	describe('Error Handling', () => {
		it('should handle service errors in findAll', async () => {
			const clinicId = 'clinic-id';
			jest.spyOn(service, 'findAll').mockRejectedValue(
				new HttpException('Database error', HttpStatus.INTERNAL_SERVER_ERROR)
			);

			await expect(controller.findAll(clinicId)).rejects.toThrow(
				HttpException
			);
		});

		it('should handle service errors in findOne', async () => {
			const id = '1';
			const clinicId = 'clinic-id';
			jest.spyOn(service, 'findOne').mockRejectedValue(
				new HttpException('Template not found', HttpStatus.NOT_FOUND)
			);

			await expect(controller.findOne(id, clinicId)).rejects.toThrow(
				HttpException
			);
		});

		it('should handle service errors in update', async () => {
			const id = '1';
			const updateDto = { templateName: 'Updated Template' };
			const clinicId = 'clinic-id';
			jest.spyOn(service, 'update').mockRejectedValue(
				new HttpException('Update failed', HttpStatus.BAD_REQUEST)
			);

			await expect(
				controller.update(id, updateDto, clinicId, {
					user: { id: 'user-id' }
				})
			).rejects.toThrow(HttpException);
		});

		it('should handle service errors in remove', async () => {
			const id = '1';
			const clinicId = 'clinic-id';
			jest.spyOn(service, 'remove').mockRejectedValue(
				new HttpException('Delete failed', HttpStatus.INTERNAL_SERVER_ERROR)
			);

			await expect(controller.remove(id, clinicId)).rejects.toThrow(
				HttpException
			);
		});

		it('should handle service errors in createNote', async () => {
			const createNoteDto: CreateDiagnosticNoteDto = {
				labReportId: 'lab-report-id',
				clinicId: 'clinic-id',
				templateId: 'template-id',
				templateName: 'Template 1',
				noteData: { notes: 'Some notes' }
			};
			jest.spyOn(service, 'createNote').mockRejectedValue(
				new HttpException('Creation failed', HttpStatus.BAD_REQUEST)
			);

			await expect(
				controller.createNote(createNoteDto, {
					user: { userId: 'user-id' }
				})
			).rejects.toThrow(HttpException);
		});

		it('should handle service errors in updateNote', async () => {
			const id = '1';
			const updateNoteDto: UpdateDiagnosticNoteDto = {
				noteData: { notes: 'Updated notes' }
			};
			jest.spyOn(service, 'updateNote').mockRejectedValue(
				new HttpException('Update failed', HttpStatus.BAD_REQUEST)
			);

			await expect(
				controller.updateNote(id, updateNoteDto, {
					user: { id: 'user-id' }
				})
			).rejects.toThrow(HttpException);
		});

		it('should handle service errors in deleteNote', async () => {
			const noteId = '1';
			jest.spyOn(service, 'deleteNote').mockRejectedValue(
				new HttpException('Delete failed', HttpStatus.INTERNAL_SERVER_ERROR)
			);

			await expect(controller.deleteNote(noteId)).rejects.toThrow(
				HttpException
			);
		});
	});
});

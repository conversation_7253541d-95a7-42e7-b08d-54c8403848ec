import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  shareAnalyticsDocuments,
  getAnalyticsDocumentRequestStatus,
  getAnalyticsDocumentRequests,
  ShareAnalyticsDocumentsRequest
} from './analytics-sharing.service';

export const useShareAnalyticsDocuments = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: shareAnalyticsDocuments,
    onSuccess: (data, variables) => {
      // Invalidate requests list for the clinic
      queryClient.invalidateQueries({
        queryKey: ['analytics-document-requests', variables.clinicId]
      });
    },
    onError: (error) => {
      console.error('Error sharing analytics documents:', error);
    }
  });
};

export const useAnalyticsDocumentRequestStatus = (requestId: string) => {
  return useQuery({
    queryKey: ['analytics-document-request-status', requestId],
    queryFn: () => getAnalyticsDocumentRequestStatus(requestId),
    enabled: !!requestId,
    refetchInterval: (data) => {
      // Refetch every 5 seconds if status is pending or processing
      if (data?.status === 'pending' || data?.status === 'processing') {
        return 5000;
      }
      return false;
    }
  });
};

export const useAnalyticsDocumentRequests = (
  clinicId: string,
  page: number = 1,
  limit: number = 10
) => {
  return useQuery({
    queryKey: ['analytics-document-requests', clinicId, page, limit],
    queryFn: () => getAnalyticsDocumentRequests(clinicId, page, limit),
    enabled: !!clinicId
  });
};

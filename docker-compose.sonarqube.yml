services:
  sonarqube:
    image: sonarqube:lts-community
    container_name: sonarqube-server
    depends_on:
      - sonarqube-db
    environment:
      SONAR_JDBC_URL: *****************************************
      SONAR_JDBC_USERNAME: sonar
      SONAR_JDBC_PASSWORD: sonar
      SONAR_ES_BOOTSTRAP_CHECKS_DISABLE: true
      # Disable Elasticsearch bootstrap checks for development
      SONAR_SEARCH_JAVAADDITIONALOPTS: "-Dbootstrap.system_call_filter=false"
      # Allow anonymous access for local development
      SONAR_FORCEAUTHENTICATION: false
    volumes:
      - sonarqube_data:/opt/sonarqube/data
      - sonarqube_extensions:/opt/sonarqube/extensions
      - sonarqube_logs:/opt/sonarqube/logs
      - sonarqube_temp:/opt/sonarqube/temp
    ports:
      - "9000:9000"
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
    # Remove sysctls for macOS compatibility
    # sysctls:
    #   - vm.max_map_count=524288
    restart: unless-stopped

  sonarqube-db:
    image: postgres:15
    container_name: sonarqube-postgres
    environment:
      POSTGRES_USER: sonar
      POSTGRES_PASSWORD: sonar
      POSTGRES_DB: sonar
    volumes:
      - sonarqube_postgresql:/var/lib/postgresql
      - sonarqube_postgresql_data:/var/lib/postgresql/data
    ports:
      - "5433:5432" # Use port 5433 externally to avoid conflict
    restart: unless-stopped

  # Scanner service for API (NestJS/TypeScript)
  sonar-scanner-api:
    image: sonarsource/sonar-scanner-cli:latest
    container_name: sonar-scanner-api
    depends_on:
      - sonarqube
    environment:
      SONAR_HOST_URL: http://sonarqube:9000
    volumes:
      - ./api:/usr/src/api
      - ./sonar-configs:/usr/src/configs
    working_dir: /usr/src/api
    profiles:
      - scan
    command: >
      sh -c "
        echo 'Waiting for SonarQube to be ready...' &&
        for i in {1..30}; do
          if curl -s http://sonarqube:9000/api/system/status | grep -q 'UP'; then
            echo 'SonarQube is ready, starting API scan...'
            break
          fi
          echo 'Waiting for SonarQube... attempt $$i/30'
          sleep 10
          if [ $$i -eq 30 ]; then
            echo 'SonarQube did not become ready in time. Proceeding anyway...'
          fi
        done &&
        sonar-scanner \
          -Dsonar.projectKey=nidana-api-local \
          -Dsonar.projectName='Nidana API (Local)' \
          -Dsonar.projectVersion=1.0 \
          -Dsonar.sources=src \
          -Dsonar.exclusions='**/test/**,**/migrations/**,**/notifications/**,**/lib/**,**/seeders/**,**/manualseeder/**,**/node_modules/**,**/coverage/**,**/dist/**,**/build/**' \
          -Dsonar.typescript.lcov.reportPaths=coverage/lcov.info \
          -Dsonar.coverage.exclusions='**/test/**,**/migrations/**,**/notifications/**,**/lib/**,**/seeders/**,**/manualseeder/**,**/scripts/**,**/utils/**,**/config/**' \
          -Dsonar.cpd.exclusions='**/test/**,**/migrations/**,**/notifications/**,**/lib/**,**/seeders/**,**/manualseeder/**' \
          -Dsonar.sourceEncoding=UTF-8 \
          -Dsonar.host.url=http://sonarqube:9000
      "

  # Scanner service for UI (Next.js)
  sonar-scanner-ui:
    image: sonarsource/sonar-scanner-cli:latest
    container_name: sonar-scanner-ui
    depends_on:
      - sonarqube
    environment:
      SONAR_HOST_URL: http://sonarqube:9000
    volumes:
      - ./ui:/usr/src/ui
      - ./sonar-configs:/usr/src/configs
    working_dir: /usr/src/ui
    profiles:
      - scan
    command: >
      sh -c "
        echo 'Waiting for SonarQube to be ready...' &&
        for i in {1..30}; do
          if curl -s http://sonarqube:9000/api/system/status | grep -q 'UP'; then
            echo 'SonarQube is ready, starting UI scan...'
            break
          fi
          echo 'Waiting for SonarQube... attempt $$i/30'
          sleep 10
          if [ $$i -eq 30 ]; then
            echo 'SonarQube did not become ready in time. Proceeding anyway...'
          fi
        done &&
        sonar-scanner \
          -Dsonar.projectKey=nidana-ui-local \
          -Dsonar.projectName='Nidana UI (Local)' \
          -Dsonar.projectVersion=1.0 \
          -Dsonar.sources=app,components,context,lib,utils \
          -Dsonar.exclusions='**/node_modules/**,**/.next/**,**/out/**,**/build/**,**/coverage/**,**/stories/**,**/ui-tests/**' \
          -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info \
          -Dsonar.coverage.exclusions='**/stories/**,**/ui-tests/**,**/public/**' \
          -Dsonar.sourceEncoding=UTF-8 \
          -Dsonar.host.url=http://sonarqube:9000
      "

  # Scanner service for Patient Portal (Next.js)
  sonar-scanner-patientportal:
    image: sonarsource/sonar-scanner-cli:latest
    container_name: sonar-scanner-patientportal
    depends_on:
      - sonarqube
    environment:
      SONAR_HOST_URL: http://sonarqube:9000
    volumes:
      - ./patientportal:/usr/src/patientportal
      - ./sonar-configs:/usr/src/configs
    working_dir: /usr/src/patientportal
    profiles:
      - scan
    command: >
      sh -c "
        echo 'Waiting for SonarQube to be ready...' &&
        for i in {1..30}; do
          if curl -s http://sonarqube:9000/api/system/status | grep -q 'UP'; then
            echo 'SonarQube is ready, starting Patient Portal scan...'
            break
          fi
          echo 'Waiting for SonarQube... attempt $$i/30'
          sleep 10
          if [ $$i -eq 30 ]; then
            echo 'SonarQube did not become ready in time. Proceeding anyway...'
          fi
        done &&
        sonar-scanner \
          -Dsonar.projectKey=nidana-patientportal-local \
          -Dsonar.projectName='Nidana Patient Portal (Local)' \
          -Dsonar.projectVersion=1.0 \
          -Dsonar.sources=app,components,lib,utils \
          -Dsonar.exclusions='**/node_modules/**,**/.next/**,**/out/**,**/build/**,**/coverage/**' \
          -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info \
          -Dsonar.coverage.exclusions='**/public/**' \
          -Dsonar.sourceEncoding=UTF-8 \
          -Dsonar.host.url=http://sonarqube:9000
      "

volumes:
  sonarqube_data:
  sonarqube_extensions:
  sonarqube_logs:
  sonarqube_temp:
  sonarqube_postgresql:
  sonarqube_postgresql_data:

networks:
  default:
    name: sonarqube-network

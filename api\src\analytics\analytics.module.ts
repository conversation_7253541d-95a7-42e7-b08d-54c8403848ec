import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnalyticsController } from './analytics.controller';
import { AnalyticsService } from './analytics.service';
import { InvoiceEntity } from '../invoice/entities/invoice.entity';
import { PaymentDetailsEntity } from '../payment-details/entities/payment-details.entity';
import { Patient } from '../patients/entities/patient.entity';
import { OwnerBrand } from '../owners/entities/owner-brand.entity';
import { RoleModule } from '../roles/role.module';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { ClinicEntity } from '../clinics/entities/clinic.entity';
@Module({
	imports: [
		TypeOrmModule.forFeature([
			InvoiceEntity,
			PaymentDetailsEntity,
			Patient,
			OwnerBrand,
			AppointmentEntity,
			ClinicEntity
		]),
		RoleModule
	],
	controllers: [AnalyticsController],
	providers: [AnalyticsService],
	exports: [AnalyticsService]
})
export class AnalyticsModule {}

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Is<PERSON>num, IsOptional, IsEmail, IsDateString, ValidateIf } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { AnalyticsDocumentType, ShareMode, RecipientType } from './analytics-document-types.enum';

export class ShareAnalyticsDocumentsDto {
  @ApiProperty({ description: 'Clinic ID' })
  @IsString()
  clinicId: string;

  @ApiProperty({ description: 'Start date for analytics period' })
  @IsDateString()
  startDate: string;

  @ApiProperty({ description: 'End date for analytics period' })
  @IsDateString()
  endDate: string;

  @ApiProperty({ 
    description: 'Types of documents to include',
    enum: AnalyticsDocumentType,
    isArray: true
  })
  @IsArray()
  @IsEnum(AnalyticsDocumentType, { each: true })
  documentTypes: AnalyticsDocumentType[];

  @ApiProperty({ 
    description: 'Share modes',
    enum: ShareMode,
    isArray: true
  })
  @IsArray()
  @IsEnum(ShareMode, { each: true })
  shareMode: ShareMode[];

  @ApiProperty({ 
    description: 'Recipient type',
    enum: RecipientType
  })
  @IsEnum(RecipientType)
  recipientType: RecipientType;

  @ApiProperty({ description: 'Email address (required if shareMode includes email)' })
  @ValidateIf(o => o.shareMode.includes(ShareMode.EMAIL))
  @IsEmail()
  email?: string;

  @ApiProperty({ description: 'Phone number (required if shareMode includes whatsapp)' })
  @ValidateIf(o => o.shareMode.includes(ShareMode.WHATSAPP))
  @IsString()
  phoneNumber?: string;

  @ApiProperty({ description: 'Include Excel report', default: true })
  @IsOptional()
  includeExcelReport?: boolean = true;
}

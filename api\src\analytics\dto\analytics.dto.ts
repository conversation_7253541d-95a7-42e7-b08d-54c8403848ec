import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export enum AnalyticsTimeFrame {
	ONE_DAY = '1D',
	ONE_WEEK = '1W',
	ONE_MONTH = '1M',
	ONE_YEAR = '1Y'
}

export enum AnalyticsType {
	REVENUE = 'REVENUE',
	APPOINTMENTS = 'APPOINTMENTS',
	DOCTOR_PERFORMANCE = 'DOCTOR_PERFORMANCE',
	OUTSTANDING_BALANCE = 'OUTSTANDING_BALANCE',
	COLLECTED_PAYMENTS = 'COLLECTED_PAYMENTS'
}

export enum AnalyticsReportType {
	BY_BILLING = 'by-billing',
	BY_PATIENT = 'by-patient'
}

export enum AppointmentAnalyticsType {
	ALL = 'All',
	BUSIEST_DAYS = 'BusiestDays',
	BUSIEST_HOURS = 'BusiestHours',
	AVERAGE_DURATION = 'AverageDuration'
}

export class GetRevenueChartDataDto {
	@ApiProperty({
		description: 'Start date for the chart data'
	})
	@IsString()
	@IsNotEmpty()
	startDate!: string;

	@ApiProperty({
		description: 'End date for the chart data'
	})
	@IsString()
	@IsNotEmpty()
	endDate!: string;

	@ApiProperty({
		description: 'Clinic ID'
	})
	@IsString()
	@IsNotEmpty()
	clinicId!: string;
}

export interface RevenueChartDataPoint {
	date: string;
	products: number;
	services: number;
	diagnostics: number;
	medications: number;
	vaccinations: number;
}

export interface CollectedPaymentsChartDataPoint {
	date: string;
	cash: number;
	card: number;
	wallet: number;
	cheque: number;
	bankTransfer: number;
}

export class DownloadAnalyticsReportDto {
	@ApiProperty({
		enum: AnalyticsType,
		description: 'Type of analytics report'
	})
	@IsEnum(AnalyticsType)
	@IsNotEmpty()
	type!: AnalyticsType;

	@ApiProperty({
		description: 'Start date for the report'
	})
	@IsString()
	@IsNotEmpty()
	startDate!: string;

	@ApiProperty({
		description: 'End date for the report'
	})
	@IsString()
	@IsNotEmpty()
	endDate!: string;

	@ApiProperty({
		description: 'Clinic ID'
	})
	@IsString()
	@IsNotEmpty()
	clinicId!: string;

	@ApiProperty({
		enum: AnalyticsReportType,
		description: 'Report type',
		required: false
	})
	@IsEnum(AnalyticsReportType)
	@IsOptional()
	reportType?: AnalyticsReportType;
}

export class GetAppointmentsChartDataDto {
	@ApiProperty({
		description: 'Start date for the chart data'
	})
	@IsString()
	@IsNotEmpty()
	startDate!: string;

	@ApiProperty({
		description: 'End date for the chart data'
	})
	@IsString()
	@IsNotEmpty()
	endDate!: string;

	@ApiProperty({
		description: 'Clinic ID'
	})
	@IsString()
	@IsNotEmpty()
	clinicId!: string;

	@ApiProperty({
		enum: AppointmentAnalyticsType,
		description: 'Type of analytics data to retrieve'
	})
	@IsEnum(AppointmentAnalyticsType)
	@IsNotEmpty()
	type!: AppointmentAnalyticsType;
}

export interface AppointmentsChartDataPoint {
	date: string;
	total?: number;
	missed?: number;
	averageDuration?: number;
}

export interface AppointmentDurationDataPoint {
	date: string;
	checkinDuration: number; // Time in check-in state
	receivingCareDuration: number; // Time in receiving care state
	checkoutDuration: number; // Time in checkout state
	totalDuration: number; // Total appointment duration
}

export interface AppointmentsChartResponse {
	total: AppointmentsChartDataPoint[];
	missed: AppointmentsChartDataPoint[];
	busiestDays?: {
		day: string;
		count: number;
		weeksCount: number;
		total: number;
	}[];
	busiestHours?: {
		hour: string;
		count: number;
		daysCount: number;
		total: number;
	}[];
	averageDuration?: AppointmentDurationDataPoint[];
}

export class GetDoctorSummaryDto {
	@ApiProperty({
		description: 'Start date for the summary data'
	})
	@IsString()
	@IsNotEmpty()
	startDate!: string;

	@ApiProperty({
		description: 'End date for the summary data'
	})
	@IsString()
	@IsNotEmpty()
	endDate!: string;

	@ApiProperty({
		description: 'Clinic ID'
	})
	@IsString()
	@IsNotEmpty()
	clinicId!: string;
}

export interface DoctorSummaryResponseDto {
	doctorName: string;
	numAppointments: number;
	totalRevenue: number;
	revenuePerAppointment: number;
	avgAppointmentDurationMinutes: number;
}

export class GetSummaryDto {
	@ApiProperty({
		description: 'Start date for the summary data'
	})
	@IsString()
	@IsNotEmpty()
	startDate!: string;

	@ApiProperty({
		description: 'End date for the summary data'
	})
	@IsString()
	@IsNotEmpty()
	endDate!: string;

	@ApiProperty({
		description: 'Clinic ID'
	})
	@IsString()
	@IsNotEmpty()
	clinicId!: string;
}

export interface SummaryResponseDto {
	appointmentsCompleted: number;
	invoicesGenerated: number;
	totalBilling: number;
	creditNotesGenerated: number;
	totalCreditNotes: number;
	amountCollected: {
		cash: number;
		card: number;
		wallet: number;
		cheque: number;
		bankTransfer: number;
		total: number;
	};
	amountRefunded: {
		cash: number;
		card: number;
		wallet: number;
		cheque: number;
		bankTransfer: number;
		total: number;
	};
	badDebts: number;
}

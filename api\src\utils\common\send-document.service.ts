import {
	HttpException,
	HttpStatus,
	Injectable,
	NotFoundException
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { WinstonLogger } from '../logger/winston-logger.service';
import { In, IsNull, Repository, Equal, LessThan } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { AppointmentEntity } from '../../appointments/entities/appointment.entity';
import { S3Service } from '../aws/s3/s3.service';
import { SESMailService } from '../aws/ses/send-mail-service';
import { WhatsappService } from '../whatsapp-integration/whatsapp.service';
import { isProduction, isProductionOrUat } from './get-login-url';
import { DEV_SES_EMAIL } from '../constants';
import axios from 'axios';
import moment = require('moment');
import {
	getAppointmentInvoiceTemplateData,
	getAppointmentInvoiceClinicLinkTemplateData,
	getCreditNoteGenerationTemplateData,
	getIndividualEMRTemplateData,
	getIndividualEMRClinicLinkTemplateData,
	getLedgerDocumentTemplateData,
	getLedgerDocumentClinicLinkTemplateData,
	getReceiptGenerationTemplateData,
	getReceiptGenerationClinicLinkTemplateData,
	getVaccinationImageTemplateData,
	getVaccinationImageClinicLinkTemplateData,
	reminderNotifications,
	reminderNotificationsClinicLink,
	sendDiagnosticReportDocument,
	sendDiagnosticReportImage,
	sendMedicalRecordDocument,
	sendMedicalRecordImage,
	getCreditNoteGenerationClinicLinkTemplateData
} from '../communicatoins/whatsapp-template-generator';
import { EnumInvoiceType } from '../../invoice/enums/enum-invoice-types';
import { generatePDFFromImage } from '../generatePdfFromImage';
import { mergePDFs, PDFInput } from '../merge-pdf-image-into';
import { uuidv4 } from 'uuidv7';
import { PatientVaccination } from '../../patient-vaccinations/entities/patient-vaccinations.entity';
import { Patient } from '../../patients/entities/patient.entity';
import { Emr } from '../../emr/entities/emr.entity';
import { PaymentDetailsEntity } from '../../payment-details/entities/payment-details.entity';
import { compressPdfBuffer } from '../pdf-compressor';
import { LabReport } from '../../clinic-lab-report/entities/lab-report.entity';
import { PatientReminder } from '../../patient-reminders/entities/patient-reminder.entity';
import { reminderNotificationTemplate } from '../mail-generator/mail-template-generator';
import { AppointmentDetailsEntity } from '../../appointments/entities/appointment-details.entity';
import { InvoiceEntity } from '../../invoice/entities/invoice.entity';
import { generatePrescription } from '../pdfs/new/generatePrescription';
import { AppointmentDoctorsEntity } from '../../appointments/entities/appointment-doctor.entity';
import { generatePDF } from '../generatePdf';
import * as crypto from 'crypto';
import { MergedInvoiceDocumentEntity } from '../../invoice/entities/merged-invoice-document.entity';
import { EnumAmountType } from '../../payment-details/enums/enum-credit-types';
import {
	generateInvoice as generateNewInvoice,
	InvoiceData
} from '../pdfs/new/generateInvoice';
import {
	generateCreditNote as generateNewCreditNote,
	CreditNoteData
} from '../pdfs/new/generateCreditNote';
import { PDFDocument } from 'pdf-lib';

import { selectTemplate } from '../common/template-helper.util';
import {
	MergedPaymentReceiptDocumentEntity,
	MergedDocumentStatus
} from '../../payment-details/entities/merged-payment-receipt-document.entity';

import {
	generateRefundReceipt,
	ReceiptData as RefundReceiptData
} from '../pdfs/new/generateRefundReceipt';
import {
	generatePaymentReceipt,
	ReceiptData as PaymentReceiptData
} from '../pdfs/new/generatePaymentReceipt';
import { EnumPaymentType } from '../../invoice/enums/enum-payment-types';
import { OwnerBrand } from '../../owners/entities/owner-brand.entity';
import { ClinicEntity } from '../../clinics/entities/clinic.entity';
import { TabActivitiesService } from '../../tab-activity/tab-activity.service';
import { TabName, ActionType } from '../../tab-activity/enums/tab-activity.enums';

export const MAX_SIZE_5MB = 5 * 1024 * 1024; // 5 MB in bytes

interface FileKeyEntry {
	fileKey: string;
	type: 'Receipt' | 'Invoice' | 'CreditNote';
}

// New Interfaces for shareStatements method
interface StatementOwnerDetails {
	name: string;
	email?: string;
	phoneNumber?: string;
	countryCode?: string;
}

interface StatementShareDetails {
	shareMethod: 'email' | 'whatsapp' | 'both';
	recipient: 'client' | 'other';
	email?: string;
	phoneNumber?: string;
	countryCode?: string;
}

interface StatementUserContext {
	brandName: string;
	clinicContact: string;
}

@Injectable()
export class SendDocuments {
	constructor(
		private configService: ConfigService,
		private logger: WinstonLogger,
		@InjectRepository(AppointmentEntity)
		private readonly appointmentRepository: Repository<AppointmentEntity>,
		private s3Service: S3Service,
		private readonly mailService: SESMailService,
		private readonly whatsappService: WhatsappService,
		@InjectRepository(PatientVaccination)
		private readonly patientVaccinationRepository: Repository<PatientVaccination>,
		@InjectRepository(Patient)
		private readonly patientRepository: Repository<Patient>,
		@InjectRepository(Emr)
		private readonly emrRepository: Repository<Emr>,
		@InjectRepository(PaymentDetailsEntity)
		private readonly paymentDetailsRepository: Repository<PaymentDetailsEntity>,
		@InjectRepository(LabReport)
		private readonly labReportRepository: Repository<LabReport>,
		@InjectRepository(AppointmentDetailsEntity)
		private appointmentDetailsEntity: Repository<AppointmentDetailsEntity>,
		@InjectRepository(InvoiceEntity)
		private invoiceEntity: Repository<InvoiceEntity>,
		@InjectRepository(AppointmentDoctorsEntity)
		private appointmentDoctorsEntity: Repository<AppointmentDoctorsEntity>,
		@InjectRepository(MergedInvoiceDocumentEntity)
		private mergedInvoiceDocumentRepository: Repository<MergedInvoiceDocumentEntity>,
		@InjectRepository(InvoiceEntity)
		private readonly invoiceRepository: Repository<InvoiceEntity>,
		@InjectRepository(MergedPaymentReceiptDocumentEntity)
		private readonly mergedPaymentReceiptDocumentRepository: Repository<MergedPaymentReceiptDocumentEntity>,
		private readonly tabActivitiesService: TabActivitiesService
	) {}
	async downloadPDF(url: string): Promise<Buffer> {
		const response = await axios.get(url, { responseType: 'arraybuffer' });
		return Buffer.from(response.data);
	}
	async sendMail(
		body: string,
		buffers: Buffer[],
		fileName: string[],
		email: string,
		subject?: string
	) {
		try {
			if (isProduction() && email) {
				this.mailService.sendMail({
					body: body,
					subject: subject ?? 'Invoice attachments',
					pdfBuffers: buffers,
					pdfFileNames: fileName,
					toMailAddress: email
				});
			} else if (!isProduction()) {
				this.mailService.sendMail({
					body: body,
					subject: subject ?? 'Invoice attachments',
					pdfBuffers: buffers,
					pdfFileNames: fileName,
					toMailAddress: DEV_SES_EMAIL //email
				});
			}
		} catch (error) {
			this.logger.log('eroor => ', error);
		}
	}
	async sendInvoiceDocument(
		appointmentId: string,
		shareMode: Array<string>,
		recipientType?: 'client' | 'other',
		email?: string,
		phoneNumber?: string
	) {
		this.logger.log(
			'start finding invoice-fileKey for appointmentId',
			appointmentId
		);
		const response: any = await this.appointmentRepository.findOne({
			where: { id: appointmentId, deletedAt: IsNull() },
			relations: [
				'cart',
				'cart.invoice',
				'patient',
				'patient.patientOwners',
				'patient.patientOwners.ownerBrand',
				'patient.patientOwners.ownerBrand.globalOwner',
				'clinic',
				'clinic.brand'
			],
			order: { date: 'DESC', startTime: 'DESC' }
		});

		const brandName = response?.clinic?.brand?.name || '';
		const patientName = response?.patient?.patientName || '';
		const invoiceFileKey =
			response?.cart?.invoice?.find(
				(inv: any) => inv.invoiceType === EnumInvoiceType.Invoice
			)?.fileUrl ?? null;
		const phoneNumbers = response?.clinic?.phoneNumbers;
		const clinicContact =
			`+${phoneNumbers?.[0]?.country_code ?? ''} ${phoneNumbers?.[0]?.number ?? ''}`.trim();
		try {
			if (invoiceFileKey?.invoiceFileKey) {
				const emrUrl = await this.s3Service.getViewPreSignedUrl(
					invoiceFileKey?.invoiceFileKey
				);
				response?.patient?.patientOwners.forEach(
					async (patient: any) => {
						const clientName = `${patient?.ownerBrand?.firstName || ''} ${patient?.ownerBrand?.lastName || ''}`;

						if (
							shareMode.includes('email') &&
							patient?.ownerBrand?.email
						) {
							const pdfBuffer = await this.downloadPDF(emrUrl);
							this.sendMail(
								`Dear ${clientName},
							<br/><br/>
							We've attached ${patientName}’s medical records here. <br/><br/>
							If you have any questions, feel free to reach out to us at ${clinicContact}.We look forward to seeing you seeing you and ensuring you receive the best possible care!

							<br/>
							<br/>
							Warm regards,<br/>
							The ${brandName} Team`,
								[pdfBuffer],
								['medical-record-invoice.pdf'],
								patient?.ownerBrand?.email ??
									'<EMAIL>',
								`Your Pet's Medical Records Ready`
							);
							this.logger.log(
								'mail send successfully',
								appointmentId
							);
						}
						if (
							shareMode.includes('whatsapp') &&
							patient?.ownerBrand?.globalOwner?.phoneNumber
						) {
							// Generate template data
							const templateArgs = {
								brandName,
								clientName,
								EMRFile: emrUrl,
								mobileNumber: `${patient?.ownerBrand?.globalOwner?.countryCode}${patient?.ownerBrand?.globalOwner?.phoneNumber}`,
								petName: patientName,
								clinicContact
							};
							const { mobileNumber, templateName, valuesArray } =
								selectTemplate(
									patient?.clinic,
									templateArgs,
									getIndividualEMRTemplateData,
									getIndividualEMRClinicLinkTemplateData
								);

							const response =
								await this.whatsappService.sendTemplateMessage({
									templateName,
									valuesArray,
									mobileNumber
								});
							this.logger.log(
								'whatsapp message sent successfully'
							);
						}
					}
				);
			} else {
				this.logger.log('invoice documents not found', appointmentId);
				throw new HttpException(
					'invoice document not found',
					HttpStatus.NOT_FOUND
				);
			}
		} catch (err) {
			this.logger.log('something went wrong', appointmentId);

			throw new HttpException(
				'Something went wrong, please try again later.',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	// Method to get appointment details needed for sending documents
	async getAppointmentDetails(appointmentId: string) {
		try {
			const appointment = await this.appointmentRepository.findOne({
				where: { id: appointmentId },
				relations: [
					'patient',
					'clinic',
					'patient.patientOwners',
					'patient.patientOwners.ownerBrand'
				]
			});

			if (!appointment) {
				this.logger.error('Appointment not found', { appointmentId });
				return null;
			}

			const ownerBrand =
				appointment.patient?.patientOwners?.[0]?.ownerBrand;

			return {
				clientName: `${ownerBrand?.firstName || ''} ${ownerBrand?.lastName || ''}`,
				patientName: appointment.patient?.patientName || '',
				clinicContact: appointment.clinic?.phoneNumbers?.[0]
					? `+${appointment.clinic.phoneNumbers[0].country_code || ''} ${appointment.clinic.phoneNumbers[0].number || ''}`
					: '',
				brandName: appointment.clinic?.brand?.name || '',
				clientEmail: ownerBrand?.email || '',
				appointmentId
			};
		} catch (error) {
			this.logger.error('Error fetching appointment details', {
				error,
				appointmentId
			});
			return null;
		}
	}

	// Method to send all documents in a single email
	async sendAllDocumentsInOneEmail(
		appointmentDetails: {
			clientName: string;
			patientName: string;
			clinicContact: string;
			brandName: string;
			clientEmail: string;
			appointmentId: string;
		},
		allBuffers: Buffer[],
		allFileNames: string[]
	) {
		const {
			clientName,
			patientName,
			clinicContact,
			brandName,
			clientEmail
		} = appointmentDetails;

		if (!clientEmail) {
			this.logger.error('No email address found for patient', {
				appointmentId: appointmentDetails.appointmentId
			});
			return;
		}

		try {
			const emailSubject = `Medical Documents from ${brandName}`;

			// Create email body
			const emailBody = `
			<p>Dear ${clientName},</p>
			<p>Please find attached the requested medical documents for ${patientName}.</p>
			<p>If you have any questions, please contact us at ${clinicContact}.</p>
			<p>Regards,<br>${brandName} Team</p>
			`;

			await this.sendMail(
				emailBody,
				allBuffers,
				allFileNames,
				clientEmail,
				emailSubject
			);

			this.logger.log('All documents sent in a single email', {
				recipientEmail: clientEmail,
				documentCount: allBuffers.length
			});
		} catch (error) {
			this.logger.error(
				'Failed to send consolidated email with all documents',
				{
					error,
					clientEmail,
					documentCount: allBuffers.length
				}
			);
		}
	}

	// Methods to get document files without sending them immediately

	async getInvoiceDocumentFiles(appointmentId: string) {
		try {
			const appointment = await this.appointmentRepository.findOne({
				where: { id: appointmentId },
				relations: ['patient', 'brand', 'clinic']
			});

			if (!appointment) {
				return { buffers: [], fileNames: [] };
			}

			// Use a raw query option since appointmentId is not recognized in the entity
			const invoices = await this.invoiceEntity
				.createQueryBuilder('invoice')
				.where('invoice.appointment_id = :appointmentId', {
					appointmentId
				})
				.getMany();

			if (!invoices || invoices.length === 0) {
				return { buffers: [], fileNames: [] };
			}

			const buffers: Buffer[] = [];
			const fileNames: string[] = [];

			for (const invoice of invoices) {
				// Skip null/undefined fileUrl
				if (!invoice.fileUrl) continue;

				try {
					// Type as any to bypass TypeScript checking for this specific property access
					const fileUrlObj: any = invoice.fileUrl;

					// Check if the object has the property we need
					if (
						fileUrlObj &&
						typeof fileUrlObj === 'object' &&
						'invoiceFileKey' in fileUrlObj
					) {
						const fileKey = String(fileUrlObj['invoiceFileKey']);
						const pdfUrl =
							await this.s3Service.getSignedUrl(fileKey);
						if (typeof pdfUrl === 'string') {
							const pdfBuffer = await this.downloadPDF(pdfUrl);

							buffers.push(pdfBuffer);
							fileNames.push(
								`Invoice_${appointment.patientId}.pdf`
							);
						}
					}
				} catch (error) {
					// Type as any for error logging as well
					const fileUrlObj: any = invoice.fileUrl;
					this.logger.error('Error downloading invoice PDF', {
						error,
						fileKey: fileUrlObj?.invoiceFileKey
					});
				}
			}

			return { buffers, fileNames };
		} catch (error) {
			this.logger.error('Error getting invoice documents', {
				error,
				appointmentId
			});
			return { buffers: [], fileNames: [] };
		}
	}

	async getDiagnosticDocumentFiles(appointmentId: string) {
		try {
			const appointment = await this.appointmentRepository.findOne({
				where: { id: appointmentId },
				relations: ['patient', 'brand', 'clinic']
			});

			if (!appointment) {
				return { buffers: [], fileNames: [] };
			}

			const emr = await this.emrRepository.findOne({
				where: { appointmentId }
			});

			// Since diagnosticFileKey is not in EMR entity, we need to look elsewhere
			// This might be in a custom field or metadata - check app structure
			// For now, let's create a safe check
			if (!emr) {
				return { buffers: [], fileNames: [] };
			}

			// Assuming the EMR might have a diagnostic file stored elsewhere
			// We should find the diagnostic file from another source
			const diagnosticFiles =
				await this.findDiagnosticFiles(appointmentId);
			if (!diagnosticFiles || diagnosticFiles.length === 0) {
				return { buffers: [], fileNames: [] };
			}

			const buffers: Buffer[] = [];
			const fileNames: string[] = [];

			for (const file of diagnosticFiles) {
				try {
					const pdfUrl = await this.s3Service.getSignedUrl(
						file.fileKey
					);
					if (typeof pdfUrl === 'string') {
						const pdfBuffer = await this.downloadPDF(pdfUrl);

						buffers.push(pdfBuffer);
						fileNames.push(
							`Diagnostic_${appointment.patientId}_${file.name || 'report'}.pdf`
						);
					}
				} catch (error) {
					this.logger.error('Error downloading diagnostic PDF', {
						error,
						fileKey: file.fileKey
					});
				}
			}

			return { buffers, fileNames };
		} catch (error) {
			this.logger.error('Error getting diagnostic documents', {
				error,
				appointmentId
			});
			return { buffers: [], fileNames: [] };
		}
	}

	async getDiagnosticTabDocumentFiles(appointmentId: string) {
		try {
			const labReport = await this.labReportRepository.findOne({
				where: { id: appointmentId }
			});

			if (!labReport) {
				return { buffers: [], fileNames: [] };
			}

			// LabReport has files array, not a fileKey
			if (
				!labReport.files ||
				!Array.isArray(labReport.files) ||
				labReport.files.length === 0
			) {
				return { buffers: [], fileNames: [] };
			}

			const buffers: Buffer[] = [];
			const fileNames: string[] = [];

			for (const file of labReport.files) {
				if (file && typeof file === 'object' && 'fileKey' in file) {
					try {
						const fileKey = String(file.fileKey);
						const pdfUrl =
							await this.s3Service.getSignedUrl(fileKey);
						if (typeof pdfUrl === 'string') {
							const pdfBuffer = await this.downloadPDF(pdfUrl);

							buffers.push(pdfBuffer);
							const fileName =
								file.fileName ||
								`LabReport_${labReport.patientId}.pdf`;
							fileNames.push(fileName);
						}
					} catch (error) {
						this.logger.error('Error downloading lab report PDF', {
							error,
							fileKey: file.fileKey
						});
					}
				}
			}

			return { buffers, fileNames };
		} catch (error) {
			this.logger.error('Error getting lab report document', {
				error,
				appointmentId
			});
			return { buffers: [], fileNames: [] };
		}
	}

	async getDiagnosticNotesFiles(
		appointmentId: string,
		fileKeys: string[] = []
	) {
		if (!fileKeys || fileKeys.length === 0) {
			return { buffers: [], fileNames: [] };
		}

		const buffers: Buffer[] = [];
		const fileNames: string[] = [];

		for (const fileKey of fileKeys) {
			try {
				const pdfUrl = await this.s3Service.getSignedUrl(fileKey);
				if (typeof pdfUrl === 'string') {
					const pdfBuffer = await this.downloadPDF(pdfUrl);

					// Extract filename from fileKey
					const fileName =
						fileKey.split('/').pop() ||
						`DiagnosticNote_${Date.now()}.pdf`;

					buffers.push(pdfBuffer);
					fileNames.push(fileName);
				}
			} catch (error) {
				this.logger.error('Error downloading diagnostic note PDF', {
					error,
					fileKey
				});
			}
		}

		return { buffers, fileNames };
	}

	async getVaccinationDocumentFiles(appointmentId: string) {
		try {
			const patientVaccinations =
				await this.patientVaccinationRepository.find({
					where: { appointmentId }
				});

			if (!patientVaccinations || patientVaccinations.length === 0) {
				return { buffers: [], fileNames: [] };
			}

			const buffers: Buffer[] = [];
			const fileNames: string[] = [];

			for (const vaccination of patientVaccinations) {
				// PatientVaccination has reportUrl, not fileKey
				if (vaccination.reportUrl) {
					try {
						const pdfUrl = await this.s3Service.getSignedUrl(
							vaccination.reportUrl
						);
						if (typeof pdfUrl === 'string') {
							const pdfBuffer = await this.downloadPDF(pdfUrl);

							buffers.push(pdfBuffer);
							fileNames.push(
								`Vaccination_${vaccination.vaccineName || 'record'}.pdf`
							);
						}
					} catch (error) {
						this.logger.error('Error downloading vaccination PDF', {
							error,
							fileKey: vaccination.reportUrl
						});
					}
				}
			}

			return { buffers, fileNames };
		} catch (error) {
			this.logger.error('Error getting vaccination documents', {
				error,
				appointmentId
			});
			return { buffers: [], fileNames: [] };
		}
	}

	async sendDiagnosticDocument(
		appointmentId: string,
		shareMode: Array<string>,
		recipientType?: 'client' | 'other',
		email?: string,
		phoneNumber?: string
	) {
		this.logger.log(
			'start finding invoice-fileKey for appointmentId',
			appointmentId
		);
		const response: any = await this.appointmentRepository.findOne({
			where: { id: appointmentId, deletedAt: IsNull() },
			relations: [
				'appointmentDetails',
				'patient',
				'patient.patientOwners',
				'patient.patientOwners.ownerBrand',
				'patient.patientOwners.ownerBrand.globalOwner',
				'clinic',
				'clinic.brand'
			],
			order: { date: 'DESC', startTime: 'DESC' }
		});

		const brandName = response?.clinic?.brand?.name || '';
		const patientName = response?.patient?.patientName || '';
		const phoneNumbers = response?.clinic?.phoneNumbers;
		const clinicContact =
			`+${phoneNumbers?.[0]?.country_code ?? ''} ${phoneNumbers?.[0]?.number ?? ''}`.trim();

		const allFiles =
			response.appointmentDetails.details.objective.labReports
				.map((diagnostic: any) => diagnostic.files)
				.filter((files: any) => files && files.length > 0)
				.flat();
		if (allFiles?.length > 0) {
			const pdfInput: Array<any> = [];
			try {
				await Promise.all(
					allFiles.map(async (file: any) => {
						const viewSignedUrl =
							await this.s3Service.getViewPreSignedUrl(
								file.fileKey
							);
						pdfInput.push({
							url: viewSignedUrl,
							name: file?.fileName,
							isPdf: file?.fileName?.includes('.pdf')
						});
					})
				);

				const documentNameArray = pdfInput.map(file => file.name);
				const pdfBuffers: Buffer[] = await Promise.all(
					pdfInput.map(async file => {
						return await this.downloadPDF(file.url);
					})
				);

				response?.patient?.patientOwners.forEach(
					async (patient: any) => {
						const clientName = `${patient?.ownerBrand?.firstName || ''} ${patient?.ownerBrand?.lastName || ''}`;

						if (
							shareMode.includes('email') &&
							patient?.ownerBrand?.email
						) {
							this.sendMail(
								`Dear ${clientName},
							<br/><br/>
							We've attached ${patientName}’s medical records here. <br/><br/>
							If you have any questions, feel free to reach out to us at ${clinicContact}.We look forward to seeing you seeing you and ensuring you receive the best possible care!

							<br/>
							<br/>
							Warm regards,<br/>
							The ${brandName} Team`,
								[...pdfBuffers],
								[...documentNameArray],
								patient?.ownerBrand?.email ??
									'<EMAIL>',
								`Your Pet's Medical Records Ready`
							);
							this.logger.log(
								'email sent successfully for patient',
								{ appointmentId, patientName }
							);
						}
						if (
							shareMode.includes('whatsapp') &&
							patient?.ownerBrand?.globalOwner?.phoneNumber
						) {
							pdfInput.map(async (file, idx) => {
								if (file.isPdf) {
									const {
										templateName,
										valuesArray,
										mobileNumber
									} = sendDiagnosticReportDocument({
										clientName,
										documentUrl: file.url,
										mobileNumber: `${patient?.ownerBrand?.globalOwner?.countryCode}${patient?.ownerBrand?.globalOwner?.phoneNumber}`,
										petName: patientName
									});
									const response =
										await this.whatsappService.sendTemplateMessage(
											{
												templateName,
												valuesArray,
												mobileNumber
											}
										);
									this.logger.log(
										'whatsapp mail sent successfully'
									);
								} else {
									const {
										templateName,
										valuesArray,
										mobileNumber
									} = sendDiagnosticReportImage({
										clientName,
										documentUrl: file.url,
										mobileNumber: `${patient?.ownerBrand?.globalOwner?.countryCode}${patient?.ownerBrand?.globalOwner?.phoneNumber}`,
										petName: patientName
									});
									const response =
										await this.whatsappService.sendTemplateMessage(
											{
												templateName,
												valuesArray,
												mobileNumber
											}
										);
									this.logger.log(
										'whatsapp mail sent successfully'
									);
								}
							});
						}
					}
				);
			} catch (error) {
				this.logger.log(
					'some error arises while merging or sending pdf',
					error
				);
				throw new HttpException(
					'Something went wrong, please try again later.',
					HttpStatus.INTERNAL_SERVER_ERROR
				);
			}
		} else {
			this.logger.log('documents not found');
			throw new HttpException(
				'supporting document not found',
				HttpStatus.NOT_FOUND
			);
		}
	}

	async sendDiagnosticTabDocument(
		labReportId: string,
		shareMode: Array<string>,
		fileKeys?: string[],
		recipientType?: 'client' | 'other',
		email?: string,
		phoneNumber?: string
	) {
		this.logger.log(
			'start finding diagnostic lab report fileKey for labReportId',
			labReportId
		);
		const response = await this.labReportRepository.findOne({
			where: { id: labReportId },
			relations: [
				'patient',
				'patient.patientOwners',
				'patient.patientOwners.ownerBrand',
				'patient.patientOwners.ownerBrand.globalOwner',
				'clinic',
				'clinic.brand'
			]
		});

		const brandName = response?.clinic?.brand?.name || '';
		const patientName = response?.patient?.patientName || '';
		const phoneNumbers = response?.clinic?.phoneNumbers;
		const clinicContact =
			`+${phoneNumbers?.[0]?.country_code ?? ''} ${phoneNumbers?.[0]?.number ?? ''}`.trim();

		// If fileKeys are provided, use them instead of response.files
		if (fileKeys && fileKeys.length > 0) {
			const pdfInput: Array<any> = [];
			try {
				await Promise.all(
					fileKeys.map(async (fileKey: string) => {
						const viewSignedUrl =
							await this.s3Service.getViewPreSignedUrl(fileKey);
						pdfInput.push({
							url: viewSignedUrl,
							name: fileKey.split('/').pop() || fileKey,
							isPdf: fileKey.toLowerCase().includes('.pdf')
						});
					})
				);

				const documentNameArray = pdfInput.map(file => file.name);
				const pdfBuffers: Buffer[] = await Promise.all(
					pdfInput.map(async file => {
						return await this.downloadPDF(file.url);
					})
				);

				// If recipient type is 'other', use the provided email and phone number
				if (recipientType === 'other') {
					// For custom recipient (email)
					if (shareMode.includes('email') && email) {
						this.sendMail(
							`Dear Recipient,
						<br/><br/>
						We've attached diagnostic records here. <br/><br/>
						If you have any questions, feel free to reach out to us at ${clinicContact}. We look forward to seeing you and ensuring you receive the best possible care!

						<br/>
						<br/>
						Warm regards,<br/>
						The ${brandName} Team`,
							[...pdfBuffers],
							[...documentNameArray],
							email,
							`Diagnostic Records Ready`
						);
						this.logger.log(
							'Email sent successfully to custom recipient'
						);
					}

					// For custom recipient (WhatsApp)
					if (shareMode.includes('whatsapp') && phoneNumber) {
						pdfInput.map(async (file, idx) => {
							if (file.isPdf) {
								const {
									templateName,
									valuesArray,
									mobileNumber
								} = sendDiagnosticReportDocument({
									clientName: 'Recipient',
									documentUrl: file.url,
									mobileNumber: phoneNumber,
									petName: patientName
								});
								await this.whatsappService.sendTemplateMessage({
									templateName,
									valuesArray,
									mobileNumber
								});
								this.logger.log(
									'WhatsApp message sent successfully to custom recipient'
								);
							} else {
								const {
									templateName,
									valuesArray,
									mobileNumber
								} = sendDiagnosticReportImage({
									clientName: 'Recipient',
									documentUrl: file.url,
									mobileNumber: phoneNumber,
									petName: patientName
								});
								await this.whatsappService.sendTemplateMessage({
									templateName,
									valuesArray,
									mobileNumber
								});
								this.logger.log(
									'WhatsApp message sent successfully to custom recipient'
								);
							}
						});
					}
				} else {
					// For patient owner (default recipient type 'client')
					response?.patient?.patientOwners.forEach(
						async (patient: any) => {
							const clientName = `${patient?.ownerBrand?.firstName || ''} ${patient?.ownerBrand?.lastName || ''}`;

							if (
								shareMode.includes('email') &&
								patient?.ownerBrand?.email
							) {
								this.sendMail(
									`Dear ${clientName},
								<br/><br/>
								We've attached ${patientName}'s medical records here. <br/><br/>
								If you have any questions, feel free to reach out to us at ${clinicContact}. We look forward to seeing you and ensuring you receive the best possible care!

								<br/>
								<br/>
								Warm regards,<br/>
								The ${brandName} Team`,
									[...pdfBuffers],
									[...documentNameArray],
									patient?.ownerBrand?.email,
									`Your Pet's Medical Records Ready`
								);
								this.logger.log(
									'Email sent successfully for patient',
									{ labReportId, patientName }
								);
							}
							if (
								shareMode.includes('whatsapp') &&
								patient?.ownerBrand?.globalOwner?.phoneNumber
							) {
								pdfInput.map(async (file, idx) => {
									if (file.isPdf) {
										const {
											templateName,
											valuesArray,
											mobileNumber
										} = sendDiagnosticReportDocument({
											clientName,
											documentUrl: file.url,
											mobileNumber: `${patient?.ownerBrand?.globalOwner?.countryCode}${patient?.ownerBrand?.globalOwner?.phoneNumber}`,
											petName: patientName
										});
										const response =
											await this.whatsappService.sendTemplateMessage(
												{
													templateName,
													valuesArray,
													mobileNumber
												}
											);
										this.logger.log(
											'WhatsApp message sent successfully'
										);
									} else {
										const {
											templateName,
											valuesArray,
											mobileNumber
										} = sendDiagnosticReportImage({
											clientName,
											documentUrl: file.url,
											mobileNumber: `${patient?.ownerBrand?.globalOwner?.countryCode}${patient?.ownerBrand?.globalOwner?.phoneNumber}`,
											petName: patientName
										});
										const response =
											await this.whatsappService.sendTemplateMessage(
												{
													templateName,
													valuesArray,
													mobileNumber
												}
											);
										this.logger.log(
											'WhatsApp message sent successfully'
										);
									}
								});
							}
						}
					);
				}
			} catch (error) {
				this.logger.log(
					'Some error occurred while merging or sending pdf',
					error
				);
				throw new HttpException(
					'Something went wrong, please try again later.',
					HttpStatus.INTERNAL_SERVER_ERROR
				);
			}
		} else if (response?.files) {
			// Original code for handling response.files
			const pdfInput: Array<any> = [];
			try {
				await Promise.all(
					response.files.map(async (file: any) => {
						const viewSignedUrl =
							await this.s3Service.getViewPreSignedUrl(
								file.fileKey
							);
						pdfInput.push({
							url: viewSignedUrl,
							name: file?.fileName,
							isPdf: file?.fileName?.includes('.pdf')
						});
					})
				);

				const documentNameArray = pdfInput.map(file => file.name);
				const pdfBuffers: Buffer[] = await Promise.all(
					pdfInput.map(async file => {
						return await this.downloadPDF(file.url);
					})
				);

				// If recipient type is 'other', use the provided email and phone number
				if (recipientType === 'other') {
					// For custom recipient (email)
					if (shareMode.includes('email') && email) {
						this.sendMail(
							`Dear Recipient,
						<br/><br/>
						We've attached diagnostic records here. <br/><br/>
						If you have any questions, feel free to reach out to us at ${clinicContact}. We look forward to seeing you and ensuring you receive the best possible care!

						<br/>
						<br/>
						Warm regards,<br/>
						The ${brandName} Team`,
							[...pdfBuffers],
							[...documentNameArray],
							email,
							`Diagnostic Records Ready`
						);
						this.logger.log(
							'Email sent successfully to custom recipient'
						);
					}

					// For custom recipient (WhatsApp)
					if (shareMode.includes('whatsapp') && phoneNumber) {
						pdfInput.map(async (file, idx) => {
							if (file.isPdf) {
								const {
									templateName,
									valuesArray,
									mobileNumber
								} = sendDiagnosticReportDocument({
									clientName: 'Recipient',
									documentUrl: file.url,
									mobileNumber: phoneNumber,
									petName: patientName
								});
								await this.whatsappService.sendTemplateMessage({
									templateName,
									valuesArray,
									mobileNumber
								});
								this.logger.log(
									'WhatsApp message sent successfully to custom recipient'
								);
							} else {
								const {
									templateName,
									valuesArray,
									mobileNumber
								} = sendDiagnosticReportImage({
									clientName: 'Recipient',
									documentUrl: file.url,
									mobileNumber: phoneNumber,
									petName: patientName
								});
								await this.whatsappService.sendTemplateMessage({
									templateName,
									valuesArray,
									mobileNumber
								});
								this.logger.log(
									'WhatsApp message sent successfully to custom recipient'
								);
							}
						});
					}
				} else {
					// For patient owner (default)
					response?.patient?.patientOwners.forEach(
						async (patient: any) => {
							const clientName = `${patient?.ownerBrand?.firstName || ''} ${patient?.ownerBrand?.lastName || ''}`;

							if (
								shareMode.includes('email') &&
								patient?.ownerBrand?.email
							) {
								this.sendMail(
									`Dear ${clientName},
								<br/><br/>
								We've attached ${patientName}'s medical records here. <br/><br/>
								If you have any questions, feel free to reach out to us at ${clinicContact}. We look forward to seeing you and ensuring you receive the best possible care!

								<br/>
								<br/>
								Warm regards,<br/>
								The ${brandName} Team`,
									[...pdfBuffers],
									[...documentNameArray],
									patient?.ownerBrand?.email,
									`Your Pet's Medical Records Ready`
								);
								this.logger.log(
									'Email sent successfully for patient',
									{ labReportId, patientName }
								);
							}
							if (
								shareMode.includes('whatsapp') &&
								patient?.ownerBrand?.globalOwner?.phoneNumber
							) {
								pdfInput.map(async (file, idx) => {
									if (file.isPdf) {
										const {
											templateName,
											valuesArray,
											mobileNumber
										} = sendDiagnosticReportDocument({
											clientName,
											documentUrl: file.url,
											mobileNumber: `${patient?.ownerBrand?.globalOwner?.countryCode}${patient?.ownerBrand?.globalOwner?.phoneNumber}`,
											petName: patientName
										});
										const response =
											await this.whatsappService.sendTemplateMessage(
												{
													templateName,
													valuesArray,
													mobileNumber
												}
											);
										this.logger.log(
											'WhatsApp message sent successfully'
										);
									} else {
										const {
											templateName,
											valuesArray,
											mobileNumber
										} = sendDiagnosticReportImage({
											clientName,
											documentUrl: file.url,
											mobileNumber: `${patient?.ownerBrand?.globalOwner?.countryCode}${patient?.ownerBrand?.globalOwner?.phoneNumber}`,
											petName: patientName
										});
										const response =
											await this.whatsappService.sendTemplateMessage(
												{
													templateName,
													valuesArray,
													mobileNumber
												}
											);
										this.logger.log(
											'WhatsApp message sent successfully'
										);
									}
								});
							}
						}
					);
				}
			} catch (error) {
				this.logger.log(
					'Some error occurred while merging or sending pdf',
					error
				);
				throw new HttpException(
					'Something went wrong, please try again later.',
					HttpStatus.INTERNAL_SERVER_ERROR
				);
			}
		} else {
			this.logger.log('documents not found');
			throw new HttpException(
				'supporting document not found',
				HttpStatus.NOT_FOUND
			);
		}
	}

	async sendVaccinationDocument(
		appointmentId: string,
		shareMode: Array<string>,
		recipientType?: 'client' | 'other',
		email?: string,
		phoneNumber?: string
	) {
		this.logger.log('vaccination documents service is callled');
		const patientVaccinationResponse: any =
			await this.patientVaccinationRepository.find({
				where: { appointmentId }
			});

		const appointmentResponse: any =
			await this.appointmentRepository.findOne({
				where: { id: appointmentId, deletedAt: IsNull() },
				relations: [
					'patient',
					'patient.patientOwners',
					'patient.patientOwners.ownerBrand',
					'patient.patientOwners.ownerBrand.globalOwner',
					'clinic',
					'clinic.brand'
				],
				order: { date: 'DESC', startTime: 'DESC' }
			});

		const brandName = appointmentResponse?.clinic?.brand?.name || '';
		const patientName = appointmentResponse?.patient?.patientName || '';
		const phoneNumbers = appointmentResponse?.clinic?.phoneNumbers;
		const clinicContact =
			`+${phoneNumbers?.[0]?.country_code ?? ''} ${phoneNumbers?.[0]?.number ?? ''}`.trim();

		const allFiles = patientVaccinationResponse
			.map((vaccination: any) => {
				if (vaccination.reportUrl) {
					return {
						fileKey: vaccination.reportUrl,
						fileName: vaccination.urlMeta?.fileName
					};
				} else return null;
			})
			.filter((file: any) => file !== null);

		if (allFiles.length > 0) {
			const pdfInput: Array<any> = [];
			try {
				await Promise.all(
					allFiles.map(async (file: any) => {
						const viewSignedUrl =
							await this.s3Service.getViewPreSignedUrl(
								file.fileKey
							);
						pdfInput.push({
							url: viewSignedUrl,
							name: file?.fileName,
							isPdf: file?.fileName?.includes('.pdf')
						});
					})
				);

				const documentNameArray = pdfInput.map(file => file.name);
				const pdfBuffers: Buffer[] = await Promise.all(
					pdfInput.map(async file => {
						return await this.downloadPDF(file.url);
					})
				);

				// If recipient type is 'other', use the provided email and phone number
				if (recipientType === 'other') {
					// For custom recipient (email)
					if (shareMode.includes('email') && email) {
						this.sendMail(
							`Dear Recipient,
						<br/><br/>
						We've attached ${patientName}'s vaccination records here. <br/><br/>
						If you have any questions, feel free to reach out to us at ${clinicContact}. We look forward to seeing you and ensuring you receive the best possible care!

						<br/>
						<br/>
						Warm regards,<br/>
						The ${brandName} Team`,
							[...pdfBuffers],
							[...documentNameArray],
							email,
							`Vaccination Records Ready`
						);
						this.logger.log(
							'Email sent successfully to custom recipient'
						);
					}

					// For custom recipient (WhatsApp)
					if (shareMode.includes('whatsapp') && phoneNumber) {
						pdfInput.map(async (file, idx) => {
							if (file.isPdf) {
								const {
									templateName,
									valuesArray,
									mobileNumber
								} = sendMedicalRecordDocument({
									clientName: 'Recipient',
									documentUrl: file.url,
									mobileNumber: phoneNumber,
									petName: patientName
								});
								const response =
									await this.whatsappService.sendTemplateMessage(
										{
											templateName,
											valuesArray,
											mobileNumber
										}
									);
								this.logger.log(
									'WhatsApp message sent successfully to custom recipient'
								);
							} else {
								const {
									templateName,
									valuesArray,
									mobileNumber
								} = sendMedicalRecordImage({
									clientName: 'Recipient',
									documentUrl: file.url,
									mobileNumber: phoneNumber,
									petName: patientName
								});
								const response =
									await this.whatsappService.sendTemplateMessage(
										{
											templateName,
											valuesArray,
											mobileNumber
										}
									);
								this.logger.log(
									'WhatsApp message sent successfully to custom recipient'
								);
							}
						});
					}
				} else {
					// For patient owner (default)
					appointmentResponse?.patient?.patientOwners.forEach(
						async (patient: any) => {
							const clientName = `${patient?.ownerBrand?.firstName || ''} ${patient?.ownerBrand?.lastName || ''}`;

							if (
								shareMode.includes('email') &&
								patient?.ownerBrand?.email
							) {
								this.sendMail(
									`Dear ${clientName},
								<br/><br/>
								We've attached ${patientName}'s medical records here. <br/><br/>
								If you have any questions, feel free to reach out to us at ${clinicContact}. We look forward to seeing you and ensuring you receive the best possible care!

								<br/>
								<br/>
								Warm regards,<br/>
								The ${brandName} Team`,
									[...pdfBuffers],
									[...documentNameArray],
									patient?.ownerBrand?.email,
									`Your Pet's Medical Records Ready`
								);
								this.logger.log(
									'Email sent successfully with attachments'
								);
							}
							if (
								shareMode.includes('whatsapp') &&
								patient?.ownerBrand?.globalOwner?.phoneNumber
							) {
								pdfInput.map(async (file, idx) => {
									if (file.isPdf) {
										const {
											templateName,
											valuesArray,
											mobileNumber
										} = sendMedicalRecordDocument({
											clientName,
											documentUrl: file.url,
											mobileNumber: `${patient?.ownerBrand?.globalOwner?.countryCode}${patient?.ownerBrand?.globalOwner?.phoneNumber}`,
											petName: patientName
										});
										const response =
											await this.whatsappService.sendTemplateMessage(
												{
													templateName,
													valuesArray,
													mobileNumber
												}
											);
										this.logger.log(
											'whatsapp mail sent successfully'
										);
									} else {
										const {
											templateName,
											valuesArray,
											mobileNumber
										} = sendMedicalRecordImage({
											clientName,
											documentUrl: file.url,
											mobileNumber: `${patient?.ownerBrand?.globalOwner?.countryCode}${patient?.ownerBrand?.globalOwner?.phoneNumber}`,
											petName: patientName
										});
										const response =
											await this.whatsappService.sendTemplateMessage(
												{
													templateName,
													valuesArray,
													mobileNumber
												}
											);
										this.logger.log(
											'whatsapp mail sent successfully'
										);
									}
								});
							}
						}
					);
				}
			} catch (error) {
				this.logger.log(
					'error arises while sending or merging pdf',
					error
				);
				throw new HttpException(
					'Something went wrong, please try again later.',
					HttpStatus.INTERNAL_SERVER_ERROR
				);
			}
		} else {
			this.logger.log('documents not found');
			throw new HttpException(
				'Vaccination document not found',
				HttpStatus.NOT_FOUND
			);
		}
	}

	async sendVaccinationFromFileKey(
		fileKey: string,
		shareMode: Array<string>,
		recipientType?: 'client' | 'other',
		email?: string,
		phoneNumber?: string
	) {
		this.logger.log('send vaccination from fileKey, service is called');
		try {
			const patientVaccinationResponse: any =
				await this.patientVaccinationRepository.findOne({
					where: { reportUrl: fileKey },
					relations: [
						'patient',
						'patient.patientOwners',
						'patient.patientOwners.ownerBrand',
						'patient.patientOwners.ownerBrand.globalOwner',
						'patient.clinic',
						'patient.clinic.brand'
					]
				});
			if (fileKey) {
				const viewSignedUrl =
					await this.s3Service.getViewPreSignedUrl(fileKey);
				const pdfBuffer: Buffer = await this.downloadPDF(viewSignedUrl);

				const brandName =
					patientVaccinationResponse?.patient?.clinic?.brand?.name ||
					'';
				const patientName =
					patientVaccinationResponse?.patient?.patientName || '';
				const phoneNumbers =
					patientVaccinationResponse?.patient?.clinic?.phoneNumbers;
				const clinicContact =
					`+${phoneNumbers?.[0]?.country_code ?? ''} ${phoneNumbers?.[0]?.number ?? ''}`.trim();

				// If recipient type is 'other', use the provided email and phone number
				if (recipientType === 'other') {
					// For custom recipient (email)
					if (shareMode.includes('email') && email) {
						this.sendMail(
							`Dear Recipient,
						<br/><br/>
						We've attached ${patientName}'s vaccination records here. <br/><br/>
						If you have any questions, feel free to reach out to us at ${clinicContact}. We look forward to seeing you and ensuring you receive the best possible care!

						<br/>
						<br/>
						Warm regards,<br/>
						The ${brandName} Team`,
							[pdfBuffer],
							[
								patientVaccinationResponse?.urlMeta?.fileName ||
									'vaccination.pdf'
							],
							email,
							`Vaccination Records Ready`
						);
						this.logger.log(
							'Email sent successfully to custom recipient'
						);
					}

					// For custom recipient (WhatsApp)
					if (shareMode.includes('whatsapp') && phoneNumber) {
						const isPdf =
							patientVaccinationResponse?.urlMeta?.fileName?.includes(
								'.pdf'
							);
						if (isPdf) {
							// Generate template data
							const templateArgs = {
								brandName,
								clientName: 'Recipient',
								EMRFile: viewSignedUrl,
								mobileNumber: phoneNumber,
								petName: patientName,
								clinicContact
							};
							const { mobileNumber, templateName, valuesArray } =
								selectTemplate(
									patientVaccinationResponse?.patient?.clinic,
									templateArgs,
									getIndividualEMRTemplateData,
									getIndividualEMRClinicLinkTemplateData
								);
							await this.whatsappService.sendTemplateMessage({
								templateName,
								valuesArray,
								mobileNumber
							});
							this.logger.log(
								'WhatsApp message sent successfully to custom recipient'
							);
						} else {
							const templateArgs = {
								brandName,
								clientName: 'Recipient',
								EMRFile: viewSignedUrl,
								mobileNumber: phoneNumber,
								petName: patientName,
								clinicContact
							};
							const { mobileNumber, templateName, valuesArray } =
								selectTemplate(
									patientVaccinationResponse?.patient?.clinic,
									templateArgs,
									getVaccinationImageTemplateData,
									getVaccinationImageClinicLinkTemplateData
								);

							await this.whatsappService.sendTemplateMessage({
								templateName,
								valuesArray,
								mobileNumber
							});
							this.logger.log(
								'WhatsApp message sent successfully to custom recipient'
							);
						}
					}
				} else {
					// Original code for patient owner
					patientVaccinationResponse?.patient?.patientOwners.forEach(
						async (patient: any) => {
							const clientName = `${patient?.ownerBrand?.firstName || ''} ${patient?.ownerBrand?.lastName || ''}`;

							if (
								shareMode.includes('email') &&
								patient?.ownerBrand?.email
							) {
								this.sendMail(
									`Dear ${clientName},
								<br/><br/>
								We've attached ${patientName}'s vaccination records here. <br/><br/>
								If you have any questions, feel free to reach out to us at ${clinicContact}. We look forward to seeing you and ensuring you receive the best possible care!

								<br/>
								<br/>
								Warm regards,<br/>
								The ${brandName} Team`,
									[pdfBuffer],
									[
										patientVaccinationResponse?.urlMeta
											?.fileName || 'vaccination.pdf'
									],
									patient?.ownerBrand?.email,
									`Your Pet's Medical Records Ready`
								);
								this.logger.log(
									'Email sent successfully with attachments'
								);
							}
							if (
								shareMode.includes('whatsapp') &&
								patient?.ownerBrand?.globalOwner?.phoneNumber
							) {
								let sendTemplateName,
									sendValuesArray,
									sendMobileNumber;
								if (
									!patientVaccinationResponse?.urlMeta?.fileName.includes(
										'.pdf'
									)
								) {
									const templateArgs = {
										brandName,
										clientName,
										EMRFile: viewSignedUrl,
										mobileNumber: `${patient?.ownerBrand?.globalOwner?.countryCode}${patient?.ownerBrand?.globalOwner?.phoneNumber}`,
										petName: patientName,
										clinicContact
									};
									const {
										mobileNumber,
										templateName,
										valuesArray
									} = selectTemplate(
										patientVaccinationResponse?.patient
											?.clinic,
										templateArgs,
										getVaccinationImageTemplateData,
										getVaccinationImageClinicLinkTemplateData
									);
									sendTemplateName = templateName;
									sendValuesArray = valuesArray;
									sendMobileNumber = mobileNumber;
								} else {
									// Generate template data
									const templateArgs = {
										brandName,
										clientName,
										EMRFile: viewSignedUrl,
										mobileNumber: `${patient?.ownerBrand?.globalOwner?.countryCode}${patient?.ownerBrand?.globalOwner?.phoneNumber}`,
										petName: patientName,
										clinicContact
									};
									const {
										mobileNumber,
										templateName,
										valuesArray
									} = selectTemplate(
										patient?.clinic,
										templateArgs,
										getIndividualEMRTemplateData,
										getIndividualEMRClinicLinkTemplateData
									);

									sendTemplateName = templateName;
									sendValuesArray = valuesArray;
									sendMobileNumber = mobileNumber;
								}
								const response =
									await this.whatsappService.sendTemplateMessage(
										{
											templateName: sendTemplateName,
											valuesArray: sendValuesArray,
											mobileNumber: sendMobileNumber
										}
									);
								this.logger.log(
									'WhatsApp message sent successfully',
									response
								);
							}
						}
					);
				}
			} else {
				this.logger.log('vaccination documents not found');
				throw new HttpException(
					'Vaccination document not found',
					HttpStatus.NOT_FOUND
				);
			}
		} catch (error) {
			this.logger.log('error occurs', error);
			throw new HttpException(
				'Something went wrong, please try again later.',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}
	async sendInvoiceTabDocuments(
		patientId: string,
		fileKeys: string[],
		shareMode: Array<string>
	) {
		const patientResonse = await this.patientRepository.findOne({
			where: { id: patientId },
			relations: [
				'patientOwners',
				'patientOwners.ownerBrand',
				'patientOwners.ownerBrand.globalOwner',
				'clinic',
				'clinic.brand'
			]
		});

		const patientName = patientResonse?.patientName || '';
		const brandName = patientResonse?.clinic?.brand?.name || '';
		const phoneNumbers = patientResonse?.clinic?.phoneNumbers;
		const clinicContact =
			`+${phoneNumbers?.[0]?.country_code ?? ''} ${phoneNumbers?.[0]?.number ?? ''}`.trim();

		try {
			// Get signed URLs and download PDFs for all files
			const pdfBuffers: Buffer[] = [];
			const fileNames: string[] = [];

			this.logger.log('Starting to process files', { fileKeys });

			for (const fileKey of fileKeys) {
				if (!fileKey) continue;

				try {
					const viewSignedUrl =
						await this.s3Service.getViewPreSignedUrl(fileKey);
					const pdfBuffer = await this.downloadPDF(viewSignedUrl);
					pdfBuffers.push(pdfBuffer);

					// Use descriptive file names based on file type
					if (fileKey.includes('invoice')) {
						fileNames.push('invoice.pdf');
					} else if (fileKey.includes('receipt')) {
						fileNames.push('receipt.pdf');
					} else if (fileKey.includes('creditNote')) {
						fileNames.push('creditNote.pdf');
					} else {
						fileNames.push('document.pdf');
					}

					this.logger.log('Successfully processed file', { fileKey });
				} catch (error) {
					this.logger.error('Failed to process file', {
						fileKey,
						error
					});
				}
			}

			if (pdfBuffers.length === 0) {
				this.logger.error('No valid documents found', { patientId });
				throw new HttpException(
					'No valid documents found',
					HttpStatus.NOT_FOUND
				);
			}

			this.logger.log('Successfully processed all files', {
				patientId,
				fileCount: pdfBuffers.length,
				fileNames
			});

			patientResonse?.patientOwners?.forEach(async (patient: any) => {
				const clientName = `${patient?.ownerBrand?.firstName || ''} ${patient?.ownerBrand?.lastName || ''}`;

				if (shareMode.includes('email') && patient?.ownerBrand?.email) {
					try {
						// Customize email subject and body based on document types
						let subject = 'Your Documents from ' + brandName;
						let bodyIntro = '';

						// Check which documents are being sent
						const hasInvoice = fileKeys.some(key =>
							key.includes('invoice')
						);
						const hasCreditNote = fileKeys.some(key =>
							key.includes('creditNote')
						);
						const hasReceipt = fileKeys.some(key =>
							key.includes('receipt')
						);

						// Set subject and body based on combination of documents
						if (hasCreditNote && hasReceipt) {
							subject =
								'Credit Note and Receipt from ' + brandName;
							bodyIntro = `We've processed your refund and attached the credit note along with its receipt`;
						} else if (hasInvoice && hasReceipt) {
							subject = 'Invoice and Receipt from ' + brandName;
							bodyIntro = `We've attached your invoice along with its payment receipt`;
						} else if (hasCreditNote) {
							subject = 'Credit Note from ' + brandName;
							bodyIntro = `We've processed your refund and attached the credit note`;
						} else if (hasInvoice) {
							subject = 'Invoice from ' + brandName;
							bodyIntro = `We've attached your invoice`;
						} else if (hasReceipt) {
							subject = 'Receipt from ' + brandName;
							bodyIntro = `We've attached your receipt`;
						}

						await this.sendMail(
							`Dear ${clientName},
							<br/><br/>
							${bodyIntro} for ${patientName}. <br/><br/>
							If you have any questions, feel free to reach out to us at ${clinicContact}. We look forward to seeing you and ensuring you receive the best possible care!

							<br/>
							<br/>
							Warm regards,<br/>
							The ${brandName} Team`,
							pdfBuffers,
							fileNames,
							patient?.ownerBrand?.email ??
								'<EMAIL>',
							subject
						);
						this.logger.log('Successfully sent email', {
							patientId,
							email: patient?.ownerBrand?.email,
							fileCount: pdfBuffers.length
						});
					} catch (error) {
						this.logger.error('Failed to send email', {
							patientId,
							email: patient?.ownerBrand?.email,
							error
						});
					}
				}
				if (
					shareMode.includes('whatsapp') &&
					patient?.ownerBrand?.globalOwner?.phoneNumber
				) {
					try {
						// Send each document one after another
						for (const fileKey of fileKeys) {
							const viewSignedUrl =
								await this.s3Service.getViewPreSignedUrl(
									fileKey
								);

							// Determine document type and use appropriate template
							let templateData;
							if (fileKey.includes('invoice')) {
								const templateArgs = {
									clientName,
									brandName,
									petName: patientName,
									doctorName: '', // We don't have doctor info here
									invoiceFile: viewSignedUrl,
									mobileNumber: `${patient?.ownerBrand?.globalOwner?.countryCode}${patient?.ownerBrand?.globalOwner?.phoneNumber}`
								};
								templateData = selectTemplate(
									patient?.clinic,
									templateArgs,
									getAppointmentInvoiceTemplateData,
									getAppointmentInvoiceClinicLinkTemplateData
								);
							} else if (fileKey.includes('creditNote')) {
								const templateArgs = {
									clientName,
									brandName,
									refundDate: moment().format('MMMM Do YYYY'),
									creditNoteFile: viewSignedUrl,
									mobileNumber: `${patient?.ownerBrand?.globalOwner?.countryCode}${patient?.ownerBrand?.globalOwner?.phoneNumber}`
								};
								templateData = selectTemplate(
									patient?.clinic,
									templateArgs,
									getCreditNoteGenerationTemplateData,
									getCreditNoteGenerationClinicLinkTemplateData
								);
							} else if (fileKey.includes('receipt')) {
								const templateArgs = {
									clientName,
									brandName,
									receiptDate:
										moment().format('MMMM Do YYYY'),
									receiptFile: viewSignedUrl,
									mobileNumber: `${patient?.ownerBrand?.globalOwner?.countryCode}${patient?.ownerBrand?.globalOwner?.phoneNumber}`
								};
								templateData = selectTemplate(
									patient?.clinic,
									templateArgs,
									getReceiptGenerationTemplateData,
									getReceiptGenerationClinicLinkTemplateData
								);
							}

							if (templateData) {
								await this.whatsappService.sendTemplateMessage(
									templateData
								);
							}
						}
						this.logger.log('Successfully sent WhatsApp messages', {
							patientId,
							phone: patient?.ownerBrand?.globalOwner?.phoneNumber
						});
					} catch (error) {
						this.logger.error('Failed to send WhatsApp message', {
							patientId,
							phone: patient?.ownerBrand?.globalOwner
								?.phoneNumber,
							error
						});
					}
				}
			});
		} catch (err) {
			this.logger.error('Failed to process documents', {
				patientId,
				error: err
			});
			throw new HttpException(
				'Something went wrong, please try again later.',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	async sendMedicalRecords(
		patientId: string,
		shareMode: Array<string>,
		documentType: Array<string>
	) {
		const patientResonse = await this.patientRepository.findOne({
			where: { id: patientId },
			relations: [
				'patientOwners',
				'patientOwners.ownerBrand',
				'patientOwners.ownerBrand.globalOwner',
				'clinic',
				'clinic.brand'
			]
		});

		this.logger.log('found the patient detail', patientResonse);

		const patientName = patientResonse?.patientName || '';
		const brandName = patientResonse?.clinic?.brand?.name || '';
		const phoneNumbers = patientResonse?.clinic?.phoneNumbers;
		const clinicContact =
			`+${phoneNumbers?.[0]?.country_code ?? ''} ${phoneNumbers?.[0]?.number ?? ''}`.trim();
		const pdfBuffers: Buffer[] = [];
		const fileNames: Array<string> = [];
		await Promise.all(
			documentType.map(async (docType: string) => {
				if (docType === 'emr') {
					this.logger.log('accessing emr for patient', patientId);
					const buffer =
						await this.getAllEMRDocumentsForPatient(patientId);
					if (buffer) {
						pdfBuffers.push(buffer);
						fileNames.push('merged_emrs.pdf');
					}
				} else if (docType === 'prescriptions') {
					this.logger.log(
						'accessing prescriptions for patient',
						patientId
					);
					const buffer =
						await this.getAllPrescriptionDocumentsforPatient(
							patientId
						);
					if (buffer) {
						pdfBuffers.push(buffer);
						fileNames.push('merged_prescriptions.pdf');
					}
				} else if (docType === 'invoices') {
					this.logger.log('accessing invoice for patient', patientId);
					const buffer =
						await this.getAllInvoiceTabDocumentsForPatient(
							patientId
						);
					if (buffer) {
						pdfBuffers.push(buffer);
						fileNames.push('merged_invoices.pdf');
					}
				} else if (docType === 'diagnostics') {
					this.logger.log(
						'accessing diagnostics for patient',
						patientId
					);
					const buffer =
						await this.getAllDiagnosticDocumentsForPatient(
							patientId
						);
					if (buffer) {
						pdfBuffers.push(buffer);
						fileNames.push('merged_diagnostics.pdf');
					}
				} else if (docType === 'otherSupportingDocuments') {
					this.logger.log(
						'accessing otherSupportingDocuments for patient',
						patientId
					);
					const buffer =
						await this.getAllOtherSupportingDocumentsForPatient(
							patientId
						);
					if (buffer) {
						pdfBuffers.push(buffer);
						fileNames.push('merged_otherSupportingDocuments.pdf');
					}
				} else if (docType === 'vaccinations') {
					this.logger.log(
						'accessing vaccinations for patient',
						patientId
					);
					const buffer =
						await this.getAllVaccinationDocumentsforPatient(
							patientId
						);
					if (buffer) {
						pdfBuffers.push(buffer);
						fileNames.push('merged_vaccinations.pdf');
					}
				}
			})
		);
		this.logger.log('successfully made the merged pdfBuffer');
		if (pdfBuffers.length > 0) {
			patientResonse?.patientOwners?.forEach(async (patient: any) => {
				const clientName = `${patient?.ownerBrand?.firstName || ''} ${patient?.ownerBrand?.lastName || ''}`;

				if (shareMode.includes('email') && patient?.ownerBrand?.email) {
					this.makeTemplateAndSendMail({
						clientName,
						patientName,
						clinicContact,
						brandName,
						clientEmail: patient?.ownerBrand?.email,
						subject: `Your Pet's Medical Records Ready`,
						pdfBuffer: pdfBuffers,
						fileNames
					});
					this.logger.log('mail send with attachments');
				}
				if (
					shareMode.includes('whatsapp') &&
					patient?.ownerBrand?.globalOwner?.phoneNumber
				) {
					pdfBuffers.map(async (buffer: Buffer) => {
						const fileKey = `medical-record/${uuidv4()}`;

						this.logger.log(
							'generating fileKey for cloudfrontkey',
							fileKey
						);

						await this.s3Service.uploadPdfToS3(buffer, fileKey);

						const viewSignedUrl =
							await this.s3Service.getViewPreSignedUrl(fileKey);

						// Generate template data
						const templateArgs = {
							brandName,
							clientName,
							EMRFile: viewSignedUrl!,
							mobileNumber: `${patient?.ownerBrand?.globalOwner?.countryCode}${patient?.ownerBrand?.globalOwner?.phoneNumber}`,
							clinicContact,
							petName: patientName
						};
						const { mobileNumber, templateName, valuesArray } =
							selectTemplate(
								patient?.clinic,
								templateArgs,
								getIndividualEMRTemplateData,
								getIndividualEMRClinicLinkTemplateData
							);

						const response =
							await this.whatsappService.sendTemplateMessage({
								templateName,
								valuesArray,
								mobileNumber
							});
						this.logger.log(
							'whatsapp message send with attachments',
							response
						);
					});
				}
			});
		} else {
			this.logger.log('not found documents');
			throw new HttpException(
				'documents not founds',
				HttpStatus.NOT_FOUND
			);
		}
	}

	async getAllEMRDocumentsForPatient(patientId: string) {
		const response = await this.emrRepository.find({
			where: { patientId }
		});

		if (response.length > 0) {
			const pdfInput: PDFInput[] = [];
			await Promise.all(
				response.map(async doc => {
					const viewSignedUrl =
						await this.s3Service.getViewPreSignedUrl(
							doc.emrFileKey
						);
					pdfInput.push(viewSignedUrl);
				})
			).catch(err => {
				this.logger.log('fileKey is not valid', err);
				return null;
			});
			this.logger.log('start merging pdfs', patientId);
			const mergePdfBuffer: Buffer = await mergePDFs(pdfInput);
			if (mergePdfBuffer.length > MAX_SIZE_5MB) {
				const compressedBuffer = compressPdfBuffer(mergePdfBuffer);
				return compressedBuffer;
			}
			return mergePdfBuffer;
		} else {
			this.logger.log('there is no any emr for this patient', patientId);
			return null;
		}
	}

	async getAllVaccinationDocumentsforPatient(patientId: string) {
		const response = await this.patientVaccinationRepository.find({
			where: { patientId }
		});
		if (response.length > 0) {
			const pdfInput: PDFInput[] = [];
			await Promise.all(
				response.map(async vaccine => {
					if (vaccine.reportUrl) {
						const viewSignedUrl =
							await this.s3Service.getViewPreSignedUrl(
								vaccine.reportUrl
							);
						const urlMeta: any = vaccine.urlMeta;
						if (urlMeta.fileType === 'Image') {
							const imageBuffer =
								await generatePDFFromImage(viewSignedUrl);
							pdfInput.push(imageBuffer);
						} else {
							pdfInput.push(viewSignedUrl);
						}
					}
				})
			).catch(err => {
				this.logger.log('fileKey is not valid', err);
				return null;
			});
			this.logger.log('start merging pdfs', patientId);
			const mergePdfBuffer: Buffer = await mergePDFs(pdfInput);
			if (mergePdfBuffer.length > MAX_SIZE_5MB) {
				const compressedBuffer = compressPdfBuffer(mergePdfBuffer);
				return compressedBuffer;
			}
			return mergePdfBuffer;
		} else {
			this.logger.log(
				'there is no any vaccinations for this patient',
				patientId
			);
			return null;
		}
	}

	async getAllPrescriptionDocumentsforPatient(patientId: string) {
		const response = await this.appointmentRepository.find({
			where: { patientId, deletedAt: IsNull() },
			relations: ['cart', 'cart.invoice'],
			order: { date: 'DESC', startTime: 'DESC' }
		});
		const pdfInputs: string[] = [];
		const fileKeys: string[] = [];

		response.map(async doc => {
			const fileUrls: any =
				doc?.cart?.invoice?.find(
					(inv: any) => inv.invoiceType === EnumInvoiceType.Invoice
				)?.fileUrl || null;
			if (fileUrls?.prescriptionFileKey) {
				fileKeys.push(fileUrls?.prescriptionFileKey);
			}
		});
		if (fileKeys.length > 0) {
			try {
				this.logger.log('start merging pdfs', patientId);
				await Promise.all(
					fileKeys.map(async fileKey => {
						const viewSignedUrl =
							await this.s3Service.getViewPreSignedUrl(fileKey);
						pdfInputs.push(viewSignedUrl);
					})
				).catch(err => {
					this.logger.log('fileKey is not valid', err);
					return null;
				});
				const mergePdfBuffer: Buffer = await mergePDFs(pdfInputs);
				if (mergePdfBuffer.length > MAX_SIZE_5MB) {
					const compressedBuffer = compressPdfBuffer(mergePdfBuffer);
					return compressedBuffer;
				}
				return mergePdfBuffer;
			} catch (err) {
				console.log('error =>>>>>>>>>>>>>>>', err);
				this.logger.log('something went wrong', err);
				return null;
			}
		} else {
			this.logger.log(
				'there is no any prescription documents for this patient',
				patientId
			);
			return null;
		}
	}

	async getAllOtherSupportingDocumentsForPatient(patientId: string) {
		const response: any = await this.appointmentRepository.find({
			where: { patientId, deletedAt: IsNull() },
			relations: ['appointmentDetails'],
			order: { date: 'DESC', startTime: 'DESC' }
		});

		const fileKeys: Array<any> = [];
		response.map((appointment: any) => {
			appointment.appointmentDetails.details?.attachments?.list.map(
				(file: any) => {
					if (file.fileName.includes('.pdf')) {
						fileKeys.push({ type: 'pdf', key: file.fileKey });
					} else {
						fileKeys.push({ type: 'image', key: file.fileKey });
					}
				}
			);
		});

		if (fileKeys.length > 0) {
			const pdfInputs: PDFInput[] = [];
			try {
				await Promise.all(
					fileKeys.map(async (fileMeta: any) => {
						const viewSignedUrl =
							await this.s3Service.getViewPreSignedUrl(
								fileMeta.key
							);
						if (fileMeta.type === 'image') {
							const imageBuffer =
								await generatePDFFromImage(viewSignedUrl);
							pdfInputs.push(imageBuffer);
						} else {
							pdfInputs.push(viewSignedUrl);
						}
					})
				).catch(err => {
					this.logger.log('fileKey is not valid', err);
					return null;
				});
				const mergePdfBuffer: Buffer = await mergePDFs(pdfInputs);
				if (mergePdfBuffer.length > MAX_SIZE_5MB) {
					const compressedBuffer = compressPdfBuffer(mergePdfBuffer);
					return compressedBuffer;
				}
				return mergePdfBuffer;
			} catch (err) {
				this.logger.log(
					'something went wrong while creating pdf for other supporting documents'
				);
				return null;
			}
		} else {
			this.logger.log(
				'there is no other supporting documents for patient',
				patientId
			);
			return null;
		}
	}

	async getAllDiagnosticDocumentsForPatient(patientId: string) {
		const response: any = await this.appointmentRepository.find({
			where: { patientId, deletedAt: IsNull() },
			relations: ['appointmentDetails'],
			order: { date: 'DESC', startTime: 'DESC' }
		});

		const fileKeys: Array<any> = [];
		response.map((appointment: any) => {
			appointment.appointmentDetails.details?.objective?.labReports?.[0]?.files?.map(
				(file: any) => {
					if (file.fileName.includes('.pdf')) {
						fileKeys.push({ type: 'pdf', key: file.fileKey });
					} else {
						fileKeys.push({ type: 'image', key: file.fileKey });
					}
				}
			);
		});
		if (fileKeys.length > 0) {
			const pdfInputs: PDFInput[] = [];
			try {
				await Promise.all(
					fileKeys.map(async (fileMeta: any) => {
						const viewSignedUrl =
							await this.s3Service.getViewPreSignedUrl(
								fileMeta.key
							);
						if (fileMeta.type === 'image') {
							const imageBuffer =
								await generatePDFFromImage(viewSignedUrl);
							pdfInputs.push(imageBuffer);
						} else {
							pdfInputs.push(viewSignedUrl);
						}
					})
				).catch(err => {
					this.logger.log('fileKey is not valid', err);
					return null;
				});
				const mergePdfBuffer: Buffer = await mergePDFs(pdfInputs);
				if (mergePdfBuffer.length > MAX_SIZE_5MB) {
					const compressedBuffer = compressPdfBuffer(mergePdfBuffer);
					return compressedBuffer;
				}
				return mergePdfBuffer;
			} catch (err) {
				this.logger.log(
					'something went wrong while creating pdf for lab reports'
				);
				return null;
			}
		} else {
			this.logger.log(
				'there is no lab reports documents for patient',
				patientId
			);
			return null;
		}
	}

	async getAllInvoiceTabDocumentsForPatient(patientId: string) {
		const response: any = await this.appointmentRepository.find({
			where: { patientId, deletedAt: IsNull() },
			relations: ['cart', 'cart.invoice'],
			order: { date: 'DESC', startTime: 'DESC' }
		});
		const payementDetailsResponse =
			await this.paymentDetailsRepository.find({ where: { patientId } });

		const fileKeys: string[] = [];
		response?.map((invoice: any) => {
			invoice?.cart?.invoice?.map((item: any) => {
				if (item?.invoiceType === 'Invoice') {
					if (item.fileUrl?.invoiceFileKey) {
						fileKeys.push(item.fileUrl?.invoiceFileKey);
					}
				} else if (item?.invoiceType === 'Refund') {
					if (item.fileUrl?.creditNoteFileKey) {
						fileKeys.push(item.fileUrl?.creditNoteFileKey);
					}
				}
			});
		});
		payementDetailsResponse.map((item: any) => {
			if (item.type === 'Collect') {
				fileKeys.push(item.receiptDetail?.fileKey);
			}
		});
		if (fileKeys.length > 0) {
			const pdfInputs: PDFInput[] = [];
			try {
				this.logger.log('start merging pdfs', patientId);
				await Promise.all(
					fileKeys.map(async fileKey => {
						const viewSignedUrl =
							await this.s3Service.getViewPreSignedUrl(fileKey);
						pdfInputs.push(viewSignedUrl);
					})
				).catch(err => {
					this.logger.log('fileKey is not valid', err);
					return null;
				});
				const mergePdfBuffer: Buffer = await mergePDFs(pdfInputs);
				if (mergePdfBuffer.length > MAX_SIZE_5MB) {
					const compressedBuffer = compressPdfBuffer(mergePdfBuffer);
					return compressedBuffer;
				}
				return mergePdfBuffer;
			} catch (err) {
				console.log('error =>>>>>>>>>>>>>>>', err);
				this.logger.log('something went wrong', err);
				return null;
			}
		} else {
			this.logger.log(
				'there is no any invoice tab documents for this patient',
				patientId
			);
			return null;
		}
	}
	async makeTemplateAndSendMail({
		clientName,
		patientName,
		clinicContact,
		brandName,
		clientEmail,
		subject,
		pdfBuffer,
		fileNames
	}: {
		clientName: string;
		patientName: string;
		clinicContact: string;
		brandName: string;
		clientEmail: string;
		subject: string;
		pdfBuffer: Array<Buffer>;
		fileNames: Array<string>;
	}) {
		this.sendMail(
			`Dear ${clientName},
		<br/><br/>
		We've attached ${patientName}’s medical records here. <br/><br/>
		If you have any questions, feel free to reach out to us at ${clinicContact}.We look forward to seeing you and ensuring you receive the best possible care!

		<br/>
		<br/>
		Warm regards,<br/>
		The ${brandName} Team`,
			pdfBuffer,
			fileNames,
			clientEmail ?? '<EMAIL>',
			subject
		);
	}

	async documentAvailableForAppointment(appointmentId: string) {
		const emrResponse = await this.emrRepository.findOne({
			where: { appointmentId }
		});
		const patientVaccinationResponse =
			await this.patientVaccinationRepository.findOne({
				where: { appointmentId }
			});
		const appointmentResponse: any =
			await this.appointmentRepository.findOne({
				where: { id: appointmentId, deletedAt: IsNull() },
				relations: ['appointmentDetails', 'cart', 'cart.invoice'],
				order: { date: 'DESC', startTime: 'DESC' }
			});
		const diagnosticAvailable = appointmentResponse?.appointmentDetails
			?.details?.objective?.labReports?.[0]?.files
			? true
			: false;
		const supportingDocumentsAvailable =
			appointmentResponse?.appointmentDetails.details?.attachments?.list
				?.length > 0;
		const invoicePrescripitonFileKeyObj =
			appointmentResponse?.cart?.invoice?.find(
				(inv: any) => inv.invoiceType === EnumInvoiceType.Invoice
			)?.fileUrl ?? null;
		const invoiceAvailable = invoicePrescripitonFileKeyObj?.invoiceFileKey;
		const prescriptionAvailable =
			invoicePrescripitonFileKeyObj?.prescriptionFileKey;

		return {
			emr: true,
			diagnostics: diagnosticAvailable ?? false,
			vaccinations: patientVaccinationResponse?.reportUrl ? true : false,
			invoices: invoiceAvailable ? true : false,
			otherSupportingDocuments: supportingDocumentsAvailable
				? true
				: false,
			prescriptions: prescriptionAvailable ? true : false
		};
	}

	async documentAvailableForPatient(patientId: string) {
		const emrResponse = await this.emrRepository.find({
			where: { patientId }
		});
		const patientVaccinationResponse =
			await this.patientVaccinationRepository.find({
				where: { patientId }
			});
		const appointmentResponse: any = await this.appointmentRepository.find({
			where: { patientId, deletedAt: IsNull() },
			relations: ['appointmentDetails', 'cart', 'cart.invoice'],
			order: { date: 'DESC', startTime: 'DESC' }
		});

		const isEmrAvailable = emrResponse.some(
			emr => emr.emrFileKey.length > 0
		);

		const isVaccinationAvailable = patientVaccinationResponse?.some(
			vaccine => vaccine.reportUrl
		);

		let isInvoiceAvailable = false;
		let isPrescriptionAvailable = false;
		let diagnosticAvailable = false;
		let otherSupportingDocumentsAvailable = false;

		for (const appointment of appointmentResponse) {
			if (
				!isInvoiceAvailable &&
				appointment?.cart?.invoice?.some(
					(item: any) =>
						item?.invoiceType === 'Invoice' &&
						item.fileUrl?.invoiceFileKey
				)
			) {
				isInvoiceAvailable = true;
			}

			if (
				!isPrescriptionAvailable &&
				appointment?.cart?.invoice?.some(
					(item: any) =>
						item?.invoiceType === 'Invoice' &&
						item.fileUrl?.prescriptionFileKey
				)
			) {
				isPrescriptionAvailable = true;
			}

			if (
				!diagnosticAvailable &&
				appointment.appointmentDetails?.details?.objective
					?.labReports?.[0]?.files
			) {
				diagnosticAvailable = true;
			}

			if (
				!otherSupportingDocumentsAvailable &&
				appointment.appointmentDetails?.details?.attachments?.list
					?.length > 0
			) {
				otherSupportingDocumentsAvailable = true;
			}

			if (
				isEmrAvailable &&
				isVaccinationAvailable &&
				isInvoiceAvailable &&
				isPrescriptionAvailable &&
				diagnosticAvailable &&
				otherSupportingDocumentsAvailable
			) {
				break;
			}
		}

		return {
			emr: isEmrAvailable,
			vaccinations: isVaccinationAvailable,
			invoices: isInvoiceAvailable,
			prescriptions: isPrescriptionAvailable,
			diagnostics: diagnosticAvailable,
			otherSupportingDocuments: otherSupportingDocumentsAvailable
		};
	}
	async sendReminderNotification(reminders: PatientReminder[]) {
		for (const reminder of reminders) {
			// Double-check that patient is not deceased before sending notifications
			if (reminder.patient?.isDeceased) {
				this.logger.log(
					`Skipping reminder ${reminder.id} for deceased patient ${reminder.patient.id}`
				);
				continue;
			}

			await this.sendEmailNotification(reminder);
			await this.sendWhatsAppNotification(reminder);
		}
	}

	async sendEmailNotification(reminder: PatientReminder) {
		for (const patient of reminder.patient?.patientOwners) {
			if (patient?.ownerBrand?.email && isProductionOrUat()) {
				const { subject, email, body } = reminderNotificationTemplate({
					brandName: reminder?.clinic?.brand?.name,
					clientName: `${patient?.ownerBrand?.firstName} ${patient?.ownerBrand?.lastName}`,
					dueDate: moment(reminder?.dueDate).format('DD-MM-YYYY'),
					email: patient?.ownerBrand?.email || '',
					patientName: reminder?.patient?.patientName || '',
					title: reminder?.title || ''
				});
				await this.sendMail(body, [], [], email, subject);
				this.logger.log(
					`Sending EMAIL notification to ${reminder} for reminder ID: ${reminder.id}`
				);
			} else {
				this.logger.log('no email for patient-owner', reminder?.id);
			}
		}
	}

	async sendWhatsAppNotification(reminder: PatientReminder) {
		for (const patient of reminder.patient?.patientOwners) {
			if (
				patient?.ownerBrand?.globalOwner?.phoneNumber &&
				isProduction()
			) {
				const templateArgs = {
					brand: reminder?.clinic?.brand?.name,
					clientName: `${patient?.ownerBrand?.firstName} ${patient?.ownerBrand?.lastName}`,
					dueDate: moment(reminder?.dueDate).format('DD-MM-YYYY'),
					mobileNumber: `${patient?.ownerBrand?.globalOwner?.countryCode}${patient?.ownerBrand?.globalOwner?.phoneNumber}`,
					petName: reminder?.patient?.patientName || '',
					title: reminder?.title || ''
				};
				const { mobileNumber, templateName, valuesArray } =
					selectTemplate(
						reminder?.patient?.clinic,
						templateArgs,
						reminderNotifications,
						reminderNotificationsClinicLink
					);
				await this.whatsappService.sendTemplateMessage({
					templateName,
					valuesArray,
					mobileNumber
				});
				this.logger.log(
					`Sending WHATSAPP notification to ${reminder} for reminder ID: ${reminder.id}`
				);
			} else {
				this.logger.log('no email for patient-owner', reminder?.id);
			}
		}
	}

	async shareledgerDocuments(
		invoiceReferenceIds: Array<string>,
		shareMode: Array<string>,
		type: 'client' | 'other',
		email: string,
		phoneNumber: string,
		filters?: {
			userId?: string;
			status?: string;
			startDate?: string;
			endDate?: string;
			searchTerm?: string;
		}
	) {
		this.logger.log('Starting shareledgerDocuments with params', {
			invoiceReferenceIds,
			shareMode,
			type,
			hasEmail: !!email,
			hasPhoneNumber: !!phoneNumber
		});

		if (!invoiceReferenceIds || invoiceReferenceIds.length === 0) {
			this.logger.error('No invoice reference IDs provided');
			throw new HttpException(
				'No invoice reference IDs provided',
				HttpStatus.BAD_REQUEST
			);
		}

		try {
			// Generate a unique request ID for this specific request
			const requestId = uuidv4();

			// Sort the invoice IDs alphabetically to ensure consistent processing
			this.logger.log('Sorting invoice IDs');
			const sortedInvoiceIds = [...invoiceReferenceIds].sort();

			// First, find the invoice details for the first invoice to get owner information
			this.logger.log('Finding first invoice', {
				referenceAlphaId: sortedInvoiceIds[0]
			});
			const firstInvoice = await this.invoiceEntity.findOne({
				where: { referenceAlphaId: sortedInvoiceIds[0] }
			});

			if (!firstInvoice) {
				this.logger.error('First invoice not found', {
					referenceAlphaId: sortedInvoiceIds[0]
				});
				throw new HttpException(
					'First invoice not found',
					HttpStatus.NOT_FOUND
				);
			}

			// Get patient and clinic details for the first invoice
			this.logger.log('Finding patient details', {
				patientId: firstInvoice.patientId
			});
			let patientDetails;
			try {
				patientDetails = await this.patientRepository.findOne({
					where: { id: firstInvoice.patientId },
					relations: [
						'clinic',
						'clinic.brand',
						'patientOwners',
						'patientOwners.ownerBrand',
						'patientOwners.ownerBrand.globalOwner'
					]
				});

				if (!patientDetails) {
					this.logger.error('Patient details not found', {
						patientId: firstInvoice.patientId
					});
					throw new HttpException(
						'Patient details not found',
						HttpStatus.NOT_FOUND
					);
				}
			} catch (error) {
				this.logger.error('Error finding patient details', {
					patientId: firstInvoice.patientId,
					error
				});
				throw new HttpException(
					'Error finding patient details',
					HttpStatus.INTERNAL_SERVER_ERROR
				);
			}

			// Get owner ID from the first invoice
			const ownerId = firstInvoice.ownerId;
			this.logger.log('Got owner ID', { ownerId });
			const brandName = patientDetails.clinic?.brand?.name || '';
			const phoneNumbers = patientDetails.clinic?.phoneNumbers || [];
			const clinicContact =
				phoneNumbers.length > 0
					? `${phoneNumbers[0]?.country_code || ''}${phoneNumbers[0]?.number || ''}`
					: '';
			// Remove the line that only gets the first owner
			// const petOwnerDetail = patientDetails.patientOwners?.[0]?.ownerBrand;

			// Process each invoice reference ID sequentially
			const invoicesWithDates = [];
			for (const referenceAlphaId of sortedInvoiceIds) {
				this.logger.log(`Processing invoice ${referenceAlphaId}`);

				let invoice;
				try {
					invoice = await this.invoiceEntity.findOne({
						where: { referenceAlphaId }
					});

					if (!invoice) {
						this.logger.warn(
							`Invoice not found: ${referenceAlphaId}, skipping`
						);
						continue;
					}

					// Get the PDF buffer and store with invoice date
					const buffer =
						await this.getInvoicePdfBuffer(referenceAlphaId);
					invoicesWithDates.push({
						buffer,
						date: invoice.createdAt || new Date(),
						referenceAlphaId
					});
				} catch (error) {
					this.logger.error(
						`Error processing invoice: ${referenceAlphaId}`,
						{ error }
					);
					continue;
				}
			}

			if (invoicesWithDates.length === 0) {
				this.logger.error('No valid invoice documents found');
				throw new HttpException(
					'No valid invoice documents found',
					HttpStatus.NOT_FOUND
				);
			}

			// Sort invoices by date (newest first)
			this.logger.log('Sorting invoices by date (newest first)');
			invoicesWithDates.sort(
				(a, b) => b.date.getTime() - a.date.getTime()
			);

			// Create array of sorted buffers
			const sortedBuffers = invoicesWithDates.map(item => item.buffer);
			this.logger.log('Sorted invoices for merging', {
				order: invoicesWithDates.map(i => ({
					id: i.referenceAlphaId,
					date: i.date
				}))
			});

			// Merge the PDFs into a single buffer
			this.logger.log('Merging PDF buffers', {
				count: sortedBuffers.length
			});

			// Validate each buffer before merging
			sortedBuffers.forEach((buffer, index) => {
				if (!buffer || buffer.length === 0) {
					throw new Error(`Invalid PDF buffer at index ${index}`);
				}
			});

			// Set expiration for 24 hours in the future
			const expirationDate = new Date();
			expirationDate.setHours(expirationDate.getHours() + 24);

			// Create a new document record with isGenerating=true
			const newDocument = await this.mergedInvoiceDocumentRepository.save(
				{
					ownerId,
					requestId,
					invoiceIdsHash: crypto
						.createHash('md5')
						.update(sortedInvoiceIds.join(','))
						.digest('hex'),
					invoiceIds: sortedInvoiceIds,
					isGenerating: true,
					expiresAt: expirationDate
				}
			);

			// Merge PDFs
			const mergedPdf = await PDFDocument.create();
			for (const pdfBytes of sortedBuffers) {
				const pdf = await PDFDocument.load(new Uint8Array(pdfBytes));
				const copiedPages = await mergedPdf.copyPages(
					pdf,
					pdf.getPageIndices()
				);
				copiedPages.forEach(page => mergedPdf.addPage(page));
			}
			const mergeBuffers = Buffer.from(await mergedPdf.save());
			this.logger.log('PDFs merged successfully', {
				mergedSize: mergeBuffers.length
			});

			// Generate a new file key for the merged document using the requestId
			const fileKey = `ledger/${requestId}`;
			this.logger.log('Generated file key', { fileKey });

			try {
				// Upload the merged PDF to S3
				await this.s3Service.uploadBuffer(mergeBuffers, fileKey);

				// Update the document record to mark as complete
				await this.mergedInvoiceDocumentRepository.update(
					newDocument.id,
					{
						fileKey,
						isGenerating: false,
						updatedAt: new Date()
					}
				);

				this.logger.log('Successfully processed merged document', {
					requestId,
					fileKey
				});

				// Create a filename for the download
				const fileName = `invoices_${moment().format('DDMMMYYYY')}.pdf`;

				// Share the document based on the share mode
				if (type === 'other' && isProductionOrUat()) {
					if (shareMode.includes('email')) {
						await this.makeTemplateAndSendMailForLedgerDocuments({
							brandName: brandName,
							clientEmail: email,
							clientName: 'Recipient', // Remove pet owner name for "other" type
							clinicContact: clinicContact,
							fileNames: [fileName],
							pdfBuffer: [mergeBuffers],
							subject: 'Your Invoice Attachment is here'
						});

						this.logger.log(
							'merged pdf is shared on email, type',
							type
						);
					}

					if (shareMode.includes('whatsapp')) {
						setTimeout(async () => {
							try {
								const viewUrl =
									await this.s3Service.getViewPreSignedUrl(
										fileKey
									);
								const templateArgs = {
									brandName: brandName,
									clientName: 'Recipient', // Remove pet owner name for "other" type
									clinicContact: clinicContact,
									EMRFile: viewUrl,
									mobileNumber: phoneNumber
								};
								const {
									mobileNumber,
									templateName,
									valuesArray
								} = selectTemplate(
									patientDetails.clinic,
									templateArgs,
									getLedgerDocumentTemplateData,
									getLedgerDocumentClinicLinkTemplateData
								);

								await this.whatsappService.sendTemplateMessage({
									templateName,
									valuesArray,
									mobileNumber
								});

								this.logger.log(
									'merged pdf is shared on whatsapp, type',
									type
								);
							} catch (error) {
								this.logger.log(
									'Error in WhatsApp sending or file deletion:',
									error
								);
							}
						}, 3000);
					}
				} else if (type === 'client' && isProductionOrUat()) {
					// Log the number of owners
					this.logger.log('Processing owners for client type', {
						ownerCount: patientDetails.patientOwners?.length || 0
					});

					// Loop through all pet owners
					for (const patientOwner of patientDetails.patientOwners ||
						[]) {
						const ownerDetail = patientOwner.ownerBrand;

						this.logger.log('Processing owner', {
							ownerName: `${ownerDetail?.firstName || ''} ${ownerDetail?.lastName || ''}`,
							hasEmail: !!ownerDetail?.email,
							hasWhatsApp: !!ownerDetail?.globalOwner?.phoneNumber
						});

						// Send email if the owner has an email address
						if (shareMode.includes('email') && ownerDetail?.email) {
							await this.makeTemplateAndSendMailForLedgerDocuments(
								{
									brandName: brandName,
									clientEmail: ownerDetail.email,
									clientName: `${ownerDetail.firstName || ''} ${ownerDetail.lastName || ''}`,
									clinicContact: clinicContact,
									fileNames: [fileName],
									pdfBuffer: [mergeBuffers],
									subject: 'Your Invoice Attachment is here'
								}
							);

							this.logger.log(
								'merged pdf is shared on email to owner',
								{
									type,
									owner: `${ownerDetail.firstName || ''} ${ownerDetail.lastName || ''}`,
									email: ownerDetail.email
								}
							);
						}

						// Send WhatsApp if the owner has a phone number
						if (
							shareMode.includes('whatsapp') &&
							ownerDetail?.globalOwner?.phoneNumber
						) {
							setTimeout(async () => {
								try {
									const viewUrl =
										await this.s3Service.getViewPreSignedUrl(
											fileKey
										);
									const templateArgs = {
										brandName: brandName,
										clientName: `${ownerDetail.firstName || ''} ${ownerDetail.lastName || ''}`,
										clinicContact: clinicContact,
										EMRFile: viewUrl,
										mobileNumber: `${ownerDetail.globalOwner.countryCode}${ownerDetail.globalOwner.phoneNumber}`
									};
									const {
										mobileNumber,
										templateName,
										valuesArray
									} = selectTemplate(
										patientDetails.clinic,
										templateArgs,
										getLedgerDocumentTemplateData,
										getLedgerDocumentClinicLinkTemplateData
									);

									this.logger.log(
										'Sending WhatsApp to owner',
										{
											owner: `${ownerDetail.firstName || ''} ${ownerDetail.lastName || ''}`,
											phone: ownerDetail.globalOwner
												.phoneNumber
										}
									);

									await this.whatsappService.sendTemplateMessage(
										{
											templateName,
											valuesArray,
											mobileNumber
										}
									);

									this.logger.log(
										'merged pdf is shared on whatsapp to owner',
										{
											type,
											owner: `${ownerDetail.firstName || ''} ${ownerDetail.lastName || ''}`,
											phone: ownerDetail.globalOwner
												.phoneNumber
										}
									);
								} catch (error) {
									this.logger.log(
										'Error in WhatsApp sending for owner',
										{
											error,
											owner: `${ownerDetail.firstName || ''} ${ownerDetail.lastName || ''}`,
											phone: ownerDetail.globalOwner
												.phoneNumber
										}
									);
								}
							}, 3000);
						}
					}
				}

				// Schedule cleanup for expired documents
				this.cleanupExpiredDocuments();

				return { success: true, fileKey };
			} catch (error) {
				// Mark document as failed
				await this.mergedInvoiceDocumentRepository.update(
					newDocument.id,
					{
						isGenerating: false
					}
				);

				this.logger.error('Error in shareledgerDocuments', error);
				throw new HttpException(
					'Failed to process invoice documents',
					HttpStatus.INTERNAL_SERVER_ERROR
				);
			}
		} catch (error) {
			this.logger.error('Error in shareledgerDocuments', error);
			throw new HttpException(
				'Failed to process invoice documents',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	async storeLedgerDocuments(
		invoiceReferenceIds: Array<string>,
		requestId?: string,
		filters?: {
			userId?: string;
			status?: string;
			startDate?: string;
			endDate?: string;
			searchTerm?: string;
		},
		userContext?: {
			userId: string;
			clinicId: string;
			brandId: string;
			ownerId: string;
		}
	) {
		// Sort invoice IDs alphabetically for consistent hash generation
		const sortedIds = [...invoiceReferenceIds].sort();
		const hash = crypto
			.createHash('md5')
			.update(sortedIds.join(','))
			.digest('hex');

		// If no requestId provided, generate one
		const docRequestId = requestId || uuidv4();

		this.logger.log('Processing ledger document generation', {
			invoiceCount: sortedIds.length,
			requestId: docRequestId
		});

		// Get the first invoice to retrieve owner information
		const firstInvoice = await this.invoiceRepository.findOne({
			where: { referenceAlphaId: sortedIds[0] }
		});

		if (!firstInvoice) {
			this.logger.error('Failed to find invoice', {
				referenceId: sortedIds[0]
			});
			throw new HttpException('Invoice not found', HttpStatus.NOT_FOUND);
		}

		const ownerId = firstInvoice.ownerId;

		// Create new document record with isGenerating flag and expiration date
		// Set expiration for 24 hours in the future
		const expirationDate = new Date();
		expirationDate.setHours(expirationDate.getHours() + 24);

		const newDocument = await this.mergedInvoiceDocumentRepository.save({
			ownerId: ownerId,
			requestId: docRequestId,
			invoiceIdsHash: hash,
			invoiceIds: sortedIds,
			isGenerating: true,
			expiresAt: expirationDate
		});

		this.logger.log('Created new merged document record', {
			id: newDocument.id,
			requestId: docRequestId,
			expiresAt: expirationDate
		});

		// Process each invoice reference ID sequentially
		const invoicesWithDates = [];
		const pdfBlobs = [];

		for (const invoiceRefId of sortedIds) {
			try {
				const buffer = await this.getInvoicePdfBuffer(invoiceRefId);
				if (buffer) {
					pdfBlobs.push(buffer);
					invoicesWithDates.push({
						id: invoiceRefId,
						date: new Date()
					});
				}
			} catch (error) {
				this.logger.error('Error processing invoice PDF', {
					invoiceRefId,
					error
				});
			}
		}

		// If no valid documents, mark the document as failed and exit
		if (pdfBlobs.length === 0) {
			await this.mergedInvoiceDocumentRepository.update(newDocument.id, {
				isGenerating: false
			});

			this.logger.error(
				'No valid invoice PDFs found, marked document as not generating',
				{
					documentId: newDocument.id,
					requestId: docRequestId
				}
			);
			throw new HttpException(
				'No valid invoice PDFs found',
				HttpStatus.BAD_REQUEST
			);
		}

		try {
			// Merge PDFs into single document
			const PDFDocument = require('pdf-lib').PDFDocument;
			const mergedPdf = await PDFDocument.create();

			for (const pdfBuffer of pdfBlobs) {
				const pdf = await PDFDocument.load(pdfBuffer);
				const copiedPages = await mergedPdf.copyPages(
					pdf,
					pdf.getPageIndices()
				);
				copiedPages.forEach((page: any) => mergedPdf.addPage(page));
			}

			const mergedPdfBuffer = Buffer.from(await mergedPdf.save());

			// Generate a unique file key for the merged document
			const fileNameForS3 = `${docRequestId}`;
			const fileKey = `ledger/${fileNameForS3}`;

			// Upload merged PDF to S3
			await this.s3Service.uploadBuffer(
				mergedPdfBuffer,
				fileKey,
				'application/pdf'
			);

			// Update document record with file key and mark as complete
			await this.mergedInvoiceDocumentRepository.update(newDocument.id, {
				fileKey: fileKey,
				isGenerating: false
			});

			this.logger.log('Merged PDF created and uploaded successfully', {
				documentId: newDocument.id,
				requestId: docRequestId,
				fileKey: fileKey
			});

			// Create tab activities for each invoice after successful processing
			if (userContext && userContext.userId && userContext.clinicId && userContext.brandId && userContext.ownerId) {
				this.logger.log('Creating tab activities for processed invoices', {
					invoiceCount: sortedIds.length,
					requestId: docRequestId
				});

				for (const paymentId of sortedIds) {
					try {
						await this.tabActivitiesService.create({
							clinicId: userContext.clinicId,
							brandId: userContext.brandId,
							tabName: TabName.INVOICES,
							actionType: ActionType.DOWNLOAD,
							referenceId: paymentId
						}, userContext.userId);

						this.logger.log('Successfully created tab activity', {
							paymentId,
							requestId: docRequestId
						});
					} catch (err) {
						const error = err as Error;
						this.logger.error(`Failed to create tab activity for payment ID ${paymentId}: ${error.message}`, {
							error,
							paymentId,
							requestId: docRequestId
						});
						// Continue processing other activities even if one fails
					}
				}

				this.logger.log('Completed tab activity creation', {
					invoiceCount: sortedIds.length,
					requestId: docRequestId
				});
			} else {
				this.logger.warn('Cannot create tab activities - missing user context', {
					hasUserContext: !!userContext,
					requestId: docRequestId
				});
			}

			// Schedule cleanup for expired documents older than 24 hours
			this.cleanupExpiredDocuments();

			return fileKey;
		} catch (error) {
			// Mark document as failed
			await this.mergedInvoiceDocumentRepository.update(newDocument.id, {
				isGenerating: false
			});

			this.logger.error('Error creating merged PDF', {
				documentId: newDocument.id,
				requestId: docRequestId,
				error
			});

			throw new HttpException(
				'Failed to create merged PDF',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	// New method to clean up expired documents
	private async cleanupExpiredDocuments() {
		try {
			const now = new Date();
			const expiredDocuments =
				await this.mergedInvoiceDocumentRepository.find({
					where: {
						expiresAt: LessThan(now)
					},
					take: 100 // Limit number of documents to process in one batch
				});

			if (expiredDocuments.length === 0) {
				return;
			}

			this.logger.log(
				`Found ${expiredDocuments.length} expired documents to clean up`
			);

			for (const doc of expiredDocuments) {
				// Delete S3 file if it exists
				if (doc.fileKey) {
					try {
						await this.s3Service.deleteFile(doc.fileKey);
						this.logger.log('Deleted expired document S3 file', {
							fileKey: doc.fileKey
						});
					} catch (err) {
						this.logger.error(
							'Error deleting expired document S3 file',
							{
								fileKey: doc.fileKey,
								error: err
							}
						);
					}
				}

				// Delete database record
				await this.mergedInvoiceDocumentRepository.delete(doc.id);
				this.logger.log('Deleted expired document record', {
					id: doc.id
				});
			}
		} catch (error) {
			this.logger.error('Error cleaning up expired documents', {
				error
			});
		}
	}

	/**
	 * Retrieves the PDF buffer for a given invoice ID.
	 * - If a permanent file exists in S3, it downloads and returns its buffer.
	 * - Otherwise, it generates a new PDF buffer using generateDocumentPdf.
	 * @param invoiceId The ID of the invoice
	 * @returns A Promise resolving to the PDF buffer
	 * @throws NotFoundException if the invoice or required details are not found
	 */
	async getInvoicePdfBuffer(referenceAlphaId: string): Promise<Buffer> {
		// Step 1: Find the invoice by ID
		const invoice = await this.invoiceRepository.findOne({
			where: { referenceAlphaId }
		});
		if (!invoice) {
			throw new NotFoundException(
				`Invoice with ID ${referenceAlphaId} not found`
			);
		}

		// Step 2: Determine if it's an invoice or credit note
		const isInvoice = invoice.invoiceType === EnumInvoiceType.Invoice;

		// Step 3: Check for an existing permanent file key in fileUrl
		const fileUrl = (invoice.fileUrl as Record<string, string>) || {};
		const existingFileKey = isInvoice
			? fileUrl.invoiceFileKey
			: fileUrl.creditNoteFileKey;

		const isPermanentFile =
			existingFileKey &&
			(isInvoice
				? existingFileKey.startsWith('invoice/')
				: existingFileKey.startsWith('creditNote/'));

		// Step 4: If a permanent file exists, try to download it
		if (isPermanentFile) {
			try {
				const viewSignedUrl =
					await this.s3Service.getViewPreSignedUrl(existingFileKey);
				const pdfBuffer = await this.downloadPDF(viewSignedUrl);
				return pdfBuffer;
			} catch (error) {
				this.logger.error(
					`Error downloading existing PDF for invoice ${referenceAlphaId}`,
					error
				);
				// If download fails, proceed to generate a new PDF
			}
		}

		// Step 5: If no permanent file or download failed, generate a new PDF
		// Fetch patient details with necessary relations
		const patientDetails = await this.patientRepository.findOne({
			where: { id: invoice.patientId },
			relations: [
				'clinic',
				'clinic.brand',
				'patientOwners',
				'patientOwners.ownerBrand',
				'patientOwners.ownerBrand.globalOwner'
			]
		});
		if (!patientDetails) {
			throw new NotFoundException(
				`Patient details not found for invoice ${referenceAlphaId}`
			);
		}

		const clinicDetails = patientDetails.clinic;
		if (!clinicDetails) {
			throw new NotFoundException(
				`Clinic details not found for invoice ${referenceAlphaId}`
			);
		}

		const patientOwner = patientDetails.patientOwners?.[0];
		if (!patientOwner?.ownerBrand) {
			throw new NotFoundException(
				`Owner details not found for invoice ${referenceAlphaId}`
			);
		}
		const ownerDetails = patientOwner.ownerBrand;

		// Generate the PDF buffer
		const pdfBuffer = await this.generateDocumentPdf(
			invoice,
			patientDetails,
			clinicDetails,
			ownerDetails
		);

		return pdfBuffer;
	}

	// Helper method to generate PDF document
	private async generateDocumentPdf(
		invoice: InvoiceEntity,
		patientDetails: any,
		clinicDetails: any,
		ownerDetails: any
	): Promise<Buffer> {
		// Get payment details
		const paymentDetails = await this.paymentDetailsRepository.find({
			where: { invoiceId: invoice.id },
			order: { createdAt: 'ASC' }
		});

		// Get refund items if applicable
		let refundItems: Array<{
			creditNote: string;
			amount: number;
			date: string;
		}> = [];

		if (invoice.invoiceType === EnumInvoiceType.Refund) {
			// This is already a refund invoice, no need to fetch additional items
		} else {
			// Check for any refund invoices linked to this invoice
			const refundInvoices = await this.invoiceRepository.find({
				where: {
					cartId: invoice.cartId,
					invoiceType: EnumInvoiceType.Refund
				}
			});

			refundItems = refundInvoices.map(refundInvoice => ({
				creditNote: refundInvoice.referenceAlphaId || '',
				amount: Number(refundInvoice.invoiceAmount) || 0,
				date: moment(refundInvoice.createdAt).format('YYYY-MM-DD')
			}));
		}

		// Format payment items - separate cash/card payments from credit payments
		const normalPayments = paymentDetails.filter(
			payment =>
				(payment.type === EnumAmountType.Invoice ||
					payment.type === EnumAmountType.ReconcileInvoice ||
					payment.type === EnumAmountType.BulkReconcileInvoice ||
					payment.type === EnumAmountType.Collect) &&
				Number(payment.amount) > 0 &&
				!payment.isCreditUsed
		);

		const creditPayments = paymentDetails.filter(
			payment =>
				payment.isCreditUsed && Number(payment.creditAmountUsed) > 0
		);

		const paymentItems: Array<{
			date: string;
			paymentMode: string;
			receiptNumber: string;
			amount: number;
		}> = [
			...[...normalPayments, ...creditPayments]
				.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())
				.map(payment => ({
					date: moment(payment.createdAt).format('YYYY-MM-DD HH:mm'),
					paymentMode: payment.isCreditUsed
						? 'Credits'
						: payment.paymentType || '',
					receiptNumber: payment.referenceAlphaId || '',
					amount: payment.isCreditUsed
						? Number(payment.creditAmountUsed) || 0
						: Number(payment.amount) || 0
				}))
		];

		// Extract line items from invoice details
		const lineItems = (invoice.details || []).map((item: any) => ({
			description: item.name,
			quantity: item.quantity,
			price: Number(item.actualPrice)
		}));

		// Calculate pet details string
		const petDetails = patientDetails.species
			? `${patientDetails.species || ''} - ${patientDetails.breed?.split('_').join(' ') || ''}`
					.toLowerCase()
					.replace(/\b\w/g, char => char.toUpperCase())
			: '';

		// Handle clinic logo URL
		let clinicLogoUrl =
			clinicDetails?.logoUrl || clinicDetails?.clinicLogo || '';

		// Get pre-signed URL for clinic logo if it exists and is an S3 path
		if (clinicLogoUrl && clinicLogoUrl.startsWith('clinicLogo/')) {
			try {
				const logoPreSignedUrl =
					await this.s3Service.getViewPreSignedUrl(clinicLogoUrl);
				clinicLogoUrl = logoPreSignedUrl;
			} catch (error) {
				this.logger.error(
					'Error generating pre-signed URL for clinic logo',
					error
				);
			}
		}

		// Generate the appropriate PDF HTML based on invoice type
		let pdfHtml = '';

		if (invoice.invoiceType === EnumInvoiceType.Invoice) {
			// Prepare invoice data
			const invoiceData: InvoiceData = {
				invoiceNumber: invoice.referenceAlphaId || '',
				invoiceDate: moment(invoice.createdAt).format('MMMM D, YYYY'),
				clinicName: clinicDetails.name || '',
				clinicAddress: this.getClinicAddress(patientDetails),
				clinicPhone:
					clinicDetails.phoneNumbers &&
					clinicDetails.phoneNumbers.length > 0
						? clinicDetails.phoneNumbers[0].number || ''
						: clinicDetails.mobile || '',
				clinicEmail: clinicDetails.email || '',
				clinicWebsite: clinicDetails.website || '',
				customerName: `${ownerDetails.firstName || ''} ${ownerDetails.lastName || ''}`,
				petName: patientDetails.patientName || '',
				petDetails,
				customerEmail: ownerDetails.email || '',
				customerPhone: ownerDetails.globalOwner
					? `${ownerDetails.globalOwner.countryCode}${ownerDetails.globalOwner.phoneNumber}`
					: '',
				clinicLogoUrl: clinicLogoUrl,
				// Receipt details
				receiptDate:
					paymentDetails.length > 0
						? moment(
								paymentDetails[paymentDetails.length - 1]
									.createdAt
							).format('Do MMM YYYY')
						: '',
				paymentMode: invoice.paymentMode || '',
				receiptNumber:
					paymentDetails.length > 0
						? paymentDetails[paymentDetails.length - 1]
								.referenceAlphaId || ''
						: '',
				// Credits and refunds
				creditsUsed: paymentDetails.reduce(
					(sum, payment) =>
						sum + (Number(payment.creditAmountUsed) || 0),
					0
				),
				refunds: refundItems.reduce(
					(sum, refund) => sum + (refund.amount || 0),
					0
				),
				refundCreditNote:
					refundItems.length > 0 ? refundItems[0].creditNote : '',
				refundAmount:
					refundItems.length > 0 ? refundItems[0].amount : 0,
				refundDate: refundItems.length > 0 ? refundItems[0].date : '',
				// Payment and refund items
				paymentItems,
				refundItems,
				// Invoice line items
				lineItems,
				// Invoice totals
				subtotal: Number(invoice.totalPrice) || 0,
				taxes: Number(invoice.totalTax) || 0,
				discount: Number(invoice.totalDiscount) || 0,
				previousBalance: Number(invoice.totalCredit) || 0,
				invoiceAmount: Number(invoice.invoiceAmount) || 0,
				totalDue: Number(invoice.invoiceAmount) || 0,
				amountPaid: Number(invoice.amountPaid) || 0,
				balanceDue: Number(invoice.balanceDue) || 0,
				// Writeoff and cancellation data from metadata
				writeoff: invoice.metadata?.writeoff
					? {
							amount:
								Number(invoice.metadata.writeoff.amount) || 0,
							date: invoice.metadata.writeoff.date || '',
							reason: invoice.metadata.writeoff.reason || '',
							by: invoice.metadata.writeoff.by || ''
						}
					: undefined,
				cancellation: invoice.metadata?.cancellation
					? {
							amount:
								Number(invoice.metadata.cancellation.amount) ||
								0,
							date: invoice.metadata.cancellation.date || '',
							reason: invoice.metadata.cancellation.reason || '',
							by: invoice.metadata.cancellation.by || ''
						}
					: undefined
			};

			// Generate invoice HTML
			pdfHtml = generateNewInvoice(invoiceData);
		} else if (invoice.invoiceType === EnumInvoiceType.Refund) {
			// Get original invoice
			const originalInvoice = await this.invoiceRepository.findOne({
				where: {
					cartId: invoice.cartId,
					invoiceType: EnumInvoiceType.Invoice
				}
			});

			// Find specific payment details
			const creditNotePayment = paymentDetails.find(
				payment => payment.type === EnumAmountType.CreditNote
			);
			const collectPayment = paymentDetails.find(
				payment =>
					payment.type === EnumAmountType.Collect &&
					payment.isCreditsAdded
			);

			// Prepare credit note data
			const creditNoteData: CreditNoteData = {
				creditNoteNumber: invoice.referenceAlphaId || '',
				creditNoteDate: moment(invoice.createdAt).format(
					'MMMM D, YYYY'
				),
				clinicName: clinicDetails.name || '',
				clinicAddress: this.getClinicAddress(patientDetails),
				clinicPhone:
					clinicDetails.phoneNumbers &&
					clinicDetails.phoneNumbers.length > 0
						? clinicDetails.phoneNumbers[0].number || ''
						: clinicDetails.mobile || '',
				clinicEmail: clinicDetails.email || '',
				clinicWebsite: clinicDetails.website || '',
				customerName: `${ownerDetails.firstName || ''} ${ownerDetails.lastName || ''}`,
				petName: patientDetails.patientName || '',
				petDetails,
				lineItems,
				adjustments:
					Number(invoice.totalTax) + Number(invoice.totalDiscount) ||
					0,
				totalDue: Number(invoice.amountPayable) || 0,
				amountPaid: creditNotePayment
					? Number(creditNotePayment.amount) || 0
					: 0,
				balanceDue: Number(invoice.balanceDue) || 0,
				invoiceDate: originalInvoice
					? moment(originalInvoice.createdAt).format('MMMM D, YYYY')
					: '',
				invoiceId: originalInvoice
					? originalInvoice.referenceAlphaId || ''
					: '',
				clinicLogoUrl: clinicLogoUrl,
				refundAmount: Number(invoice.invoiceAmount) || 0,
				referenceInvoice: originalInvoice
					? originalInvoice.referenceAlphaId || ''
					: '',
				// Credit Note payment receipt info
				receiptDate: creditNotePayment
					? moment(creditNotePayment.createdAt).format('Do MMM YYYY')
					: moment(invoice.createdAt).format('Do MMM YYYY'),
				paymentMode: creditNotePayment
					? creditNotePayment.paymentType || ''
					: invoice.paymentMode || '',
				receiptNumber: creditNotePayment
					? creditNotePayment.referenceAlphaId || ''
					: '',
				// Collect payment receipt info (for credits)
				receiptNumberCredits: collectPayment
					? collectPayment.referenceAlphaId || ''
					: '',
				creditsAdded: collectPayment
					? Number(collectPayment.creditAmountAdded) || 0
					: 0
			};

			// Generate credit note HTML
			pdfHtml = generateNewCreditNote(creditNoteData);
		}

		// Generate PDF from HTML
		return await generatePDF(pdfHtml);
	}

	async sendUpdatedPrescriptionDocument(
		appointmentId: string,
		shareMode: Array<string>,
		type: 'client' | 'other',
		contactNo: string,
		email: string
	) {
		const checkPrescriptionUpdateStatus =
			await this.checkPrescriptionIsUpdated(appointmentId);
		this.logger.log(
			'checking status for prescription is updated or not',
			checkPrescriptionUpdateStatus
		);

		if (checkPrescriptionUpdateStatus) {
			// create
			this.logger.log(
				'start creating new prescription pdf',
				appointmentId
			);

			await this.createUpdatedPrescriptionPdf(appointmentId);
		}
		// share the prescription
		this.logger.log('start sharing the new prescription pdf', {
			type,
			shareMode,
			contactNo,
			email
		});
		return await this.sendPrescriptionDocument(
			appointmentId,
			shareMode,
			type,
			contactNo,
			email
		);
	}

	async checkPrescriptionIsUpdated(appointmentId: string) {
		const response = await this.appointmentDetailsEntity.findOne({
			where: { appointmentId }
		});
		this.logger.log(
			'checking response for appointmentDetailEntity',
			response
		);

		if (
			response?.updatedAt?.getTime() ===
			response?.prescriptionCreatedAt?.getTime()
		) {
			return false;
		}
		return true;
	}

	async deleteOldPrescriptionFileKey(appointmentId: string) {
		this.logger.log(
			'start finding appointment detail for invoice object',
			appointmentId
		);
		const appointmentResponse = await this.appointmentRepository.findOne({
			where: { id: appointmentId, deletedAt: IsNull() },
			relations: ['cart', 'cart.invoice'],
			order: { date: 'DESC', startTime: 'DESC' }
		});

		const invoice = appointmentResponse?.cart?.invoice?.find(
			(inv: any) => inv.invoiceType === EnumInvoiceType.Invoice
		);

		if (!invoice) {
			this.logger.log(
				'not fetch any invoice for the requested appointmentId',
				appointmentId
			);
			return;
		}

		const fileUrl: any = invoice?.fileUrl;
		const oldPrescriptionFileKey = fileUrl?.prescriptionFileKey;
		if (oldPrescriptionFileKey) {
			this.logger.log(
				'found the old prescripiton fileKey',
				oldPrescriptionFileKey
			);
			await this.s3Service.deleteFile(oldPrescriptionFileKey);
		}
		const { prescriptionFileKey, ...newfileUrl } = fileUrl;
		this.logger.log('new updated fileUrlObject', newfileUrl);
		await this.invoiceEntity?.update(invoice.id, {
			fileUrl: newfileUrl
		});
		this.logger.log(
			'deleted prescription old fileKey successfully',
			appointmentId
		);
	}

	async sendPrescriptionFileKey(appointmentId: string) {
		this.logger.log(
			'start finding appointment detail for invoice object',
			appointmentId
		);
		const appointmentResponse = await this.appointmentRepository.findOne({
			where: { id: appointmentId, deletedAt: IsNull() },
			relations: ['cart', 'cart.invoice'],
			order: { date: 'DESC', startTime: 'DESC' }
		});

		const invoice = appointmentResponse?.cart?.invoice?.find(
			(inv: any) => inv.invoiceType === EnumInvoiceType.Invoice
		);

		if (!invoice) {
			this.logger.log(
				'not fetch any invoice for the requested appointmentId',
				appointmentId
			);
			return;
		}

		const fileUrl: any = invoice?.fileUrl;
		const oldPrescriptionFileKey = fileUrl?.prescriptionFileKey;

		return oldPrescriptionFileKey;
	}

	async createUpdatedPrescriptionPdf(appointmentId: string) {
		this.logger.log('start creating new prescription pdf', appointmentId);

		const appointmentResponse = await this.appointmentRepository.findOne({
			where: { id: appointmentId, deletedAt: IsNull() },
			relations: [
				'cart',
				'cart.invoice',
				'patient',
				'patient.patientOwners',
				'patient.patientOwners.ownerBrand',
				'patient.patientOwners.ownerBrand.globalOwner',
				'clinic',
				'clinic.brand'
			],
			order: { date: 'DESC', startTime: 'DESC' }
		});

		const invoice = appointmentResponse?.cart?.invoice?.find(
			(inv: any) => inv.invoiceType === EnumInvoiceType.Invoice
		);

		if (!invoice) {
			this.logger.log('not found the invoice', invoice);
			return;
		}
		const appointmentDoctorResponse =
			await this.appointmentDoctorsEntity.findOne({
				where: {
					appointmentId: appointmentId,
					primary: true
				},
				relations: [
					'clinicUser',
					'clinicUser.user',
					'clinicUser.clinic.brand'
				]
			});

		const { licenseNumber, digitalSignature } = appointmentDoctorResponse
			?.clinicUser?.user as {
			licenseNumber: string;
			digitalSignature: string;
		};
		const doctorName = `${appointmentDoctorResponse?.clinicUser?.user?.firstName} ${appointmentDoctorResponse?.clinicUser?.user?.lastName}`;
		const appointmentDetailResponse: any =
			await this.appointmentDetailsEntity.findOne({
				where: { appointmentId }
			});
		const prescriptionList =
			appointmentDetailResponse?.details?.prescription?.list || [];

		const medicationList = prescriptionList
			?.map((list: any) => {
				return {
					medication: list.name,
					comments: list.comment ?? ''
				};
			})
			.filter(({ medication }: any) => medication);

		const dischargeInstruction =
			appointmentDetailResponse?.details?.prescription?.notes || '';
		const prescriptionAlphaId = invoice?.prescriptionReferenceAlphaId;

		// Extract and format follow-up appointment date if present
		const followUpRawData =
			appointmentDetailResponse?.details?.followup || null;
		let formattedFollowUpText = null;

		if (followUpRawData) {
			formattedFollowUpText = `Follow-up due: ${followUpRawData.label}.`;
		}

		const petDetails = appointmentResponse?.patient?.species
			? `${appointmentResponse?.patient?.species || ''} - ${appointmentResponse?.patient?.breed?.split('_').join(' ') || ''}`
					.toLowerCase()
					.replace(/\b\w/g, char => char.toUpperCase())
			: '';

		let clinicLogoUrl =
			appointmentResponse?.clinic?.logoUrl ||
			appointmentResponse?.clinic?.clinicLogo ||
			'';

		// Get pre-signed URL for clinic logo if it exists and is an S3 path
		if (clinicLogoUrl && clinicLogoUrl.startsWith('clinicLogo/')) {
			try {
				const logoPreSignedUrl =
					await this.s3Service.getViewPreSignedUrl(clinicLogoUrl);
				clinicLogoUrl = logoPreSignedUrl;
			} catch (error) {
				this.logger.error(
					'Error generating pre-signed URL for clinic logo',
					error
				);
			}
		}
		const prescriptionData = {
			clinicLogoUrl: clinicLogoUrl,
			prescriptionId: prescriptionAlphaId || '',
			prescriptionDate: appointmentResponse?.date
				? moment(appointmentResponse.date).format('DD MMM YYYY')
				: moment().format('DD MMM YYYY'),
			clinicName: appointmentResponse?.clinic?.name || '',
			clinicAddress: appointmentResponse
				? this.getClinicAddress(appointmentResponse as any)
				: '',
			clinicPhone:
				appointmentResponse?.clinic?.phoneNumbers[0].number || '',
			clinicEmail: appointmentResponse?.clinic?.email || '',
			clinicWebsite: appointmentResponse?.clinic?.website || '',
			customerName: `${appointmentResponse?.patient?.patientOwners?.[0]?.ownerBrand?.firstName || ''} ${appointmentResponse?.patient?.patientOwners?.[0]?.ownerBrand?.lastName || ''}`,
			petName: appointmentResponse?.patient?.patientName || '',
			petDetails,
			lineItems: medicationList || [],
			dischargeInstructions: dischargeInstruction ?? '',
			vetName: doctorName,
			vetLicenseNo: licenseNumber ?? '',
			digitalSignature: digitalSignature ?? '',
			customerEmail:
				appointmentResponse?.patient?.patientOwners?.[0]?.ownerBrand
					?.email || '',
			customerPhone: `${appointmentResponse?.patient?.patientOwners?.[0]?.ownerBrand?.globalOwner?.countryCode}${appointmentResponse?.patient?.patientOwners?.[0]?.ownerBrand?.globalOwner?.phoneNumber}`,
			// Add follow-up date if present - formatted appropriately
			followUpDate: formattedFollowUpText
		};
		this.logger.log('pdf document data for creating pdf', {
			prescriptionData,
			followUpDate: formattedFollowUpText
		});

		const prescriptionHtml = generatePrescription(prescriptionData);

		const pdfBuffer = await generatePDF(prescriptionHtml);
		const prescriptionFileKey = `prescription/${uuidv4()}`;
		this.logger.log('new prescription fileKey', prescriptionFileKey);

		await this.s3Service.uploadPdfToS3(pdfBuffer, prescriptionFileKey);
		const fileUrl: any = invoice?.fileUrl;
		const oldPrescriptionFileKey = fileUrl?.prescriptionFileKey;
		if (oldPrescriptionFileKey) {
			this.logger.log(
				'deleting existing prescription fileKey',
				oldPrescriptionFileKey
			);
			await this.s3Service.deleteFile(oldPrescriptionFileKey);
		}
		const newfileUrl = {
			...fileUrl,
			prescriptionFileKey: prescriptionFileKey
		};
		await this.invoiceEntity?.update(invoice.id, {
			fileUrl: newfileUrl
		});
		this.logger.log('updating invoice entity', appointmentId);

		const updatedTimestamp = new Date();

		await this.appointmentDetailsEntity.update(
			{ appointmentId: appointmentId },
			{
				updatedAt: updatedTimestamp,
				prescriptionCreatedAt: updatedTimestamp
			}
		);
		this.logger.log('updating appointment detail entity', appointmentId);
	}

	getClinicAddress(patientDetail: Patient) {
		const addressParts = [];

		if (patientDetail?.clinic?.addressLine1) {
			addressParts.push(patientDetail.clinic.addressLine1);
		}

		if (patientDetail?.clinic?.city) {
			addressParts.push(patientDetail.clinic.city);
		}

		if (patientDetail?.clinic?.addressPincode) {
			addressParts.push(`- ${patientDetail.clinic.addressPincode}`);
		}

		if (patientDetail?.clinic?.state) {
			addressParts.push(patientDetail.clinic.state);
		}

		this.logger.log('Clinic Address formatted', {
			addressParts
		});
		return addressParts.join(', ').trim();
	}

	async sendPrescriptionDocument(
		appointmentId: string,
		shareMode: Array<string>,
		type: 'client' | 'other',
		contactNo: string,
		email: string
	) {
		this.logger.log(
			'start finding invoice-fileKey for appointmentId',
			appointmentId
		);
		const response: any = await this.appointmentRepository.findOne({
			where: { id: appointmentId, deletedAt: IsNull() },
			relations: [
				'cart',
				'cart.invoice',
				'patient',
				'patient.patientOwners',
				'patient.patientOwners.ownerBrand',
				'patient.patientOwners.ownerBrand.globalOwner',
				'clinic',
				'clinic.brand'
			],
			order: { date: 'DESC', startTime: 'DESC' }
		});

		const brandName = response?.clinic?.brand?.name || '';
		const patientName = response?.patient?.patientName || '';
		const invoiceFileKey =
			response?.cart?.invoice?.find(
				(inv: any) => inv.invoiceType === EnumInvoiceType.Invoice
			)?.fileUrl ?? null;

		const phoneNumbers = response?.clinic?.phoneNumbers;
		const clinicContact =
			`+${phoneNumbers?.[0]?.country_code ?? ''} ${phoneNumbers?.[0]?.number ?? ''}`.trim();

		try {
			if (invoiceFileKey?.prescriptionFileKey) {
				this.logger.log(
					' SendDocuments ~ invoiceFileKey?.prescriptionFileKey:',
					invoiceFileKey?.prescriptionFileKey
				);
				const emrUrl = await this.s3Service.getViewPreSignedUrl(
					invoiceFileKey?.prescriptionFileKey
				);

				if (type === 'other') {
					this.logger.log('SendDocuments ~ type:', type);
					const clientName = `${response?.patient?.patientOwners[0]?.ownerBrand?.firstName} ${response?.patient?.patientOwners[0]?.ownerBrand?.lastName}`;
					if (shareMode.includes('email') && isProductionOrUat()) {
						await this.sendPrescriptionViaEmail(
							clientName,
							patientName,
							brandName,
							clinicContact,
							email,
							emrUrl
						);
						this.logger.log(
							'prescription is shared on email successfully'
						);
					}

					if (shareMode.includes('whatsapp') && isProductionOrUat()) {
						await this.sendPrescriptionViaWhatsApp(
							clientName,
							patientName,
							brandName,
							clinicContact,
							contactNo,
							emrUrl,
							response?.clinic
						);
						this.logger.log(
							'prescription is shared on whatsapp successfully'
						);
					}
				} else {
					this.logger.log('SendDocuments ~ type:', type);

					response?.patient?.patientOwners.forEach(
						async (patient: any) => {
							const clientName = `${patient?.ownerBrand?.firstName || ''} ${patient?.ownerBrand?.lastName || ''}`;

							if (
								shareMode.includes('email') &&
								isProductionOrUat()
							) {
								await this.sendPrescriptionViaEmail(
									clientName,
									patientName,
									brandName,
									clinicContact,
									patient?.ownerBrand?.email,
									emrUrl
								);
								this.logger.log(
									'prescription is shared on email successfully'
								);
							}

							if (
								shareMode.includes('whatsapp') &&
								isProductionOrUat()
							) {
								await this.sendPrescriptionViaWhatsApp(
									clientName,
									patientName,
									brandName,
									clinicContact,
									patient?.ownerBrand?.globalOwner
										?.phoneNumber,
									emrUrl,
									response?.clinic
								);
								this.logger.log(
									'prescription is shared on whatsapp successfully'
								);
							}
						}
					);
				}
			} else {
				this.logger.log('prescription documents not found');
				throw new HttpException(
					'prescription document not found',
					HttpStatus.NOT_FOUND
				);
			}
		} catch (err) {
			this.logger.log('error ===>>', err);
			throw new HttpException(
				'Something went wrong, please try again later.',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	private async sendPrescriptionViaEmail(
		clientName: string,
		patientName: string,
		brandName: string,
		clinicContact: string,
		email: string,
		emrUrl: string
	) {
		const pdfBuffer = await this.downloadPDF(emrUrl);
		await this.sendMail(
			`Dear ${clientName},
			<br/><br/>
			We've attached ${patientName}’s medical records here. <br/><br/>
			If you have any questions, feel free to reach out to us at ${clinicContact}. We look forward to seeing you and ensuring you receive the best possible care!
			<br/><br/>
			Warm regards,<br/>
			The ${brandName} Team`,
			[pdfBuffer],
			['medical-record-prescription.pdf'],
			email ?? '<EMAIL>',
			`Your Pet's Medical Records Ready`
		);
		this.logger.log('email sent successfully');
	}

	private async sendPrescriptionViaWhatsApp(
		clientName: string,
		patientName: string,
		brandName: string,
		clinicContact: string,
		phoneNumber: string,
		emrUrl: string,
		clinic: any
	) {
		// Generate template data
		const templateArgs = {
			brandName,
			clientName,
			EMRFile: emrUrl,
			mobileNumber: `${phoneNumber}`,
			petName: patientName,
			clinicContact
		};
		const { mobileNumber, templateName, valuesArray } = selectTemplate(
			clinic,
			templateArgs,
			getIndividualEMRTemplateData,
			getIndividualEMRClinicLinkTemplateData
		);

		await this.whatsappService.sendTemplateMessage({
			templateName,
			valuesArray,
			mobileNumber
		});

		this.logger.log('whatsapp message sent successfully');
	}

	async sendSupportingDocument(
		appointmentId: string,
		shareMode: Array<string>,
		type: 'client' | 'other',
		contactNo: string,
		email: string
	) {
		this.logger.log(
			'start finding invoice-fileKey for appointmentId',
			appointmentId
		);

		const response: any = await this.appointmentRepository.findOne({
			where: { id: appointmentId, deletedAt: IsNull() },
			relations: [
				'appointmentDetails',
				'patient',
				'patient.patientOwners',
				'patient.patientOwners.ownerBrand',
				'patient.patientOwners.ownerBrand.globalOwner',
				'clinic',
				'clinic.brand'
			],
			order: { date: 'DESC', startTime: 'DESC' }
		});

		const brandName = response?.clinic?.brand?.name || '';
		const patientName = response?.patient?.patientName || '';
		const phoneNumbers = response?.clinic?.phoneNumbers;
		const clinicContact =
			`+${phoneNumbers?.[0]?.country_code ?? ''} ${phoneNumbers?.[0]?.number ?? ''}`.trim();

		if (
			response.appointmentDetails.details?.attachments?.list?.length > 0
		) {
			const pdfInput: Array<any> = [];

			try {
				await Promise.all(
					response.appointmentDetails.details?.attachments?.list.map(
						async (file: any) => {
							const viewSignedUrl =
								await this.s3Service.getViewPreSignedUrl(
									file.fileKey
								);
							pdfInput.push({
								url: viewSignedUrl,
								name: file?.fileName,
								isPdf: file?.fileName?.includes('.pdf')
							});
						}
					)
				);

				const documentNameArray = pdfInput.map(file => file.name);
				const pdfBuffers: Buffer[] = await Promise.all(
					pdfInput.map(async file => {
						return await this.downloadPDF(file.url);
					})
				);

				if (type === 'other') {
					this.logger.log("Sending documents to 'other' contact:", {
						email,
						contactNo
					});

					const clientName = `${response?.patient?.patientOwners[0]?.ownerBrand?.firstName} ${response?.patient?.patientOwners[0]?.ownerBrand?.lastName}`;

					if (shareMode.includes('email') && isProductionOrUat()) {
						await this.sendMail(
							`Dear ${clientName},<br/><br/>
							We've attached ${patientName}’s medical records here. <br/><br/>
							If you have any questions, feel free to reach out to us at ${clinicContact}.
							<br/><br/>Warm regards,<br/>The ${brandName} Team`,
							[...pdfBuffers],
							[...documentNameArray],
							email,
							`Your Pet's Medical Records Ready`
						);
						this.logger.log('Document sent via email successfully');
					}

					if (shareMode.includes('whatsapp') && isProductionOrUat()) {
						pdfInput.forEach(async file => {
							const { templateName, valuesArray, mobileNumber } =
								file.isPdf
									? sendMedicalRecordDocument({
											clientName,
											documentUrl: file.url,
											mobileNumber: contactNo,
											petName: patientName
										})
									: sendMedicalRecordImage({
											clientName,
											documentUrl: file.url,
											mobileNumber: contactNo,
											petName: patientName
										});

							await this.whatsappService.sendTemplateMessage({
								templateName,
								valuesArray,
								mobileNumber
							});
							this.logger.log(
								'Document sent via WhatsApp successfully'
							);
						});
					}
				} else {
					// **Send to Client (Patient's Owners)**
					response?.patient?.patientOwners.forEach(
						async (patient: any) => {
							const clientName = `${patient?.ownerBrand?.firstName || ''} ${patient?.ownerBrand?.lastName || ''}`;

							if (
								shareMode.includes('email') &&
								isProductionOrUat()
							) {
								await this.sendMail(
									`Dear ${clientName},<br/><br/>
								We've attached ${patientName}’s medical records here. <br/><br/>
								If you have any questions, feel free to reach out to us at ${clinicContact}.
								<br/><br/>Warm regards,<br/>The ${brandName} Team`,
									[...pdfBuffers],
									[...documentNameArray],
									patient?.ownerBrand?.email ??
										'<EMAIL>',
									`Your Pet's Medical Records Ready`
								);
								this.logger.log(
									'Document sent via email successfully'
								);
							}

							if (
								shareMode.includes('whatsapp') &&
								isProductionOrUat()
							) {
								pdfInput.forEach(async file => {
									const {
										templateName,
										valuesArray,
										mobileNumber
									} = file.isPdf
										? sendMedicalRecordDocument({
												clientName,
												documentUrl: file.url,
												mobileNumber: `${patient?.ownerBrand?.globalOwner?.countryCode}${patient?.ownerBrand?.globalOwner?.phoneNumber}`,
												petName: patientName
											})
										: sendMedicalRecordImage({
												clientName,
												documentUrl: file.url,
												mobileNumber: `${patient?.ownerBrand?.globalOwner?.countryCode}${patient?.ownerBrand?.globalOwner?.phoneNumber}`,
												petName: patientName
											});

									await this.whatsappService.sendTemplateMessage(
										{
											templateName,
											valuesArray,
											mobileNumber
										}
									);
									this.logger.log(
										'Document sent via WhatsApp successfully'
									);
								});
							}
						}
					);
				}
			} catch (error) {
				this.logger.error('Error while sending documents:', error);
				throw new HttpException(
					'Something went wrong, please try again later.',
					HttpStatus.INTERNAL_SERVER_ERROR
				);
			}
		} else {
			this.logger.log('Vaccination document not found');
			throw new HttpException(
				'Vaccination document not found',
				HttpStatus.NOT_FOUND
			);
		}
	}

	// Methods to collect document files without sending them immediately

	async collectInvoiceDocuments(appointmentId: string) {
		try {
			const appointment = await this.appointmentRepository.findOne({
				where: { id: appointmentId, deletedAt: IsNull() },
				relations: ['cart', 'cart.invoice']
			});

			if (!appointment?.cart?.invoice?.length) {
				return { buffers: [], fileNames: [] };
			}

			const invoice = appointment.cart.invoice.find(
				(inv: any) => inv.invoiceType === EnumInvoiceType.Invoice
			);

			// Check if we have an invoice with a fileUrl that contains invoiceFileKey
			if (!invoice?.fileUrl) {
				return { buffers: [], fileNames: [] };
			}

			// Use type assertion to access the property
			const fileUrlObj = invoice.fileUrl as { invoiceFileKey?: string };
			const invoiceFileKey = fileUrlObj.invoiceFileKey;

			if (!invoiceFileKey) {
				return { buffers: [], fileNames: [] };
			}

			try {
				const emrUrl =
					await this.s3Service.getViewPreSignedUrl(invoiceFileKey);
				const pdfBuffer = await this.downloadPDF(emrUrl);

				return {
					buffers: [pdfBuffer],
					fileNames: ['medical-record-invoice.pdf']
				};
			} catch (error) {
				this.logger.error('Error downloading invoice PDF', {
					error,
					fileKey: invoiceFileKey
				});
				return { buffers: [], fileNames: [] };
			}
		} catch (error) {
			this.logger.error('Error collecting invoice documents', {
				error,
				appointmentId
			});
			return { buffers: [], fileNames: [] };
		}
	}

	async collectDiagnosticDocuments(appointmentId: string) {
		try {
			const emr = await this.emrRepository.findOne({
				where: { appointmentId }
			});

			if (!emr) {
				return { buffers: [], fileNames: [] };
			}

			// Same approach as getDiagnosticDocumentFiles
			const diagnosticFiles =
				await this.findDiagnosticFiles(appointmentId);
			if (!diagnosticFiles || diagnosticFiles.length === 0) {
				return { buffers: [], fileNames: [] };
			}

			const buffers: Buffer[] = [];
			const fileNames: string[] = [];

			for (const file of diagnosticFiles) {
				try {
					const emrUrl = await this.s3Service.getViewPreSignedUrl(
						file.fileKey
					);
					if (typeof emrUrl === 'string') {
						const pdfBuffer = await this.downloadPDF(emrUrl);

						buffers.push(pdfBuffer);
						fileNames.push(
							`Diagnostic_${file.name || 'report'}.pdf`
						);
					}
				} catch (error) {
					this.logger.error('Error downloading diagnostic PDF', {
						error,
						fileKey: file.fileKey
					});
				}
			}

			return { buffers, fileNames };
		} catch (error) {
			this.logger.error('Error collecting diagnostic documents', {
				error,
				appointmentId
			});
			return { buffers: [], fileNames: [] };
		}
	}

	async collectLabReportDocuments(appointmentId: string) {
		try {
			const labReport = await this.labReportRepository.findOne({
				where: { id: appointmentId }
			});

			if (!labReport) {
				return { buffers: [], fileNames: [] };
			}

			// LabReport has files array, not a fileKey
			if (
				!labReport.files ||
				!Array.isArray(labReport.files) ||
				labReport.files.length === 0
			) {
				return { buffers: [], fileNames: [] };
			}

			const buffers: Buffer[] = [];
			const fileNames: string[] = [];

			for (const file of labReport.files) {
				if (file && typeof file === 'object' && 'fileKey' in file) {
					try {
						const fileKey = String(file.fileKey);
						const emrUrl =
							await this.s3Service.getViewPreSignedUrl(fileKey);
						if (typeof emrUrl === 'string') {
							const pdfBuffer = await this.downloadPDF(emrUrl);

							buffers.push(pdfBuffer);
							const fileName = file.fileName || 'lab-report.pdf';
							fileNames.push(fileName);
						}
					} catch (error) {
						this.logger.error('Error downloading lab report PDF', {
							error,
							fileKey: file.fileKey
						});
					}
				}
			}

			return { buffers, fileNames };
		} catch (error) {
			this.logger.error('Error collecting lab report document', {
				error,
				appointmentId
			});
			return { buffers: [], fileNames: [] };
		}
	}

	async collectDiagnosticNotesDocuments(fileKeys: string[]) {
		if (!fileKeys || fileKeys.length === 0) {
			return { buffers: [], fileNames: [] };
		}

		const buffers: Buffer[] = [];
		const fileNames: string[] = [];

		for (const fileKey of fileKeys) {
			try {
				const pdfUrl =
					await this.s3Service.getViewPreSignedUrl(fileKey);
				const pdfBuffer = await this.downloadPDF(pdfUrl);

				// Extract filename from fileKey
				const fileName =
					fileKey.split('/').pop() ||
					`DiagnosticNote_${Date.now()}.pdf`;

				buffers.push(pdfBuffer);
				fileNames.push(fileName);
			} catch (error) {
				this.logger.error('Error downloading diagnostic note PDF', {
					error,
					fileKey
				});
			}
		}

		return { buffers, fileNames };
	}

	async collectVaccinationDocuments(appointmentId: string) {
		try {
			const patientVaccinations =
				await this.patientVaccinationRepository.find({
					where: { appointmentId }
				});

			if (!patientVaccinations || patientVaccinations.length === 0) {
				return { buffers: [], fileNames: [] };
			}

			const buffers: Buffer[] = [];
			const fileNames: string[] = [];

			for (const vaccination of patientVaccinations) {
				// PatientVaccination has reportUrl, not fileKey
				if (vaccination.reportUrl) {
					try {
						const pdfUrl = await this.s3Service.getViewPreSignedUrl(
							vaccination.reportUrl
						);
						if (typeof pdfUrl === 'string') {
							const pdfBuffer = await this.downloadPDF(pdfUrl);

							buffers.push(pdfBuffer);
							fileNames.push(
								`Vaccination_${vaccination.vaccineName || 'record'}.pdf`
							);
						}
					} catch (error) {
						this.logger.error('Error downloading vaccination PDF', {
							error,
							fileKey: vaccination.reportUrl
						});
					}
				}
			}

			return { buffers, fileNames };
		} catch (error) {
			this.logger.error('Error collecting vaccination documents', {
				error,
				appointmentId
			});
			return { buffers: [], fileNames: [] };
		}
	}

	// Add a helper method to find diagnostic files
	private async findDiagnosticFiles(appointmentId: string) {
		// This is a placeholder - we need to determine where diagnostic files are stored
		// It might be in a different repository or a field in the appointment details
		try {
			// Using appointmentDetailsEntity which is available in the constructor
			const appointmentDetails =
				await this.appointmentDetailsEntity.findOne({
					where: { appointmentId },
					relations: ['appointment']
				});

			if (!appointmentDetails || !appointmentDetails.details) {
				return [];
			}

			// Check if diagnostic files are stored in appointment details
			const details = appointmentDetails.details as any;
			if (details.objective?.labReports) {
				const files = [];
				for (const report of details.objective.labReports) {
					if (report.files && Array.isArray(report.files)) {
						files.push(...report.files);
					}
				}
				return files;
			}

			return [];
		} catch (error) {
			this.logger.error('Error finding diagnostic files', {
				error,
				appointmentId
			});
			return [];
		}
	}

	async makeTemplateAndSendMailForLedgerDocuments({
		clientName,
		clinicContact,
		brandName,
		clientEmail,
		subject,
		pdfBuffer,
		fileNames
	}: {
		clientName: string;
		clinicContact: string;
		brandName: string;
		clientEmail: string;
		subject: string;
		pdfBuffer: Array<Buffer>;
		fileNames: Array<string>;
	}) {
		this.sendMail(
			`Dear ${clientName},
		<br/><br/>
		We've attached the selected inovices. <br/><br/>
		If you have any questions, feel free to reach out to us at ${clinicContact}. We look forward to seeing you and ensuring you receive the best possible care!

		<br/>
		<br/>
		Warm regards,<br/>
		The ${brandName} Team`,
			pdfBuffer,
			fileNames,
			clientEmail ?? '<EMAIL>',
			subject
		);
	}

	// Method to get or generate an individual payment receipt PDF
	async getPaymentReceiptPdfBuffer(
		referenceAlphaId: string
	): Promise<Buffer | null> {
		this.logger.log('Starting getPaymentReceiptPdfBuffer', {
			referenceAlphaId
		});
		const paymentDetailsArray = await this.paymentDetailsRepository.find({
			where: { referenceAlphaId },
			relations: [
				'ownerBrand',
				'patient',
				'clinic',
				'clinic.brand',
				'invoice'
			],
			order: { createdAt: 'ASC' }
		});

		if (!paymentDetailsArray || paymentDetailsArray.length === 0) {
			this.logger.error('Payment details not found for receipt', {
				referenceAlphaId
			});
			throw new NotFoundException(
				`Payment details with ID ${referenceAlphaId} not found`
			);
		}

		const firstPayment = paymentDetailsArray[0];
		const receiptDetail = firstPayment.receiptDetail as
			| Record<string, string>
			| undefined;

		// Check for an existing permanent file key
		if (
			receiptDetail?.fileKey &&
			receiptDetail.fileKey.startsWith('receipt/')
		) {
			try {
				this.logger.log('Found existing permanent receipt file', {
					fileKey: receiptDetail.fileKey
				});
				const viewSignedUrl = await this.s3Service.getViewPreSignedUrl(
					receiptDetail.fileKey
				);
				return await this.downloadPDF(viewSignedUrl);
			} catch (error) {
				this.logger.error(
					'Error downloading existing permanent receipt PDF, will regenerate',
					{
						referenceAlphaId,
						fileKey: receiptDetail.fileKey,
						error
					}
				);
			}
		}

		// Generate new PDF if no permanent file or download failed
		this.logger.log('Generating new payment receipt PDF', {
			referenceAlphaId
		});

		const clinicDetails = firstPayment.clinic as ClinicEntity | undefined;
		const ownerDetails = firstPayment.ownerBrand as OwnerBrand | undefined;

		if (!clinicDetails) {
			this.logger.error('Clinic details not found for payment receipt', {
				referenceAlphaId
			});
			throw new NotFoundException(
				`Clinic details not found for payment ${referenceAlphaId}`
			);
		}

		let clinicLogoUrl =
			clinicDetails?.logoUrl || (clinicDetails as any)?.clinicLogo || '';
		if (clinicLogoUrl && clinicLogoUrl.startsWith('clinicLogo/')) {
			try {
				clinicLogoUrl =
					await this.s3Service.getViewPreSignedUrl(clinicLogoUrl);
			} catch (error) {
				this.logger.error(
					'Error generating pre-signed URL for clinic logo',
					error
				);
			}
		}

		const totalAmount = paymentDetailsArray.reduce((sum, payment) => {
			const cashAmount = Number(payment.amount || 0);
			const creditAmount = Number(payment.creditAmountUsed || 0);
			return sum + cashAmount + creditAmount;
		}, 0);

		const isRefund =
			firstPayment.type === EnumAmountType.Return ||
			firstPayment.type === EnumAmountType.CreditNote ||
			(firstPayment.type === EnumAmountType.Collect &&
				!!firstPayment.invoiceId);

		let pdfHtml = '';
		const receiptDateFormatted = moment(firstPayment.createdAt).format(
			'MMMM D, YYYY'
		);

		if (isRefund) {
			const refundReceiptData: RefundReceiptData = {
				receiptNumber: referenceAlphaId,
				receiptDate: receiptDateFormatted,
				clinicName: clinicDetails.name || '',
				clinicAddress: this.getClinicAddress(clinicDetails as any), // Cast to any if ClinicEntity type is strict
				clinicPhone: clinicDetails.phoneNumbers?.[0]?.number || '',
				clinicEmail: clinicDetails.email || '',
				clinicWebsite: clinicDetails.website || '',
				customerName: ownerDetails
					? `${ownerDetails.firstName || ''} ${ownerDetails.lastName || ''}`
					: 'N/A',
				amount: totalAmount,
				paymentType: firstPayment.paymentType || 'Cash',
				clinicLogoUrl,
				creditsAdded: Number(
					paymentDetailsArray.find(p => p.isCreditsAdded)
						?.creditAmountAdded || 0
				),
				ReferenceCreditNote:
					firstPayment.invoice?.referenceAlphaId || ''
			};
			pdfHtml = generateRefundReceipt(refundReceiptData); // ASSUMED available
		} else {
			const outstandingInvoicesPaid = paymentDetailsArray
				.filter(
					p =>
						p.invoice &&
						p.type !== EnumAmountType.Collect &&
						p.invoice.referenceAlphaId
				)
				.map(p => ({
					referenceInvoice: p.invoice!.referenceAlphaId!,
					invoiceAmountCleared:
						p.paymentType === EnumPaymentType.Credits
							? Number(p.creditAmountUsed || 0)
							: Number(p.amount || 0),
					pet: p.patient?.patientName || ''
				}));

			const paymentReceiptData: PaymentReceiptData = {
				receiptNumber: referenceAlphaId,
				receiptDate: receiptDateFormatted,
				clinicName: clinicDetails.name || '',
				clinicAddress: this.getClinicAddress(clinicDetails as any), // Cast to any if ClinicEntity type is strict
				clinicPhone: clinicDetails.phoneNumbers?.[0]?.number || '',
				clinicEmail: clinicDetails.email || '',
				clinicWebsite: clinicDetails.website || '',
				customerName: ownerDetails
					? `${ownerDetails.firstName || ''} ${ownerDetails.lastName || ''}`
					: 'N/A',
				amount: totalAmount,
				paymentType: firstPayment.paymentType || 'Cash',
				clinicLogoUrl,
				creditsAdded: Number(
					paymentDetailsArray.find(
						p =>
							p.type === EnumAmountType.Collect &&
							p.isCreditsAdded
					)?.creditAmountAdded || 0
				),
				outstandingInvoicesPaid
			};
			pdfHtml = generatePaymentReceipt(paymentReceiptData); // ASSUMED available
		}

		const pdfBuffer = await generatePDF(pdfHtml);

		// Store with a permanent key
		const permanentFileKey = `receipt/${uuidv4()}.pdf`;
		const petName =
			firstPayment.patient?.patientName
				?.replace(/[^a-zA-Z0-9]/g, '_')
				.toLowerCase() || 'receipt';
		const permanentFileName = `${petName}_${referenceAlphaId}.pdf`;

		await this.s3Service.uploadPdfToS3(pdfBuffer, permanentFileKey);
		this.logger.log('Uploaded new receipt PDF to S3 with permanent key', {
			permanentFileKey
		});

		const newReceiptDetail = {
			fileKey: permanentFileKey,
			fileName: permanentFileName,
			fileType: 'PDF',
			isGenerating: false
		};

		// Update all payment details with this referenceAlphaId to store the permanent fileKey
		for (const payment of paymentDetailsArray) {
			await this.paymentDetailsRepository.update(payment.id, {
				receiptDetail: newReceiptDetail
			});
		}
		this.logger.log(
			'Updated payment details with new permanent receipt fileKey',
			{ referenceAlphaId, count: paymentDetailsArray.length }
		);

		return pdfBuffer;
	}

	async storePaymentReceipts(
		paymentReferenceIds: string[],
		requestId: string
	): Promise<{ fileKey: string; buffer: Buffer } | null> {
		this.logger.log('Starting storePaymentReceipts', {
			paymentReferenceIds,
			requestId
		});
		const sortedIds = [...paymentReferenceIds].sort();
		const hash = crypto
			.createHash('md5')
			.update(sortedIds.join(','))
			.digest('hex');

		const firstPayment = await this.paymentDetailsRepository.findOne({
			where: { referenceAlphaId: sortedIds[0] }
		});
		if (!firstPayment) {
			this.logger.error(
				'First payment detail not found for storing receipts',
				{ referenceAlphaId: sortedIds[0] }
			);
			throw new NotFoundException('First payment detail not found');
		}
		const ownerId = firstPayment.ownerId;

		const expirationDate = new Date();
		expirationDate.setHours(expirationDate.getHours() + 24);

		let documentRecord =
			await this.mergedPaymentReceiptDocumentRepository.findOne({
				where: { requestId }
			});

		if (!documentRecord) {
			documentRecord =
				await this.mergedPaymentReceiptDocumentRepository.save({
					ownerId,
					requestId,
					paymentReferenceIdsHash: hash,
					paymentReferenceIds: sortedIds,
					isGenerating: true,
					status: MergedDocumentStatus.PROCESSING,
					expiresAt: expirationDate
				});
		} else {
			await this.mergedPaymentReceiptDocumentRepository.update(
				documentRecord.id,
				{
					isGenerating: true,
					status: MergedDocumentStatus.PROCESSING,
					paymentReferenceIdsHash: hash,
					paymentReferenceIds: sortedIds,
					expiresAt: expirationDate // Reset expiration
				}
			);
		}

		const pdfBuffers: Buffer[] = [];
		try {
			for (const refId of sortedIds) {
				const buffer = await this.getPaymentReceiptPdfBuffer(refId);
				if (buffer) {
					pdfBuffers.push(buffer);
				} else {
					this.logger.warn(
						'Could not get PDF buffer for payment receipt',
						{ refId }
					);
				}
			}

			if (pdfBuffers.length === 0) {
				this.logger.error(
					'No valid payment receipt PDFs found for merging',
					{ requestId }
				);
				await this.mergedPaymentReceiptDocumentRepository.update(
					documentRecord.id,
					{
						isGenerating: false,
						status: MergedDocumentStatus.ERROR,
						errorMessage: 'No valid PDFs found'
					}
				);
				return null;
			}

			const mergedPdfDoc = await PDFDocument.create();
			for (const pdfBytes of pdfBuffers) {
				const individualPdf = await PDFDocument.load(
					new Uint8Array(pdfBytes)
				);
				const copiedPages = await mergedPdfDoc.copyPages(
					individualPdf,
					individualPdf.getPageIndices()
				);
				copiedPages.forEach(page => mergedPdfDoc.addPage(page));
			}
			const mergedPdfBuffer = Buffer.from(await mergedPdfDoc.save());

			const fileKey = `payment_receipts_merged/${requestId}.pdf`;
			await this.s3Service.uploadBuffer(
				mergedPdfBuffer,
				fileKey,
				'application/pdf'
			);

			await this.mergedPaymentReceiptDocumentRepository.update(
				documentRecord.id,
				{
					fileKey,
					isGenerating: false,
					status: MergedDocumentStatus.COMPLETED,
					updatedAt: new Date(),
					errorMessage: null
				}
			);
			this.logger.log('Successfully stored merged payment receipts', {
				requestId,
				fileKey
			});
			this.cleanupExpiredMergedPaymentReceiptDocuments(); // Schedule cleanup
			return { fileKey: fileKey, buffer: mergedPdfBuffer };
		} catch (error: any) {
			this.logger.error('Error storing merged payment receipts', {
				requestId,
				error
			});
			await this.mergedPaymentReceiptDocumentRepository.update(
				documentRecord.id,
				{
					isGenerating: false,
					status: MergedDocumentStatus.ERROR,
					errorMessage: error.message
				}
			);
			return null;
		}
	}

	async sharePaymentReceipts(
		paymentReferenceIds: string[],
		shareMode: string[],
		type: 'client' | 'other',
		email?: string,
		phoneNumber?: string
	): Promise<void> {
		const requestId = uuidv4();
		this.logger.log('Starting sharePaymentReceipts', {
			paymentReferenceIds,
			shareMode,
			type,
			email,
			phoneNumber,
			requestId
		});

		if (!paymentReferenceIds || paymentReferenceIds.length === 0) {
			this.logger.error(
				'No payment reference IDs provided for sharing receipts'
			);
			throw new HttpException(
				'No payment reference IDs provided',
				HttpStatus.BAD_REQUEST
			);
		}

		const storeResult = await this.storePaymentReceipts(
			paymentReferenceIds,
			requestId
		);

		if (!storeResult || !storeResult.fileKey) {
			this.logger.error(
				'Failed to get merged payment receipts fileKey and buffer for sharing',
				{ requestId }
			);
			throw new HttpException(
				'Failed to prepare payment receipts for sharing',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}

		const mergedFileKey = storeResult.fileKey;
		const mergedPdfBufferForEmail = storeResult.buffer;

		// Fetch details from the first payment for patient/clinic info
		const firstPayment = await this.paymentDetailsRepository.findOne({
			where: { referenceAlphaId: paymentReferenceIds[0] },
			relations: [
				'patient',
				'patient.patientOwners',
				'patient.patientOwners.ownerBrand',
				'patient.patientOwners.ownerBrand.globalOwner',
				'clinic',
				'clinic.brand'
			]
		});

		if (!firstPayment) {
			this.logger.error(
				'Could not find first payment details for sharing context',
				{ referenceAlphaId: paymentReferenceIds[0] }
			);
			throw new NotFoundException(
				'Payment details not found to determine sharing context.'
			);
		}

		const patientDetails = firstPayment.patient as Patient | undefined;
		const clinicDetails = firstPayment.clinic as ClinicEntity | undefined;
		const brandName =
			clinicDetails?.brand?.name || clinicDetails?.name || 'Your Clinic';
		const clinicContact = clinicDetails?.phoneNumbers?.[0]
			? `+${clinicDetails.phoneNumbers[0].country_code || ''} ${clinicDetails.phoneNumbers[0].number || ''}`.trim()
			: '';
		const patientName = patientDetails?.patientName || 'your pet';

		const fileName = `Payment_Receipts_${moment().format('DDMMYYYY')}.pdf`;
		const emailSubject = `Payment Receipts from ${brandName}`;

		if (type === 'other') {
			this.logger.log("Sharing payment receipts with 'other' recipient", {
				email,
				phoneNumber
			});
			if (shareMode.includes('email') && email) {
				const emailBody = `Dear Recipient,<br/><br/>Please find attached the payment receipts.<br/><br/>If you have any questions, feel free to reach out to us at ${clinicContact}.<br/><br/>Warm regards,<br/>The ${brandName} Team`;
				await this.sendMail(
					emailBody,
					[mergedPdfBufferForEmail],
					[fileName],
					email,
					emailSubject
				);
				this.logger.log(
					'Payment receipts sent via email to other recipient',
					{ email }
				);
			}
			if (shareMode.includes('whatsapp') && phoneNumber) {
				setTimeout(async () => {
					try {
						const viewUrl =
							await this.s3Service.getViewPreSignedUrl(
								mergedFileKey
							);
						const templateData = getReceiptGenerationTemplateData({
							clientName: 'Recipient',
							receiptDate: moment().format('MMMM D, YYYY'), // General date for merged doc
							brandName,
							receiptFile: viewUrl,
							mobileNumber: phoneNumber
						});
						await this.whatsappService.sendTemplateMessage(
							templateData
						);
						this.logger.log(
							'Payment receipts link sent via WhatsApp to other recipient',
							{ phoneNumber }
						);
					} catch (error) {
						this.logger.error(
							'Error sending WhatsApp to other recipient',
							{ phoneNumber, error }
						);
					}
				}, 3000);
			}
		} else {
			// Client type sharing
			if (
				!patientDetails?.patientOwners ||
				patientDetails.patientOwners.length === 0
			) {
				this.logger.warn(
					'No patient owners found for sharing payment receipts',
					{ patientId: patientDetails?.id }
				);
				return;
			}
			for (const owner of patientDetails.patientOwners) {
				const ownerBrand = owner.ownerBrand as OwnerBrand | undefined;
				if (!ownerBrand) continue;

				const clientName =
					`${ownerBrand.firstName || ''} ${ownerBrand.lastName || ''}`.trim() ||
					'Recipient';
				const ownerEmail = ownerBrand.email;
				const ownerPhone = ownerBrand.globalOwner?.phoneNumber;
				const ownerCountryCode = ownerBrand.globalOwner?.countryCode;

				if (shareMode.includes('email') && ownerEmail) {
					const emailBody = `Dear ${clientName},<br/><br/>Please find attached the payment receipts for ${patientName}.<br/><br/>If you have any questions, feel free to reach out to us at ${clinicContact}.<br/><br/>Warm regards,<br/>The ${brandName} Team`;
					await this.sendMail(
						emailBody,
						[mergedPdfBufferForEmail],
						[fileName],
						ownerEmail,
						emailSubject
					);
					this.logger.log(
						'Payment receipts sent via email to owner',
						{ email: ownerEmail }
					);
				}
				if (
					shareMode.includes('whatsapp') &&
					ownerPhone &&
					ownerCountryCode
				) {
					setTimeout(async () => {
						try {
							const viewUrl =
								await this.s3Service.getViewPreSignedUrl(
									mergedFileKey
								);

							const templateData =
								getReceiptGenerationTemplateData({
									clientName,
									receiptDate:
										moment().format('MMMM D, YYYY'), // General date
									brandName,
									receiptFile: viewUrl,
									mobileNumber: `${ownerCountryCode}${ownerPhone}`
								});
							await this.whatsappService.sendTemplateMessage(
								templateData
							);
							this.logger.log(
								'Payment receipts link sent via WhatsApp to owner',
								{
									phoneNumber: `${ownerCountryCode}${ownerPhone}`
								}
							);
						} catch (error) {
							this.logger.error(
								'Error sending WhatsApp to owner',
								{
									phoneNumber: `${ownerCountryCode}${ownerPhone}`,
									error
								}
							);
						}
					}, 3000);
				}
			}
		}
	}

	// Cleanup method for expired merged payment receipt documents
	private async cleanupExpiredMergedPaymentReceiptDocuments() {
		try {
			const now = new Date();
			const expiredDocuments =
				await this.mergedPaymentReceiptDocumentRepository.find({
					where: {
						expiresAt: LessThan(now),
						status: MergedDocumentStatus.COMPLETED // Only cleanup completed ones that are expired
					},
					take: 100
				});

			if (expiredDocuments.length === 0) {
				return;
			}

			this.logger.log(
				`Found ${expiredDocuments.length} expired merged payment receipt documents to clean up`
			);

			for (const doc of expiredDocuments) {
				if (doc.fileKey) {
					try {
						await this.s3Service.deleteFile(doc.fileKey);
						this.logger.log(
							'Deleted expired merged payment receipt S3 file',
							{ fileKey: doc.fileKey }
						);
					} catch (err) {
						this.logger.error(
							'Error deleting expired merged payment receipt S3 file',
							{ fileKey: doc.fileKey, error: err }
						);
					}
				}
				await this.mergedPaymentReceiptDocumentRepository.delete(
					doc.id
				);
				this.logger.log(
					'Deleted expired merged payment receipt document record',
					{ id: doc.id }
				);
			}
		} catch (error) {
			this.logger.error(
				'Error cleaning up expired merged payment receipt documents',
				{ error }
			);
		}
	}

	async shareStatements(
		generatedFileKeys: Record<string, string>, // e.g., { "invoice": "s3key1", "refund": "s3key2" }
		ownerDetails: StatementOwnerDetails,
		shareDetails: StatementShareDetails,
		userContext: StatementUserContext
	) {
		this.logger.log('Starting shareStatements', {
			generatedFileKeys,
			ownerDetails,
			shareDetails,
			userContext
		});

		const pdfBuffers: { buffer: Buffer; fileName: string }[] = [];
		const s3UrlsForWhatsapp: { url: string; fileName: string }[] = [];

		for (const statementType in generatedFileKeys) {
			const fileKey = generatedFileKeys[statementType];
			if (fileKey) {
				try {
					await new Promise(resolve => setTimeout(resolve, 3000));
					const s3Url =
						await this.s3Service.getViewPreSignedUrl(fileKey);
					const pdfBuffer = await this.downloadPDF(s3Url);
					const descriptiveFileName = `${statementType.charAt(0).toUpperCase() + statementType.slice(1)} Statement.pdf`;
					pdfBuffers.push({
						buffer: pdfBuffer,
						fileName: descriptiveFileName
					});
					s3UrlsForWhatsapp.push({
						url: s3Url,
						fileName: descriptiveFileName
					});
					this.logger.log('Successfully processed statement for S3', {
						statementType,
						fileKey
					});
				} catch (error) {
					this.logger.error(
						`Failed to download PDF from S3 for ${statementType}`,
						{ fileKey, error }
					);
					// Continue to process other files if one fails
				}
			}
		}

		if (pdfBuffers.length === 0 && s3UrlsForWhatsapp.length === 0) {
			this.logger.warn(
				'No statement PDFs could be retrieved for sharing.'
			);
			// Optionally throw an error or return if no documents to share
			// For now, just return to avoid breaking the flow if some PDFs failed but others might succeed.
			// Or, throw an error if ALL fail. Let's assume partial success is acceptable.
			if (Object.keys(generatedFileKeys).length > 0) {
				// If files were expected
				// Let the caller know no documents were processed if files were expected.
				// If generatedFileKeys was empty, then it's not an error.
				// Consider throwing an error to indicate failure to the end-user or SQS task.
				// For now, just logging and returning.
				throw new HttpException(
					'Failed to retrieve any statement documents for sharing.',
					HttpStatus.INTERNAL_SERVER_ERROR
				);
			}
			return;
		}

		const targetEmail =
			shareDetails.recipient === 'other' && shareDetails.email
				? shareDetails.email
				: ownerDetails.email;
		let targetPhoneNumber =
			shareDetails.recipient === 'other' && shareDetails.phoneNumber
				? shareDetails.phoneNumber
				: ownerDetails.phoneNumber;
		const targetCountryCode =
			shareDetails.recipient === 'other' && shareDetails.countryCode
				? shareDetails.countryCode
				: ownerDetails.countryCode;

		if (
			targetPhoneNumber &&
			targetCountryCode &&
			!targetPhoneNumber.startsWith(targetCountryCode) &&
			!targetPhoneNumber.startsWith('+' + targetCountryCode)
		) {
			targetPhoneNumber = `${targetCountryCode}${targetPhoneNumber}`;
		}

		const clientName =
			shareDetails.recipient === 'other'
				? 'Recipient'
				: ownerDetails.name;

		// Email Sharing
		if (
			targetEmail &&
			(shareDetails.shareMethod === 'email' ||
				shareDetails.shareMethod === 'both')
		) {
			if (pdfBuffers.length > 0) {
				const emailSubject = `Your Financial Statements from ${userContext.brandName}`;
				const emailBody = `
Dear ${clientName},<br/><br/>
Please find your requested financial statement(s) attached.<br/><br/>
If you have any questions, please contact us at ${userContext.clinicContact || ''}.<br/><br/>
Warm regards,<br/>
The ${userContext.brandName} Team
`;
				try {
					await this.sendMail(
						emailBody,
						pdfBuffers.map(item => item.buffer),
						pdfBuffers.map(item => item.fileName),
						targetEmail,
						emailSubject
					);
					this.logger.log('Financial statements sent via email', {
						recipientEmail: targetEmail,
						count: pdfBuffers.length
					});
				} catch (error) {
					this.logger.error(
						'Failed to send email with financial statements',
						{ recipientEmail: targetEmail, error }
					);
					// Decide if this should throw an error or just be logged
				}
			} else {
				this.logger.warn(
					'Email sharing requested, but no PDF buffers available.',
					{ targetEmail }
				);
			}
		}

		// WhatsApp Sharing
		if (
			targetPhoneNumber &&
			(shareDetails.shareMethod === 'whatsapp' ||
				shareDetails.shareMethod === 'both')
		) {
			if (s3UrlsForWhatsapp.length > 0) {
				for (const doc of s3UrlsForWhatsapp) {
					// Using getLedgerDocumentTemplateData as a generic template to send a document link
					// Ensure the template name and parameters match what's expected by your WhatsApp service provider
					const { templateName, valuesArray, mobileNumber } =
						getLedgerDocumentTemplateData({
							brandName: userContext.brandName,
							clientName: clientName,
							clinicContact: userContext.clinicContact || '',
							EMRFile: doc.url, // The S3 URL for the PDF
							mobileNumber: targetPhoneNumber // Assumed to include country code if needed by getLedgerDocumentTemplateData
						});

					try {
						await this.whatsappService.sendTemplateMessage({
							templateName,
							valuesArray,
							mobileNumber
						});
						this.logger.log(
							`Financial statement (${doc.fileName}) link sent via WhatsApp`,
							{ recipientPhone: targetPhoneNumber }
						);
					} catch (error) {
						this.logger.error(
							`Failed to send WhatsApp message for ${doc.fileName}`,
							{ recipientPhone: targetPhoneNumber, error }
						);
						// Decide if this should throw an error or just be logged
					}
				}
			} else {
				this.logger.warn(
					'WhatsApp sharing requested, but no S3 URLs available.',
					{ targetPhoneNumber }
				);
			}
		}
	}
}

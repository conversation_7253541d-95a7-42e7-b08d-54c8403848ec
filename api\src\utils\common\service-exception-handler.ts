import {
	BadRequestException,
	ConflictException,
	InternalServerErrorException,
	NotFoundException
} from '@nestjs/common';

/**
 * Utility function to handle common service exceptions
 * Rethrows known exceptions (ConflictException, BadRequestException, NotFoundException)
 * and wraps unknown exceptions in InternalServerErrorException
 * 
 * @param error - The caught error
 * @param defaultMessage - Default error message for InternalServerErrorException
 */
export function handleServiceException(
	error: any,
	defaultMessage: string
): never {
	if (
		error instanceof ConflictException ||
		error instanceof BadRequestException ||
		error instanceof NotFoundException
	) {
		throw error;
	}
	
	throw new InternalServerErrorException(defaultMessage);
}

/**
 * Decorator function to automatically wrap service methods with exception handling
 * 
 * @param defaultMessage - Default error message for InternalServerErrorException
 */
export function HandleServiceExceptions(defaultMessage: string) {
	return function (
		target: any,
		propertyName: string,
		descriptor: PropertyDescriptor
	) {
		const method = descriptor.value;

		descriptor.value = async function (...args: any[]) {
			try {
				return await method.apply(this, args);
			} catch (error) {
				handleServiceException(error, defaultMessage);
			}
		};

		return descriptor;
	};
}

'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
    useLoginWithEmailMutation,
    useGenerateOtpForEmailMutation,
} from '../../services/signin.queries';
import Alert from '../../atoms/Alert';
import EmailSignInForm from '../../organisms/signin/EmailSignIn';
import { setAuth } from '@/app/services/identity.service';

export default function EmailSignInPage() {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isOtpSent, setIsOtpSent] = useState(false);
    const [email, setEmail] = useState('');
    const [alert, setAlert] = useState<{
        variant: 'info' | 'error' | 'success' | 'warning';
        label: string;
        isOpen: boolean;
    }>({ variant: 'info', label: '', isOpen: false });

    const [resendEnabled, setResendEnabled] = useState(false);
    const [timer, setTimer] = useState(60);

    const router = useRouter();
    const { generateOtpForEmailMutation } = useGenerateOtpForEmailMutation();
    const { mutate: generateOtp } = generateOtpForEmailMutation;
    const { loginWithEmailMutation } = useLoginWithEmailMutation();
    const { mutate: loginWithEmail } = loginWithEmailMutation;

    const handleAlertClose = () => setAlert({ ...alert, isOpen: false });

    const onSubmit = (data: { email: string; otp?: string }) => {
        setIsSubmitting(true);
        if (!data.otp) {
            setEmail(data.email); // Store email for future use
            handleOtpRequest({ email: data.email });
        } else {
            handleLogin(data);
        }
    };

    const handleOtpRequest = (data: { email: string }) => {
        generateOtp(data.email, {
            onSuccess: (response: any) => {
                console.log(response);
                if (response.statusCode === 404) {
                    setAlert({
                        variant: 'error',
                        label: 'User not found!',
                        isOpen: true,
                    });
                } else if (response.statusCode === 401) {
                    setAlert({
                        variant: 'error',
                        label: 'Only SuperAdmins can login using OTP!',
                        isOpen: true,
                    });
                } else if (response.data.statusCode === 201) {
                    setIsOtpSent(true);
                    setResendEnabled(false); // Disable Resend OTP button initially
                    setTimer(60); // Start 60-second countdown
                    setAlert({
                        variant: 'success',
                        label: 'OTP has been sent to your email!',
                        isOpen: true,
                    });
                } else {
                    setAlert({
                        variant: 'error',
                        label: 'An unexpected error occured!',
                        isOpen: true,
                    });
                }
            },
            onError: (error: any) => {
                console.log('OTP generation error:', error);
                setAlert({
                    variant: 'error',
                    label: 'Failed to generate OTP. Please try again.',
                    isOpen: true,
                });
            },
            onSettled: () => setIsSubmitting(false),
        });
    };

    const handleResendOtp = () => {
        // Resend OTP using the stored email
        setIsSubmitting(true);
        handleOtpRequest({ email });
    };

    const handleLogin = (data: { email: string; otp?: string }) => {
        const loginData = {
            email: data.email,
            otp: data.otp || '',
        };
        loginWithEmail(loginData, {
            onSuccess: (response) => {
                if (response.status === false) {
                    setAlert({
                        variant: 'error',
                        label: 'Invalid email or OTP entered!',
                        isOpen: true,
                    });
                } else {
                    const { token, roleName } = response.data;
                    setAlert({
                        variant: 'success',
                        label: 'Login successful! Redirecting to brands...',
                        isOpen: true,
                    });
                    console.log(token, roleName);
                    setAuth({
                        token,
                        role: roleName,
                        roleName: roleName, // Keep backward compatibility
                    });
                    router.push('/brands');
                }
            },
            onSettled: () => setIsSubmitting(false),
        });
    };

    useEffect(() => {
        let countdown: NodeJS.Timeout;
        if (isOtpSent && timer > 0) {
            countdown = setInterval(() => {
                setTimer((prev) => {
                    if (prev === 1) {
                        clearInterval(countdown);
                        setResendEnabled(true);
                        return 0;
                    }
                    return prev - 1;
                });
            }, 1000);
        }

        return () => clearInterval(countdown);
    }, [isOtpSent, timer]);

    return (
        <div className="h-screen flex justify-center items-center flex-col relative w-full">
            {alert.isOpen && (
                <Alert
                    variant={alert.variant}
                    label={alert.label}
                    onClose={handleAlertClose}
                    className="fixed top-2 z-50 w-2/6"
                />
            )}
            <EmailSignInForm
                onSubmit={onSubmit}
                isSubmitting={isSubmitting}
                isOtpSent={isOtpSent}
                onResendOtp={handleResendOtp}
                resendEnabled={resendEnabled}
                timer={timer}
            />
        </div>
    );
}

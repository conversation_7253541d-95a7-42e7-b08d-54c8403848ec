# SonarQube project configuration for Nidana Patient Portal (Next.js)
sonar.projectKey=nidana-patientportal-local
sonar.projectName=Nidana Patient Portal (Local)
sonar.projectVersion=1.0

# Source code configuration
sonar.sources=app,components,lib,utils
sonar.sourceEncoding=UTF-8

# Exclusions - files/directories to exclude from analysis
sonar.exclusions=**/node_modules/**,\
                 **/.next/**,\
                 **/out/**,\
                 **/build/**,\
                 **/coverage/**,\
                 **/public/**,\
                 **/env/**,\
                 **/*.spec.ts,\
                 **/*.spec.tsx,\
                 **/*.test.ts,\
                 **/*.test.tsx,\
                 **/middleware.tsx,\
                 **/next.config.mjs,\
                 **/tailwind.config.ts,\
                 **/postcss.config.mjs

# Coverage exclusions - files to exclude from coverage calculation
sonar.coverage.exclusions=**/public/**,\
                          **/env/**,\
                          **/middleware.tsx,\
                          **/next.config.mjs,\
                          **/tailwind.config.ts,\
                          **/postcss.config.mjs,\
                          **/*.spec.ts,\
                          **/*.spec.tsx,\
                          **/*.test.ts,\
                          **/*.test.tsx

# Duplication exclusions - files to exclude from duplication detection
sonar.cpd.exclusions=**/public/**,\
                     **/env/**

# JavaScript/TypeScript specific settings
# sonar.javascript.lcov.reportPaths=coverage/lcov.info
# sonar.typescript.lcov.reportPaths=coverage/lcov.info

# Test execution reports (if available)
# sonar.testExecutionReportPaths=coverage/test-reporter.xml

# SonarQube server configuration
sonar.host.url=http://localhost:9000

# Quality gate settings
sonar.qualitygate.wait=true

# Next.js specific settings
sonar.javascript.environments=browser,node

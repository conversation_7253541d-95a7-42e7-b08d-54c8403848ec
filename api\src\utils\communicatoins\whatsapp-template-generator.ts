export const whatsappTemplateTypes = {
	APPOINTMENT_CONFIRMATION: 'appointment_confirmation_v3',
	APPOINTMENT_CONFIRMATION_CLINICLINK:
		'appointment_confirmation_cliniclink_v4',
	APPOINTMENT_UPDATE: 'appointment_update_v2',
	APPOINTMENT_UPDATE_CLINICLINK: 'appointment_update_cliniclink_v3',
	APPOINTMENT_REMINDER: 'appointment_reminder_v2',
	APPOINTMENT_REMINDER_CLINICLINK: 'appointment_reminder_cliniclink_v3',
	APPOINTMENT_CANCELLATION: 'appointment_cancellation_v2',
	APPOINTMENT_CANCELLATION_CLINICLINK:
		'appointment_cancellation_cliniclink_v3',
	RECEIPT_GENERATION: 'receipt_generated',
	RECEIPT_GENERATION_CLINICLINK: 'receipt_generated_cliniclink_v2',
	CREDIT_NOTE_GENERATION: 'credit_note_generated_v1',
	CREDIT_NOTE_GENERATION_CLINICLINK: 'credit_note_generated_cliniclink_v2',
	VACCINATION_GENERATION: 'appointment_completion_vaccination_v1',
	VACCINATION_GENERATION_CLINICLINK:
		'appointment_completion_vaccination_cliniclink_v2',
	APPOINTMENT_COMPLETION_INVOICE: 'appointment_completion_invoice_v1',
	APPOINTMENT_COMPLETION_INVOICE_CLINICLINK:
		'appointment_completion_invoice_cliniclink_v2',
	APPOINTMENT_COMPLETION_PRESCRIPTION: 'prescription_v1',
	APPOINTMENT_COMPLETION_PRESCRIPTION_CLINICLINK:
		'prescription_cliniclink_v2',
	MEDICAL_RECORDS: 'medical_record_v3',
	MEDICAL_RECORDS_CLINICLINK: 'medical_record_cliniclink_v4',
	SEND_SIGNABLE_DOCUMENT: 'send_signable_document',
	SEND_NON_SIGNABLE_DOCUMENT: 'send_non_signable_document',
	SEND_NON_SIGNABLE_DOCUMENT_CLINICLINK:
		'send_non_signable_document_cliniclink_v4',
	DIAGNOSTIC_REPORT_IMAGE: 'diagnostic_report_image_v2',
	DIAGNOSTIC_REPORT_PDF: 'diagnostic_report_pdf_v4',
	VACCINATION_IMAGE: 'vaccination_image_v2',
	VACCINATION_IMAGE_CLINICLINK: 'vaccination_image_cliniclink_v5',
	MEDICAL_RECORD_PDF: 'medical_record_pdf',
	MEDICAL_RECORD_IMAGE: 'medical_record_image_v2',
	SUPPORTING_DOCUMENT_PDF: 'supporting_document_pdf',
	SUPPORTING_DOCUMENT_IMAGE: 'supporting_document_image',
	REMINDER_NOTIFICATION: 'reminder_notification',
	REMINDER_NOTIFICATION_CLINICLINK: 'reminder_notification_cliniclink_v3',
	TREATMENT_ESTIMATE_DOCUMENT: 'treatment_estimate_document',
	TREATMENT_ESTIMATE_DOCUMENT_CLINICLINK:
		'treatment_estimate_document_cliniclink_v4',
	TREATMENT_ESTIMATE_URL: 'treatment_estimate_url_v2',
	LEDGER_DOCUMENT: 'ledger_document_v1',
	LEDGER_DOCUMENT_CLINICLINK: 'ledger_document_cliniclink_v3',
	PET_TRANSFER_OLD_OWNER: 'transfer_oldowner_v1',
	PET_TRANSFER_NEW_OWNER: 'transfer_newowner',
	PET_TRANSFER_NEW_OWNER_CLINICLINK: 'transfer_newowner_cliniclink_v3'
};

export const getAppointmentCreatationTemplateData = ({
	clientName,
	appointmentTime,
	appointmentDate,
	brandName,
	contactInformation,
	mobileNumber,
	clinicAddress
}: {
	clientName: string;
	appointmentTime: string;
	appointmentDate: string;
	brandName: string;
	contactInformation: string;
	mobileNumber: string;
	clinicAddress: string;
}) => ({
	templateName: whatsappTemplateTypes.APPOINTMENT_CONFIRMATION,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'appointment_date',
			value: appointmentDate
		},
		{
			name: 'appointment_time',
			value: appointmentTime
		},
		{
			name: 'contact_information',
			value: contactInformation
		},
		{
			name: 'clinic_address',
			value: clinicAddress
		}
	]
});

export const getAppointmentCreatationClinicLinkTemplateData = ({
	clientName,
	appointmentTime,
	appointmentDate,
	brandName,
	contactInformation,
	mobileNumber,
	clinicAddress,
	client_booking_URL
}: {
	clientName: string;
	appointmentTime: string;
	appointmentDate: string;
	brandName: string;
	contactInformation: string;
	mobileNumber: string;
	clinicAddress: string;
	client_booking_URL: string; // Note: For WhatsApp, this contains only the brand slug
}) => ({
	templateName: whatsappTemplateTypes.APPOINTMENT_CONFIRMATION_CLINICLINK,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'appointment_date',
			value: appointmentDate
		},
		{
			name: 'appointment_time',
			value: appointmentTime
		},
		{
			name: 'contact_information',
			value: contactInformation
		},
		{
			name: 'clinic_address',
			value: clinicAddress
		},
		{
			name: 'client_booking_URL',
			value: client_booking_URL // WhatsApp template receives brand slug, nginx handles URL construction
		}
	]
});

export const getAppointmentUpdateTemplateData = ({
	clientName,
	appointmentTime,
	appointmentDate,
	brandName,
	contactInformation,
	mobileNumber,
	petName
}: {
	clientName: string;
	appointmentTime: string;
	appointmentDate: string;
	brandName: string;
	contactInformation: string;
	mobileNumber: string;
	petName: string;
}) => ({
	templateName: whatsappTemplateTypes.APPOINTMENT_UPDATE,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'updated_appointment_date',
			value: appointmentDate
		},
		{
			name: 'updated_appointment_time',
			value: appointmentTime
		},
		{
			name: 'contact_information',
			value: contactInformation
		},
		{
			name: 'pet_name',
			value: petName
		}
	]
});

export const getAppointmentUpdateClinicLinkTemplateData = ({
	clientName,
	appointmentTime,
	appointmentDate,
	brandName,
	contactInformation,
	mobileNumber,
	petName,
	client_booking_URL
}: {
	clientName: string;
	appointmentTime: string;
	appointmentDate: string;
	brandName: string;
	contactInformation: string;
	mobileNumber: string;
	petName: string;
	client_booking_URL: string;
}) => ({
	templateName: whatsappTemplateTypes.APPOINTMENT_UPDATE_CLINICLINK,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'updated_appointment_date',
			value: appointmentDate
		},
		{
			name: 'updated_appointment_time',
			value: appointmentTime
		},
		{
			name: 'contact_information',
			value: contactInformation
		},
		{
			name: 'pet_name',
			value: petName
		},
		{
			name: 'client_booking_URL',
			value: client_booking_URL
		}
	]
});

export const getAppointmentReminderTemplateData = ({
	clientName,
	appointmentTime,
	appointmentDate,
	brandName,
	contactInformation,
	mobileNumber,
	petName
}: {
	clientName: string;
	appointmentTime: string;
	appointmentDate: string;
	brandName: string;
	contactInformation: string;
	mobileNumber: string;
	petName: string;
}) => ({
	templateName: whatsappTemplateTypes.APPOINTMENT_REMINDER,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'appointment_date',
			value: appointmentDate
		},
		{
			name: 'appointment_time',
			value: appointmentTime
		},
		{
			name: 'contact_information',
			value: contactInformation
		},
		{
			name: 'pet_name',
			value: petName
		}
	]
});

export const getAppointmentReminderClinicLinkTemplateData = ({
	clientName,
	appointmentTime,
	appointmentDate,
	brandName,
	contactInformation,
	mobileNumber,
	petName,
	client_booking_URL
}: {
	clientName: string;
	appointmentTime: string;
	appointmentDate: string;
	brandName: string;
	contactInformation: string;
	mobileNumber: string;
	petName: string;
	client_booking_URL: string;
}) => ({
	templateName: whatsappTemplateTypes.APPOINTMENT_REMINDER_CLINICLINK,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'appointment_date',
			value: appointmentDate
		},
		{
			name: 'appointment_time',
			value: appointmentTime
		},
		{
			name: 'contact_information',
			value: contactInformation
		},
		{
			name: 'pet_name',
			value: petName
		},
		{
			name: 'client_booking_URL',
			value: client_booking_URL
		}
	]
});

export const getAppointmentCancellationTemplateData = ({
	clientName,
	appointmentTime,
	appointmentDate,
	brandName,
	contactInformation,
	mobileNumber
}: {
	clientName: string;
	appointmentTime: string;
	appointmentDate: string;
	brandName: string;
	contactInformation: string;
	mobileNumber: string;
}) => ({
	templateName: whatsappTemplateTypes.APPOINTMENT_CANCELLATION,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'appointment_date',
			value: appointmentDate
		},
		{
			name: 'appointment_time',
			value: appointmentTime
		},
		{
			name: 'contact_information',
			value: contactInformation
		}
	]
});

export const getAppointmentCancellationClinicLinkTemplateData = ({
	clientName,
	appointmentTime,
	appointmentDate,
	brandName,
	contactInformation,
	mobileNumber,
	client_booking_URL
}: {
	clientName: string;
	appointmentTime: string;
	appointmentDate: string;
	brandName: string;
	contactInformation: string;
	mobileNumber: string;
	client_booking_URL: string;
}) => ({
	templateName: whatsappTemplateTypes.APPOINTMENT_CANCELLATION_CLINICLINK,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'appointment_date',
			value: appointmentDate
		},
		{
			name: 'appointment_time',
			value: appointmentTime
		},
		{
			name: 'contact_information',
			value: contactInformation
		},
		{
			name: 'client_booking_URL',
			value: client_booking_URL
		}
	]
});

export const getReceiptGenerationTemplateData = ({
	clientName,
	receiptDate,
	brandName,
	receiptFile,
	mobileNumber
}: {
	clientName: string;
	receiptDate: string;
	brandName: string;
	receiptFile: string;
	mobileNumber: string;
}) => ({
	templateName: whatsappTemplateTypes.RECEIPT_GENERATION,
	mobileNumber,
	valuesArray: [
		{
			name: 'Client_Name',
			value: clientName
		},
		{
			name: 'Brand_Name',
			value: brandName
		},
		{
			name: 'receipt_generation_date',
			value: receiptDate
		},
		{
			name: 'receipt_file',
			value: receiptFile
		}
	]
});

export const getReceiptGenerationClinicLinkTemplateData = ({
	clientName,
	receiptDate,
	brandName,
	receiptFile,
	mobileNumber,
	client_booking_URL
}: {
	clientName: string;
	receiptDate: string;
	brandName: string;
	receiptFile: string;
	mobileNumber: string;
	client_booking_URL: string;
}) => ({
	templateName: whatsappTemplateTypes.RECEIPT_GENERATION_CLINICLINK,
	mobileNumber,
	valuesArray: [
		{
			name: 'Client_Name',
			value: clientName
		},
		{
			name: 'Brand_Name',
			value: brandName
		},
		{
			name: 'receipt_generation_date',
			value: receiptDate
		},
		{
			name: 'receipt_file',
			value: receiptFile
		},
		{
			name: 'client_booking_URL',
			value: client_booking_URL
		}
	]
});

export const getCreditNoteGenerationTemplateData = ({
	clientName,
	refundDate,
	brandName,
	creditNoteFile,
	mobileNumber
}: {
	clientName: string;
	refundDate: string;
	brandName: string;
	creditNoteFile: string;
	mobileNumber: string;
}) => ({
	templateName: whatsappTemplateTypes.CREDIT_NOTE_GENERATION,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'refund_processing_date',
			value: refundDate
		},
		{
			name: 'credit_note_file',
			value: creditNoteFile
		}
	]
});

export const getCreditNoteGenerationClinicLinkTemplateData = ({
	clientName,
	refundDate,
	brandName,
	creditNoteFile,
	mobileNumber,
	client_booking_URL
}: {
	clientName: string;
	refundDate: string;
	brandName: string;
	creditNoteFile: string;
	mobileNumber: string;
	client_booking_URL: string;
}) => ({
	templateName: whatsappTemplateTypes.CREDIT_NOTE_GENERATION_CLINICLINK,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'refund_processing_date',
			value: refundDate
		},
		{
			name: 'credit_note_file',
			value: creditNoteFile
		},
		{
			name: 'client_booking_URL',
			value: client_booking_URL
		}
	]
});

export const getAppointmentVaccinationCertificateTemplateData = ({
	clientName,
	petName,
	brandName,
	vaccinationFile,
	mobileNumber
}: {
	clientName: string;
	petName: string;
	brandName: string;
	vaccinationFile: string;
	mobileNumber: string;
}) => ({
	templateName: whatsappTemplateTypes.VACCINATION_GENERATION,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'pet_name',
			value: petName
		},
		{
			name: 'vaccination_file',
			value: vaccinationFile
		}
	]
});

export const getAppointmentVaccinationCertificateClinicLinkTemplateData = ({
	clientName,
	petName,
	brandName,
	vaccinationFile,
	mobileNumber,
	client_booking_URL
}: {
	clientName: string;
	petName: string;
	brandName: string;
	vaccinationFile: string;
	mobileNumber: string;
	client_booking_URL: string;
}) => ({
	templateName: whatsappTemplateTypes.VACCINATION_GENERATION_CLINICLINK,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'pet_name',
			value: petName
		},
		{
			name: 'vaccination_file',
			value: vaccinationFile
		},
		{
			name: 'client_booking_URL',
			value: client_booking_URL
		}
	]
});

export const getAppointmentInvoiceTemplateData = ({
	clientName,
	petName,
	brandName,
	doctorName,
	invoiceFile,
	mobileNumber
}: {
	clientName: string;
	petName: string;
	brandName: string;
	invoiceFile: string;
	mobileNumber: string;
	doctorName: string;
}) => ({
	templateName: whatsappTemplateTypes.APPOINTMENT_COMPLETION_INVOICE,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'pet_name',
			value: petName
		},
		{
			name: 'doctor_name',
			value: doctorName
		},
		{
			name: 'invoice_file',
			value: invoiceFile
		}
	]
});

export const getAppointmentInvoiceClinicLinkTemplateData = ({
	clientName,
	petName,
	brandName,
	doctorName,
	invoiceFile,
	mobileNumber,
	client_booking_URL
}: {
	clientName: string;
	petName: string;
	brandName: string;
	invoiceFile: string;
	mobileNumber: string;
	doctorName: string;
	client_booking_URL: string;
}) => ({
	templateName:
		whatsappTemplateTypes.APPOINTMENT_COMPLETION_INVOICE_CLINICLINK,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'pet_name',
			value: petName
		},
		{
			name: 'doctor_name',
			value: doctorName
		},
		{
			name: 'invoice_file',
			value: invoiceFile
		},
		{
			name: 'client_booking_URL',
			value: client_booking_URL
		}
	]
});

export const getAppointmentPrescriptionTemplateData = ({
	clientName,
	petName,
	brandName,
	doctorName,
	prescriptionFile,
	mobileNumber
}: {
	clientName: string;
	petName: string;
	brandName: string;
	prescriptionFile: string;
	mobileNumber: string;
	doctorName: string;
}) => ({
	templateName: whatsappTemplateTypes.APPOINTMENT_COMPLETION_PRESCRIPTION,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'pet_name',
			value: petName
		},
		{
			name: 'doctor_name',
			value: doctorName
		},
		{
			name: 'prescription_file',
			value: prescriptionFile
		}
	]
});

export const getAppointmentPrescriptionClinicLinkTemplateData = ({
	clientName,
	petName,
	brandName,
	doctorName,
	prescriptionFile,
	mobileNumber,
	client_booking_URL
}: {
	clientName: string;
	petName: string;
	brandName: string;
	prescriptionFile: string;
	mobileNumber: string;
	doctorName: string;
	client_booking_URL: string;
}) => ({
	templateName:
		whatsappTemplateTypes.APPOINTMENT_COMPLETION_PRESCRIPTION_CLINICLINK,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'pet_name',
			value: petName
		},
		{
			name: 'doctor_name',
			value: doctorName
		},
		{
			name: 'prescription_file',
			value: prescriptionFile
		},
		{
			name: 'client_booking_URL',
			value: client_booking_URL
		}
	]
});

export const getIndividualEMRTemplateData = ({
	clientName,
	petName,
	brandName,
	EMRFile,
	mobileNumber,
	clinicContact
}: {
	clientName: string;
	petName: string;
	brandName: string;
	EMRFile: string;
	mobileNumber: string;
	clinicContact: string;
}) => ({
	templateName: whatsappTemplateTypes.MEDICAL_RECORDS,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'pet_name',
			value: petName
		},
		{
			name: 'record_pdf',
			value: EMRFile
		},
		{
			name: 'contact_information',
			value: clinicContact
		}
	]
});

export const getIndividualEMRClinicLinkTemplateData = ({
	clientName,
	petName,
	brandName,
	EMRFile,
	mobileNumber,
	clinicContact,
	client_booking_URL
}: {
	clientName: string;
	petName: string;
	brandName: string;
	EMRFile: string;
	mobileNumber: string;
	clinicContact: string;
	client_booking_URL: string;
}) => ({
	templateName: whatsappTemplateTypes.MEDICAL_RECORDS_CLINICLINK,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'pet_name',
			value: petName
		},
		{
			name: 'record_pdf',
			value: EMRFile
		},
		{
			name: 'contact_information',
			value: clinicContact
		},
		{
			name: 'client_booking_URL',
			value: client_booking_URL
		}
	]
});

export const sendSignableDocumentUrl = ({
	clientName,
	brandName,
	url,
	mobileNumber,
	patientName
}: {
	clientName: string;
	brandName: string;
	url: string;
	mobileNumber: string;
	patientName: string;
}) => ({
	templateName: whatsappTemplateTypes.SEND_SIGNABLE_DOCUMENT,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'patient_name',
			value: patientName
		},
		{
			name: 'url',
			value: url
		}
	]
});

export const sendNonSignableDocument = ({
	clientName,
	brandName,
	contactNo,
	mobileNumber,
	patientName,
	documentName,
	documentUrl
}: {
	clientName: string;
	brandName: string;
	contactNo: string;
	mobileNumber: string;
	patientName: string;
	documentName: string;
	documentUrl: string;
}) => ({
	templateName: whatsappTemplateTypes.SEND_NON_SIGNABLE_DOCUMENT,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'patient_name',
			value: patientName
		},
		{
			name: 'contact_no',
			value: contactNo
		},
		{
			name: 'document_name',
			value: documentName
		},
		{
			name: 'document_url',
			value: documentUrl
		}
	]
});

export const sendNonSignableDocumentClinicLink = ({
	clientName,
	brandName,
	contactNo,
	mobileNumber,
	patientName,
	documentName,
	documentUrl,
	client_booking_URL
}: {
	clientName: string;
	brandName: string;
	contactNo: string;
	mobileNumber: string;
	patientName: string;
	documentName: string;
	documentUrl: string;
	client_booking_URL: string;
}) => ({
	templateName: whatsappTemplateTypes.SEND_NON_SIGNABLE_DOCUMENT_CLINICLINK,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'patient_name',
			value: patientName
		},
		{
			name: 'contact_no',
			value: contactNo
		},
		{
			name: 'document_name',
			value: documentName
		},
		{
			name: 'document_url',
			value: documentUrl
		},
		{
			name: 'client_booking_URL',
			value: client_booking_URL
		}
	]
});

export const sendDiagnosticReportImage = ({
	clientName,
	mobileNumber,
	documentUrl,
	petName
}: {
	clientName: string;
	mobileNumber: string;
	documentUrl: string;
	petName: string;
}) => ({
	templateName: whatsappTemplateTypes.DIAGNOSTIC_REPORT_IMAGE,
	mobileNumber,
	valuesArray: [
		{
			name: 'name',
			value: clientName
		},
		{
			name: 'pet_name',
			value: petName
		},
		{
			name: 'document_url',
			value: documentUrl
		}
	]
});

export const sendDiagnosticReportDocument = ({
	clientName,
	mobileNumber,
	documentUrl,
	petName
}: {
	clientName: string;
	mobileNumber: string;
	documentUrl: string;
	petName: string;
}) => ({
	templateName: whatsappTemplateTypes.DIAGNOSTIC_REPORT_PDF,
	mobileNumber,
	valuesArray: [
		{
			name: 'name',
			value: clientName
		},
		{
			name: 'document_url',
			value: documentUrl
		},
		{
			name: 'pet_name',
			value: petName
		}
	]
});

export const getVaccinationImageTemplateData = ({
	clientName,
	petName,
	brandName,
	EMRFile,
	mobileNumber,
	clinicContact
}: {
	clientName: string;
	petName: string;
	brandName: string;
	EMRFile: string;
	mobileNumber: string;
	clinicContact: string;
}) => ({
	templateName: whatsappTemplateTypes.VACCINATION_IMAGE,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'pet_name',
			value: petName
		},
		{
			name: 'record_pdf',
			value: EMRFile
		},
		{
			name: 'contact_information',
			value: clinicContact
		}
	]
});

export const getVaccinationImageClinicLinkTemplateData = ({
	clientName,
	petName,
	brandName,
	EMRFile,
	mobileNumber,
	clinicContact,
	client_booking_URL
}: {
	clientName: string;
	petName: string;
	brandName: string;
	EMRFile: string;
	mobileNumber: string;
	clinicContact: string;
	client_booking_URL: string;
}) => ({
	templateName: whatsappTemplateTypes.VACCINATION_IMAGE_CLINICLINK,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'pet_name',
			value: petName
		},
		{
			name: 'record_pdf',
			value: EMRFile
		},
		{
			name: 'contact_information',
			value: clinicContact
		},
		{
			name: 'client_booking_URL',
			value: client_booking_URL
		}
	]
});

export const sendMedicalRecordDocument = ({
	clientName,
	mobileNumber,
	documentUrl,
	petName
}: {
	clientName: string;
	mobileNumber: string;
	documentUrl: string;
	petName: string;
}) => ({
	templateName: whatsappTemplateTypes.MEDICAL_RECORD_PDF,
	mobileNumber,
	valuesArray: [
		{
			name: 'name',
			value: clientName
		},
		{
			name: 'document_url',
			value: documentUrl
		},
		{
			name: 'pet_name',
			value: petName
		}
	]
});

export const sendMedicalRecordImage = ({
	clientName,
	mobileNumber,
	documentUrl,
	petName
}: {
	clientName: string;
	mobileNumber: string;
	documentUrl: string;
	petName: string;
}) => ({
	templateName: whatsappTemplateTypes.MEDICAL_RECORD_IMAGE,
	mobileNumber,
	valuesArray: [
		{
			name: 'name',
			value: clientName
		},
		{
			name: 'document_url',
			value: documentUrl
		},
		{
			name: 'pet_name',
			value: petName
		}
	]
});

export const sendTreatmentEstimateUrl = ({
	clientName,
	brandName,
	url,
	mobileNumber,
	patientName
}: {
	clientName: string;
	brandName: string;
	url: string;
	mobileNumber: string;
	patientName: string;
}) => ({
	templateName: whatsappTemplateTypes.TREATMENT_ESTIMATE_URL,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'patient_name',
			value: patientName
		},
		{
			name: 'url',
			value: url
		}
	]
});

export const sendtreatmentEstimateDocument = ({
	clientName,
	brandName,
	contactNo,
	mobileNumber,
	patientName,
	documentName,
	documentUrl
}: {
	clientName: string;
	brandName: string;
	contactNo: string;
	mobileNumber: string;
	patientName: string;
	documentName: string;
	documentUrl: string;
}) => ({
	templateName: whatsappTemplateTypes.TREATMENT_ESTIMATE_DOCUMENT,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'patient_name',
			value: patientName
		},
		{
			name: 'contact_no',
			value: contactNo
		},
		{
			name: 'document_name',
			value: documentName
		},
		{
			name: 'document_url',
			value: documentUrl
		}
	]
});

export const sendtreatmentEstimateDocumentClinicLink = ({
	clientName,
	brandName,
	contactNo,
	mobileNumber,
	patientName,
	documentName,
	documentUrl,
	client_booking_URL
}: {
	clientName: string;
	brandName: string;
	contactNo: string;
	mobileNumber: string;
	patientName: string;
	documentName: string;
	documentUrl: string;
	client_booking_URL: string;
}) => ({
	templateName: whatsappTemplateTypes.TREATMENT_ESTIMATE_DOCUMENT_CLINICLINK,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'patient_name',
			value: patientName
		},
		{
			name: 'contact_no',
			value: contactNo
		},
		{
			name: 'document_name',
			value: documentName
		},
		{
			name: 'document_url',
			value: documentUrl
		},
		{
			name: 'client_booking_URL',
			value: client_booking_URL
		}
	]
});

export const sendSupportingDocumentPdf = ({
	clientName,
	mobileNumber,
	documentUrl
}: {
	clientName: string;
	mobileNumber: string;
	documentUrl: string;
}) => ({
	templateName: whatsappTemplateTypes.SUPPORTING_DOCUMENT_PDF,
	mobileNumber,
	valuesArray: [
		{
			name: 'name',
			value: clientName
		},
		{
			name: 'document_url',
			value: documentUrl
		}
	]
});

export const sendSupportingDocumentImage = ({
	clientName,
	mobileNumber,
	documentUrl
}: {
	clientName: string;
	mobileNumber: string;
	documentUrl: string;
}) => ({
	templateName: whatsappTemplateTypes.SUPPORTING_DOCUMENT_IMAGE,
	mobileNumber,
	valuesArray: [
		{
			name: 'name',
			value: clientName
		},
		{
			name: 'document_url',
			value: documentUrl
		}
	]
});

export const reminderNotifications = ({
	clientName,
	dueDate,
	title,
	brand,
	petName,
	mobileNumber
}: {
	clientName: string;
	dueDate: string;
	title: string;
	brand: string;
	petName: string;
	mobileNumber: string;
}) => ({
	templateName: whatsappTemplateTypes.REMINDER_NOTIFICATION,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'title',
			value: title
		},
		{
			name: 'due_date',
			value: dueDate
		},
		{
			name: 'pet_name',
			value: petName
		},
		{
			name: 'brand_name',
			value: brand
		}
	]
});

export const reminderNotificationsClinicLink = ({
	clientName,
	dueDate,
	title,
	brand,
	petName,
	mobileNumber,
	client_booking_URL
}: {
	clientName: string;
	dueDate: string;
	title: string;
	brand: string;
	petName: string;
	mobileNumber: string;
	client_booking_URL: string;
}) => ({
	templateName: whatsappTemplateTypes.REMINDER_NOTIFICATION_CLINICLINK,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'title',
			value: title
		},
		{
			name: 'due_date',
			value: dueDate
		},
		{
			name: 'pet_name',
			value: petName
		},
		{
			name: 'brand_name',
			value: brand
		},
		{
			name: 'client_booking_URL',
			value: client_booking_URL
		}
	]
});

export const getLedgerDocumentTemplateData = ({
	clientName,
	brandName,
	EMRFile,
	mobileNumber,
	clinicContact
}: {
	clientName: string;
	brandName: string;
	EMRFile: string;
	mobileNumber: string;
	clinicContact: string;
}) => ({
	templateName: whatsappTemplateTypes.LEDGER_DOCUMENT,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'record_pdf',
			value: EMRFile
		},
		{
			name: 'contact_information',
			value: clinicContact
		}
	]
});

export const getLedgerDocumentClinicLinkTemplateData = ({
	clientName,
	brandName,
	EMRFile,
	mobileNumber,
	clinicContact,
	client_booking_URL
}: {
	clientName: string;
	brandName: string;
	EMRFile: string;
	mobileNumber: string;
	clinicContact: string;
	client_booking_URL: string;
}) => ({
	templateName: whatsappTemplateTypes.LEDGER_DOCUMENT_CLINICLINK,
	mobileNumber,
	valuesArray: [
		{
			name: 'client_name',
			value: clientName
		},
		{
			name: 'brand_name',
			value: brandName
		},
		{
			name: 'record_pdf',
			value: EMRFile
		},
		{
			name: 'contact_information',
			value: clinicContact
		},
		{
			name: 'client_booking_URL',
			value: client_booking_URL
		}
	]
});

export const getPetTransferOldOwnerTemplateData = ({
	oldPetOwner,
	petName,
	newPetOwner,
	brandName,
	mobileNumber
}: {
	oldPetOwner: string;
	petName: string;
	newPetOwner: string;
	brandName: string;
	mobileNumber: string;
}) => ({
	templateName: whatsappTemplateTypes.PET_TRANSFER_OLD_OWNER,
	mobileNumber,
	valuesArray: [
		{
			name: 'initial_pet_owner',
			value: oldPetOwner
		},
		{
			name: 'pet_name',
			value: petName
		},
		{
			name: 'new_pet_owner',
			value: newPetOwner
		},
		{
			name: 'Brand_Name',
			value: brandName
		}
	]
});

export const getPetTransferNewOwnerTemplateData = ({
	oldPetOwner,
	petName,
	newPetOwner,
	brandName,
	mobileNumber
}: {
	oldPetOwner: string;
	petName: string;
	newPetOwner: string;
	brandName: string;
	mobileNumber: string;
}) => ({
	templateName: whatsappTemplateTypes.PET_TRANSFER_NEW_OWNER,
	mobileNumber,
	valuesArray: [
		{
			name: 'new_pet_owner',
			value: newPetOwner
		},
		{
			name: 'pet_name',
			value: petName
		},
		{
			name: 'initial_pet_owner',
			value: oldPetOwner
		},
		{
			name: 'Brand_Name',
			value: brandName
		}
	]
});

export const getPetTransferNewOwnerClinicLinkTemplateData = ({
	oldPetOwner,
	petName,
	newPetOwner,
	brandName,
	mobileNumber,
	client_booking_URL
}: {
	oldPetOwner: string;
	petName: string;
	newPetOwner: string;
	brandName: string;
	mobileNumber: string;
	client_booking_URL: string;
}) => ({
	templateName: whatsappTemplateTypes.PET_TRANSFER_NEW_OWNER_CLINICLINK,
	mobileNumber,
	valuesArray: [
		{
			name: 'new_pet_owner',
			value: newPetOwner
		},
		{
			name: 'pet_name',
			value: petName
		},
		{
			name: 'initial_pet_owner',
			value: oldPetOwner
		},
		{
			name: 'client_booking_URL',
			value: client_booking_URL
		},
		{
			name: 'Brand_Name',
			value: brandName
		}
	]
});

import {
	BadRequestException,
	ConflictException,
	Injectable,
	InternalServerErrorException,
	Inject,
	forwardRef
} from '@nestjs/common';
import { CreateClinicDto, UpdateBasicClinicDto } from './dto/create-clinic.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { ClinicEntity } from './entities/clinic.entity';
import { User } from '../users/entities/user.entity';
import { NotFoundException } from '@nestjs/common';
import { DataSource, In, Repository, IsNull } from 'typeorm';
import { ClinicRoomEntity } from './entities/clinic-room.entity';
import { UpdateClinicDto } from './dto/update-clinic.dto';
import { ReadService } from '../utils/excel/read.service';
import { FormatService } from '../utils/excel/format.service';
import { ClinicConsumblesService } from '../clinic-consumables/clinic-consumbles.service';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { ClinicMedicationsService } from '../clinic-medications/clinic-medications.service';
import { ClinicProductsService } from '../clinic-products/clinic-products.service';
import { ClinicServicesService } from '../clinic-services/clinic-services.service';
import { ClinicVaccinationsService } from '../clinic-vaccinations/clinic-vaccinations.service';
import { ClinicLabReportService } from '../clinic-lab-report/clinic-lab-report.service';
import * as XLSX from 'xlsx';
import * as bcrypt from 'bcrypt';
import {
	ConsumableHeaders,
	ProductHeaders,
	ServiceHeaders,
	DiagnosticHeaders,
	VaccinationHeaders,
	MedicationHeaders
} from './enum/inventory-headers.enum';
import { UsersService } from '../users/users.service';
import { SESMailService } from '../utils/aws/ses/send-mail-service';
import { ClinicUser } from './entities/clinic-user.entity';
import {
	CreateClinicRoomDto,
	UpdateClinicRoomDto
} from './dto/create-clinic-room.dto';
import { ADMIN_ROLE_ID, DEV_SES_EMAIL } from '../utils/constants';
import { getLoginUrl, getFallbackLoginUrl, isProduction } from '../utils/common/get-login-url';
import { BrandService } from '../brands/brands.service';
import { UpdateClientBookingSettingsDto } from './dto/update-client-booking-settings.dto';
import { ClientBookingSettings } from './entities/clinic.entity';
import { ClientBookingSettingsResponseDto } from './dto/client-booking-settings-response.dto';
import {
	ClinicSettingsDto,
	ClinicSettingsResponseDto
} from './dto/clinic-settings.dto';

@Injectable()
export class ClinicService {
	constructor(
		@InjectRepository(ClinicEntity)
		private clinicRepository: Repository<ClinicEntity>,
		@InjectRepository(ClinicRoomEntity)
		private clinicRoomRepository: Repository<ClinicRoomEntity>,
		@InjectRepository(ClinicUser)
		private clinicUserRepository: Repository<ClinicUser>,
		private readonly logger: WinstonLogger,
		private readonly consumblesService: ClinicConsumblesService,
		private readonly clinicMedicationsService: ClinicMedicationsService,
		private readonly productsService: ClinicProductsService,
		private readonly clinicServices: ClinicServicesService,
		private readonly vaccinationService: ClinicVaccinationsService,
		private readonly clinicLabReportService: ClinicLabReportService,
		@Inject(forwardRef(() => UsersService))
		private userService: UsersService,
		private readonly mailService: SESMailService,
		private dataSource: DataSource,
		@Inject(forwardRef(() => BrandService))
		private brandService: BrandService
	) {}

	async createClinic(
		createClinicDto: CreateClinicDto,
		createdBy: string
	): Promise<ClinicEntity> {
		const queryRunner = this.dataSource.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			this.logger.log('Creating new clinic', { dto: createClinicDto });

			const {
				name,
				brandId,
				adminFirstName,
				adminLastName,
				adminEmail,
				adminMobile
			} = createClinicDto;
			console.log({
				name,
				brandId,
				adminFirstName,
				adminLastName,
				adminEmail,
				adminMobile
			});

			const existingClinic = await queryRunner.manager.findOne(
				ClinicEntity,
				{ where: { name } }
			);
			if (existingClinic) {
				throw new ConflictException(
					`The clinic with name ${name} already exists`
				);
			}

			const existingUser = await queryRunner.manager.findOne(User, {
				where: { email: adminEmail }
			});
			if (existingUser) {
				throw new ConflictException(
					`An Admin with email ${adminEmail} already exists`
				);
			}

			const pin = await this.userService.generateUniquePin();
			const hashedPin = await bcrypt.hash(pin, 10);
			const newUser = queryRunner.manager.create(User, {
				firstName: adminFirstName,
				lastName: adminLastName,
				email: adminEmail,
				roleId: ADMIN_ROLE_ID,
				mobileNumber: adminMobile,
				pin: hashedPin,
				createdBy,
				updatedBy: createdBy
			});

			await queryRunner.manager.save(newUser);

			const newClinic = queryRunner.manager.create(ClinicEntity, {
				name,
				brandId,
				adminFirstName,
				adminLastName,
				adminEmail,
				adminMobile,
				createdBy,
				updatedBy: createdBy
			});

			await queryRunner.manager.save(newClinic);

			const clinicId = newClinic.id;
			const userId = newUser.id;
			const newClinicUser = queryRunner.manager.create(ClinicUser, {
				clinicId,
				userId,
				isPrimary: true,
				brandId,
				createdBy,
				updatedBy: createdBy
			});

			await queryRunner.manager.save(newClinicUser);
			const brandInfo = await this.brandService.getBrandById(brandId);
			
			// Generate login URL only if brand slug is available
			let loginUrl = getFallbackLoginUrl(); // Dynamic fallback URL based on environment
			if (brandInfo?.slug) {
				loginUrl = getLoginUrl(brandInfo.slug);
			} else {
				this.logger.warn('Brand slug not found for brand ID', { brandId });
			}
			
			const subject = 'Clinic Registration';
			const body = `
				Dear ${adminFirstName} ${adminLastName},
				Your clinic has been registered with us. Your PIN is: ${pin}. Please use this PIN to log in to your account.
				The URL to login to your clinic is ${loginUrl}
			`;
			console.log(body);
			if (isProduction() && adminEmail) {
				await this.mailService.sendMail({
					body,
					subject,
					toMailAddress: adminEmail
				});
			} else if (!isProduction()) {
				await this.mailService.sendMail({
					body,
					subject,
					toMailAddress: DEV_SES_EMAIL //adminEmail
				});
			}

			await queryRunner.commitTransaction();
			return newClinic;
		} catch (error) {
			await queryRunner.rollbackTransaction();
			this.logger.error('Error creating new clinic', { error });

			if (
				error instanceof ConflictException ||
				error instanceof BadRequestException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'An unexpected error occurred while creating/associating the user'
			);
		} finally {
			await queryRunner.release();
		}
	}

	async getAllClinics(
		page: number = 1,
		limit: number = 10,
		orderBy: string = 'DESC'
	): Promise<{ clinics: ClinicEntity[]; total: number }> {
		const [clinics, total] = await this.clinicRepository.findAndCount({
			where: { deletedAt: IsNull() },
			relations: ['brand'],
			skip: (page - 1) * limit,
			take: limit,
			order:
				orderBy === 'ASC' ? { createdAt: 'ASC' } : { createdAt: 'DESC' }
		});

		return { clinics, total };
	}

	async updateBasicClinicInfo(
		id: string,
		updateBasicClinicDto: UpdateBasicClinicDto,
		userId: string
	): Promise<ClinicEntity> {
		const queryRunner = this.dataSource.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();
		try {
			const clinic = await queryRunner.manager.findOne(ClinicEntity, {
				where: { id }
			});
			if (!clinic) {
				throw new NotFoundException(`Clinic with ID "${id}" not found`);
			}

			const clinicUser = await queryRunner.manager.findOne(ClinicUser, {
				where: { clinicId: clinic.id, isPrimary: true },
				relations: ['user']
			});
			if (!clinicUser) {
				throw new NotFoundException(
					`Primary admin for Clinic with ID "${id}" not found`
				);
			}

			const user = await queryRunner.manager.findOne(User, {
				where: { id: clinicUser.userId }
			});
			let isEmailChanged = false;

			if (user && user.email !== updateBasicClinicDto.adminEmail) {
				isEmailChanged = true;

				const existingUser = await queryRunner.manager.findOne(User, {
					where: { email: updateBasicClinicDto.adminEmail }
				});
				if (existingUser) {
					throw new ConflictException(
						`An admin with email ${updateBasicClinicDto.adminEmail} already exists`
					);
				}
			}

			if (user) {
				user.firstName = updateBasicClinicDto.adminFirstName;
				user.lastName = updateBasicClinicDto.adminLastName;
				user.mobileNumber = updateBasicClinicDto.adminMobile;
				user.updatedBy = userId;
			}

			if (user && isEmailChanged) {
				user.email = updateBasicClinicDto.adminEmail;
			}
			await queryRunner.manager.save(user);

			Object.assign(clinic, updateBasicClinicDto);
			await queryRunner.manager.save(clinic);
			if (isEmailChanged) {
				const subject = 'Your New Admin Credentials';
				const body = `
					Dear ${updateBasicClinicDto.adminFirstName} ${updateBasicClinicDto.adminLastName},
	
					Your new password is 8907. Please use the following link to log in.
				`;

				await this.mailService.sendMail({
					body,
					subject,
					toMailAddress: updateBasicClinicDto.adminEmail
				});
			}
			await queryRunner.commitTransaction();

			return clinic;
		} catch (error) {
			await queryRunner.rollbackTransaction();

			this.logger.error('Error updating clinic', { error });

			if (
				error instanceof ConflictException ||
				error instanceof BadRequestException
			) {
				throw error;
			}

			throw new InternalServerErrorException(
				'An unexpected error occurred while updating the clinic and associated user'
			);
		} finally {
			await queryRunner.release();
		}
	}

	async updateClinic(
		id: string,
		updateClinicDto: UpdateClinicDto,
		userId: string
	): Promise<ClinicEntity> {
		try {
			const clinic = await this.clinicRepository.findOne({
				where: { id }
			});
			if (!clinic) {
				throw new NotFoundException(`Clinic with ID "${id}" not found`);
			}

			// Handle phone numbers separately
			if (updateClinicDto.phoneNumbers) {
				clinic.phoneNumbers = updateClinicDto.phoneNumbers
					.filter(phone => phone.country_code && phone.number)
					.map(phone => ({
						country_code: phone.country_code!,
						number: phone.number!
					}));
			}

			// Handle customRule separately with validation
			if (updateClinicDto.customRule !== undefined) {
				if (
					typeof updateClinicDto.customRule
						.patientLastNameAsOwnerLastName !== 'boolean'
				) {
					throw new BadRequestException(
						'patientLastNameAsOwnerLastName must be a boolean value'
					);
				}
				clinic.customRule = {
					patientLastNameAsOwnerLastName:
						updateClinicDto.customRule
							.patientLastNameAsOwnerLastName
				};
			}

			const { ...restOfDto } = updateClinicDto;

			Object.assign(clinic, restOfDto);

			clinic.updatedBy = userId;

			const updatedClinic = await this.clinicRepository.save(clinic);

			// Ensure customRule is included in response
			return {
				...updatedClinic,
				customRule: updatedClinic.customRule || {
					patientLastNameAsOwnerLastName: false
				}
			};
		} catch (error) {
			this.logger.error('Error updating clinic', { error });

			if (error instanceof BadRequestException) {
				throw error;
			}

			throw new InternalServerErrorException(
				'An unexpected error occurred while updating the clinic'
			);
		}
	}

	async getClinicById(id: string): Promise<ClinicEntity> {
		const clinic = await this.clinicRepository.findOne({ where: { id } });
		if (!clinic) {
			throw new NotFoundException(`This clinic with ${id} doesn't exist`);
		}

		// Ensure customRule is included in response with default value if not set
		return {
			...clinic,
			customRule: clinic.customRule || {
				patientLastNameAsOwnerLastName: false
			}
		};
	}

	async getClinicRooms(id: string) {
		const clinic = await this.clinicRepository.findOne({ where: { id } });
		if (!clinic) {
			throw new NotFoundException(`This clinic with ${id} doesn't exist`);
		}

		const [rooms, total] = await this.clinicRoomRepository.findAndCount({
			where: { clinicId: id }
		});
		return { rooms, total };
	}

	async createClinicRoom(
		createClinicRoomDto: CreateClinicRoomDto,
		brandId: string
	): Promise<ClinicRoomEntity> {
		const clinic = await this.clinicRepository.findOne({
			where: { id: createClinicRoomDto.clinicId }
		});
		if (!clinic) {
			throw new NotFoundException(
				`Clinic with ID ${createClinicRoomDto.clinicId} not found`
			);
		}

		const newRoom = this.clinicRoomRepository.create({
			...createClinicRoomDto,
			brandId: brandId
		});
		return await this.clinicRoomRepository.save(newRoom);
	}

	async updateClinicRoom(
		id: string,
		updateClinicRoomDto: UpdateClinicRoomDto
	): Promise<ClinicRoomEntity> {
		const room = await this.clinicRoomRepository.findOne({ where: { id } });
		if (!room) {
			throw new NotFoundException(`Clinic room with ID ${id} not found`);
		}

		if (updateClinicRoomDto.clinicId) {
			const clinic = await this.clinicRepository.findOne({
				where: { id: updateClinicRoomDto.clinicId }
			});
			if (!clinic) {
				throw new NotFoundException(
					`Clinic with ID ${updateClinicRoomDto.clinicId} not found`
				);
			}
		}

		Object.assign(room, updateClinicRoomDto);
		return await this.clinicRoomRepository.save(room);
	}

	async deleteRoom(id: string): Promise<void> {
		const result = await this.clinicRoomRepository.delete(id);
		if (result.affected === 0) {
			throw new NotFoundException(`Room with ID "${id}" not found`);
		}
	}

	async deactivateClinic(id: string): Promise<ClinicEntity> {
		const queryRunner = this.dataSource.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			const clinic = await queryRunner.manager.findOne(ClinicEntity, {
				where: { id, deletedAt: IsNull() }
			});

			if (!clinic) {
				throw new NotFoundException(`Clinic with id ${id} not found`);
			}

			if (!clinic.isActive) {
				throw new ConflictException(
					`Clinic with id ${id} is already deactivated`
				);
			}

			clinic.isActive = false;
			await queryRunner.manager.save(clinic);

			const clinicUsers = await queryRunner.manager.find(ClinicUser, {
				where: { clinicId: id }
			});

			if (clinicUsers.length > 0) {
				const userIds = clinicUsers.map(
					clinicUser => clinicUser.userId
				);

				await queryRunner.manager.update(
					User,
					{ id: In(userIds) },
					{ isActive: false }
				);
			}

			await queryRunner.commitTransaction();
			return clinic;
		} catch (error) {
			await queryRunner.rollbackTransaction();
			this.logger.error('Error deactivating clinic', { error });

			if (
				error instanceof NotFoundException ||
				error instanceof ConflictException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'An error occurred during clinic deactivation'
			);
		} finally {
			await queryRunner.release();
		}
	}

	async reactivateClinic(id: string): Promise<ClinicEntity> {
		const queryRunner = this.dataSource.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			const clinic = await queryRunner.manager.findOne(ClinicEntity, {
				where: { id, deletedAt: IsNull() }
			});

			if (!clinic) {
				throw new NotFoundException(`Clinic with id ${id} not found`);
			}

			if (clinic.isActive) {
				throw new ConflictException(
					`Clinic with id ${id} is already active`
				);
			}

			clinic.isActive = true;
			await queryRunner.manager.save(clinic);

			const clinicUsers = await queryRunner.manager.find(ClinicUser, {
				where: { clinicId: id }
			});

			if (clinicUsers.length > 0) {
				const userIds = clinicUsers.map(
					clinicUser => clinicUser.userId
				);

				await queryRunner.manager.update(
					User,
					{ id: In(userIds) },
					{ isActive: true }
				);
			}

			await queryRunner.commitTransaction();
			return clinic;
		} catch (error) {
			await queryRunner.rollbackTransaction();
			this.logger.error('Error reactivating clinic', { error });

			if (
				error instanceof NotFoundException ||
				error instanceof ConflictException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'An error occurred during clinic reactivation'
			);
		} finally {
			await queryRunner.release();
		}
	}

	async softDeleteClinic(id: string): Promise<ClinicEntity> {
		const queryRunner = this.dataSource.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			const clinic = await queryRunner.manager.findOne(ClinicEntity, {
				where: { id, deletedAt: IsNull() }
			});

			if (!clinic) {
				throw new NotFoundException(`Clinic with id ${id} not found`);
			}

			if (clinic.deletedAt) {
				throw new ConflictException(
					`Clinic with id ${id} is already deleted`
				);
			}

			clinic.deletedAt = new Date();
			clinic.isActive = false; // Also deactivate when soft deleting
			await queryRunner.manager.save(clinic);

			// Also deactivate all users associated with this clinic
			const clinicUsers = await queryRunner.manager.find(ClinicUser, {
				where: { clinicId: id }
			});

			if (clinicUsers.length > 0) {
				const userIds = clinicUsers.map(
					clinicUser => clinicUser.userId
				);

				await queryRunner.manager.update(
					User,
					{ id: In(userIds) },
					{ isActive: false }
				);
			}

			await queryRunner.commitTransaction();
			return clinic;
		} catch (error) {
			await queryRunner.rollbackTransaction();
			this.logger.error('Error soft deleting clinic', { error });

			if (
				error instanceof NotFoundException ||
				error instanceof ConflictException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'An error occurred during clinic soft deletion'
			);
		} finally {
			await queryRunner.release();
		}
	}

	async processBulkUpload(
		file: Express.Multer.File,
		clinicId: string,
		brandId: string
	) {
		const sheetData = await ReadService.readExcelBuffer(file.buffer);
		const results: { [sheetName: string]: any } = {};

		for (const [sheetName, data] of Object.entries(sheetData)) {
			this.logger.log(`Processing sheet: ${sheetName}`);

			try {
				switch (sheetName.toLowerCase()) {
					case 'consumables':
						results[sheetName] = await this.processConsumables(
							data,
							clinicId,
							brandId
						);
						break;
					case 'medications':
						results[sheetName] = await this.processMedications(
							data,
							clinicId,
							brandId
						);
						break;
					case 'products':
						results[sheetName] = await this.processProducts(
							data,
							clinicId,
							brandId
						);
						break;
					case 'services':
						results[sheetName] = await this.processServices(
							data,
							clinicId,
							brandId
						);
						break;
					case 'vaccinations':
						results[sheetName] = await this.processVaccinations(
							data,
							clinicId,
							brandId
						);
						break;
					case 'diagnostics':
						results[sheetName] = await this.processDiagnostics(
							data,
							clinicId,
							brandId
						);
						break;
					default:
						this.logger.error(`Unknown sheet type: ${sheetName}`);
						continue;
				}
			} catch (error) {
				this.logger.error(`Error processing ${sheetName}`, error);
				results[sheetName] = {
					summary: { created: 0, updated: 0, failed: 0 },
					errors: [],
					errorsUpdate: [
						{
							id: 'bulk-operation',
							name: sheetName,
							errors: [`Failed to process sheet: ${sheetName}`]
						}
					]
				};
			}
		}

		return results;
	}

	private async processMedications(
		data: any[],
		clinicId: string,
		brandId: string
	) {
		const formattedData = FormatService.formatClinicMedications(
			data,
			clinicId,
			brandId
		);
		return await this.processItems(
			formattedData,
			this.clinicMedicationsService
		);
	}

	private async processConsumables(
		data: any[],
		clinicId: string,
		brandId: string
	) {
		const formattedData = FormatService.formatClinicConsumables(
			data,
			clinicId,
			brandId
		);
		return await this.processItems(formattedData, this.consumblesService);
	}

	private async processVaccinations(
		data: any[],
		clinicId: string,
		brandId: string
	) {
		const formattedData = FormatService.formatClinicVaccinations(
			data,
			clinicId,
			brandId
		);
		return await this.processItems(formattedData, this.vaccinationService);
	}

	private async processServices(
		data: any[],
		clinicId: string,
		brandId: string
	) {
		const formattedData = FormatService.formatClinicServices(
			data,
			clinicId,
			brandId
		);
		return await this.processItems(formattedData, this.clinicServices);
	}

	private async processProducts(
		data: any[],
		clinicId: string,
		brandId: string
	) {
		const formattedData = FormatService.formatClinicProducts(
			data,
			clinicId,
			brandId
		);
		return await this.processItems(formattedData, this.productsService);
	}

	private async processDiagnostics(
		data: any[],
		clinicId: string,
		brandId: string
	) {
		const formattedData = FormatService.formatClinicLabReports(
			data,
			clinicId,
			brandId
		);
		return await this.processItems(
			formattedData,
			this.clinicLabReportService
		);
	}

	private async processItems<T>(
		formattedData: { insertArray: T[]; errorArray: any[] },
		service: any
	) {
		const summary = { updated: 0, created: 0, failed: 0 };
		const errorArray = [];
		const itemsToInsert = [];
		const itemsToUpdate = [];

		for (const item of formattedData.insertArray) {
			try {
				let searchCriteria;
				if (service === this.clinicLabReportService) {
					searchCriteria = {
						name: (item as any).name,
						clinicId: (item as any).clinicId
					};
				} else if (service === this.consumblesService) {
					searchCriteria = {
						productName: (item as any).productName,
						clinicId: (item as any).clinicId
					};
				} else if (service === this.clinicServices) {
					searchCriteria = {
						serviceName: (item as any).serviceName,
						clinicId: (item as any).clinicId
					};
				} else if (service === this.productsService) {
					searchCriteria = {
						productName: (item as any).productName,
						clinicId: (item as any).clinicId
					};
				} else if (service === this.vaccinationService) {
					searchCriteria = {
						productName: (item as any).productName,
						clinicId: (item as any).clinicId
					};
				} else if (service === this.clinicMedicationsService) {
					searchCriteria = {
						name: (item as any).name || (item as any).productName,
						clinicId: (item as any).clinicId
					};
				}

				this.logger.log(
					'Searching for existing item with criteria:',
					searchCriteria
				);
				const existingItem = await service.findOneEntry(searchCriteria);
				this.logger.log('Search result:', existingItem);

				if (existingItem) {
					this.logger.log(
						'Found existing item, will update:',
						existingItem
					);
					itemsToUpdate.push({ ...existingItem, ...item });
					summary.updated++;
				} else {
					this.logger.log('No existing item found, will create new');
					itemsToInsert.push(item);
					summary.created++;
				}
			} catch (error) {
				this.logger.error('Error processing item', { error, item });
				summary.failed++;
				errorArray.push({
					id:
						(item as any).uniqueId ||
						(item as any).name ||
						(item as any).productName,
					errors: [
						`Failed to process: ${(item as any).uniqueId || (item as any).name || (item as any).productName}`
					]
				});
			}
		}

		try {
			if (itemsToInsert.length > 0) {
				this.logger.log('Inserting new items', {
					count: itemsToInsert.length,
					items: itemsToInsert
				});
				await service.bulkInsert(itemsToInsert);
			}
			if (itemsToUpdate.length > 0) {
				this.logger.log('Updating existing items', {
					count: itemsToUpdate.length,
					items: itemsToUpdate
				});
				await service.bulkInsert(itemsToUpdate);
			}
		} catch (error) {
			this.logger.error('Error during bulk operation', { error });
			summary.failed += itemsToInsert.length + itemsToUpdate.length;
			summary.created = 0;
			summary.updated = 0;
			errorArray.push({
				id: 'bulk-operation',
				name: 'operation',
				errors: ['Failed to perform bulk operation']
			});
		}

		return {
			summary,
			errors: formattedData.errorArray,
			errorsUpdate: errorArray
		};
	}

	async generateInventoryExcel(clinicId: string): Promise<Buffer> {
		const clinic = await this.clinicRepository.findOne({
			where: { id: clinicId }
		});
		if (!clinic) {
			throw new NotFoundException(
				`Clinic with ID "${clinicId}" not found`
			);
		}

		const workbook = XLSX.utils.book_new();
		const createSheet = (
			data: any[],
			headers: string[],
			sheetName: string
		) => {
			const worksheet = XLSX.utils.aoa_to_sheet([]);
			XLSX.utils.sheet_add_aoa(worksheet, [headers], { origin: 'B2' });
			XLSX.utils.sheet_add_json(worksheet, data, {
				origin: 'B3',
				skipHeader: true
			});

			if (worksheet['!ref']) {
				const headerRange = XLSX.utils.decode_range(worksheet['!ref']);
				for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
					const cellRef = XLSX.utils.encode_cell({ r: 1, c: col });
					if (worksheet[cellRef]) {
						worksheet[cellRef].s = { font: { bold: true } };
					}
				}
			}

			const columnWidth = 20;
			worksheet['!cols'] = headers.map(() => ({ wch: columnWidth }));
			XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
		};

		const consumables =
			await this.consumblesService.getConsumables(clinicId);
		const consumablesHeaders = Object.values(ConsumableHeaders);
		const consumablesData = consumables.map(c => ({
			// [ConsumableHeaders.UNIQUE_ID]: c.uniqueId,
			[ConsumableHeaders.PRODUCT_NAME]: c.productName,
			[ConsumableHeaders.CURRENT_STOCK]: c.currentStock,
			[ConsumableHeaders.MINIMUM_QUANTITY]: c.minimumQuantity
		}));
		console.log(consumablesData, consumablesHeaders);
		createSheet(consumablesData, consumablesHeaders, 'Consumables');

		// Products
		const products = await this.productsService.getProducts(clinicId);
		const productsHeaders = Object.values(ProductHeaders);
		const productsData = products.map(p => ({
			// [ProductHeaders.UNIQUE_ID]: p.uniqueId,
			[ProductHeaders.PRODUCT_NAME]: p.productName,
			[ProductHeaders.CHARGEABLE_PRICE]: p.chargeablePrice,
			[ProductHeaders.TAX]: p.tax,
			[ProductHeaders.CURRENT_STOCK]: p.currentStock,
			[ProductHeaders.MINIMUM_QUANTITY]: p.minimumQuantity
		}));
		createSheet(productsData, productsHeaders, 'Products');

		// Services
		const services = await this.clinicServices.getServices(clinicId);
		const servicesHeaders = Object.values(ServiceHeaders);
		const servicesData = services.map(s => ({
			// [ServiceHeaders.UNIQUE_ID]: s.uniqueId,
			[ServiceHeaders.SERVICE_NAME]: s.serviceName,
			[ServiceHeaders.CHARGEABLE_PRICE]: s.chargeablePrice,
			[ServiceHeaders.TAX]: s.tax
		}));
		createSheet(servicesData, servicesHeaders, 'Services');

		// Lab Reports (Diagnostics)
		const labReports =
			await this.clinicLabReportService.getLabReports(clinicId);
		const filteredLabReports = labReports.filter(l => !l.integrationType);
		const labReportsHeaders = Object.values(DiagnosticHeaders);
		const labReportsData = filteredLabReports.map(l => ({
			[DiagnosticHeaders.SERVICE_NAME]: l.name,
			[DiagnosticHeaders.CHARGEABLE_PRICE]: l.chargeablePrice,
			[DiagnosticHeaders.TAX]: l.tax
		}));
		createSheet(labReportsData, labReportsHeaders, 'Diagnostics');

		// Vaccinations
		const vaccinations =
			await this.vaccinationService.getVaccinations(clinicId);
		const vaccinationsHeaders = Object.values(VaccinationHeaders);
		const vaccinationsData = vaccinations.map(v => ({
			// [VaccinationHeaders.UNIQUE_ID]: v.uniqueId,
			[VaccinationHeaders.PRODUCT_NAME]: v.productName,
			[VaccinationHeaders.CHARGEABLE_PRICE]: v.chargeablePrice,
			[VaccinationHeaders.TAX]: v.tax,
			[VaccinationHeaders.CURRENT_STOCK]: v.currentStock,
			[VaccinationHeaders.MINIMUM_QUANTITY]: v.minimumQuantity
		}));
		createSheet(vaccinationsData, vaccinationsHeaders, 'Vaccinations');

		// Medications
		const medications =
			await this.clinicMedicationsService.getMedications(clinicId);
		const medicationsHeaders = Object.values(MedicationHeaders);
		const medicationsData = medications.map(m => ({
			// [MedicationHeaders.UNIQUE_ID]: m.uniqueId,
			[MedicationHeaders.MEDICATION_NAME]: m.name,
			[MedicationHeaders.RESTRICTED_SUBSTANCE]: m.isRestricted,
			[MedicationHeaders.CHARGEABLE_PRICE]: m.chargeablePrice,
			[MedicationHeaders.TAX]: m.tax,
			[MedicationHeaders.CURRENT_STOCK]: m.currentStock,
			[MedicationHeaders.MINIMUM_QUANTITY]: m.minimumQuantity
		}));
		createSheet(medicationsData, medicationsHeaders, 'Medications');

		const excelBuffer = XLSX.write(workbook, {
			bookType: 'xlsx',
			type: 'buffer'
		});
		return excelBuffer;
	}

	async deleteInventoryItem(itemType: string, itemId: string) {
		let service;

		switch (itemType) {
			case 'consumables':
				service = this.consumblesService;
				break;
			case 'medications':
				service = this.clinicMedicationsService;
				break;
			case 'products':
				service = this.productsService;
				break;
			case 'services':
				service = this.clinicServices;
				break;
			case 'vaccinations':
				service = this.vaccinationService;
				break;
			case 'diagnostics':
				service = this.clinicLabReportService;
				break;
			default:
				throw new NotFoundException(`Invalid item type: ${itemType}`);
		}

		try {
			await service.deleteItem(itemId);
			return { message: `${itemType} item deleted successfully` };
		} catch (error) {
			if (error instanceof NotFoundException) {
				throw error;
			}
			throw new InternalServerErrorException(
				`Failed to delete ${itemType} item`
			);
		}
	}

	// Method to retrieve client booking settings, now returns the enhanced DTO
	async getClientBookingSettings(
		clinicId: string
	): Promise<ClientBookingSettingsResponseDto | null> {
		this.logger.log('Fetching client booking settings', { clinicId });
		try {
			const clinic = await this.clinicRepository.findOne({
				where: { id: clinicId }
			});

			if (!clinic) {
				this.logger.warn('Clinic not found for settings retrieval', {
					clinicId
				});
				throw new NotFoundException(
					`Clinic with ID "${clinicId}" not found`
				);
			}

			// Get the settings from the customRule JSONB field
			const settings = clinic.customRule?.clientBookingSettings;

			// If settings don't exist or are null, return null
			if (!settings) {
				this.logger.log(
					'Client booking settings not configured for clinic',
					{ clinicId }
				);
				return null;
			}

			// Prepare the base response object conforming to the DTO
			const response: ClientBookingSettingsResponseDto = {
				isEnabled: settings.isEnabled ?? false, // Provide default
				allowAllDoctors: settings.allowAllDoctors ?? false, // Default to false if null/undefined
				workingHours: settings.workingHours ?? undefined, // Use undefined if null
				allowedDoctorIds: settings.allowedDoctorIds ?? undefined,

				// Include new time duration fields
				minBookingLeadTime: settings.minBookingLeadTime ?? undefined,
				modificationDeadlineTime:
					settings.modificationDeadlineTime ?? undefined,
				maxAdvanceBookingTime:
					settings.maxAdvanceBookingTime ?? undefined,

				// Legacy fields
				minBookingLeadHours: settings.minBookingLeadHours ?? undefined,
				modificationDeadlineHours:
					settings.modificationDeadlineHours ?? undefined,
				allowedDoctorsInfo: [] // Initialize as empty array for consistency
			};

			// If allowedDoctorIds exist and the array is not empty, fetch doctor names
			if (
				settings.allowedDoctorIds &&
				settings.allowedDoctorIds.length > 0
			) {
				this.logger.log('Fetching doctor details for allowed IDs', {
					clinicId,
					ids: settings.allowedDoctorIds
				});
				try {
					const allowedClinicUsers =
						await this.clinicUserRepository.find({
							where: {
								clinicId: clinicId, // Ensure users belong to the correct clinic
								id: In(settings.allowedDoctorIds)
							},
							relations: ['user'], // Still need to load the related User entity
							select: {
								// Select necessary fields
								id: true,
								userId: true,
								user: {
									id: true,
									firstName: true,
									lastName: true
								}
							}
						});

					// Log the raw result from the database query
					this.logger.log(
						'Found clinic users from DB query (using ClinicUser ID):',
						{
							clinicId,
							allowedClinicUsers:
								JSON.stringify(allowedClinicUsers)
						}
					);

					// Map the found clinic users to the DoctorInfo structure
					// The response still needs the User ID and name
					response.allowedDoctorsInfo = allowedClinicUsers.map(
						cu => ({
							id: cu.userId, // Return the actual User ID
							// Construct the full name
							name:
								`${cu.user?.firstName ?? ''} ${cu.user?.lastName ?? ''}`.trim() ||
								'Name Unavailable'
						})
					);

					this.logger.log('Successfully fetched doctor details', {
						clinicId,
						count: response.allowedDoctorsInfo.length
					});
				} catch (userQueryError) {
					// Log the error but potentially continue without doctor names if that's acceptable
					this.logger.error(
						'Error fetching doctor details for client booking settings',
						{
							clinicId,
							allowedDoctorIds: settings.allowedDoctorIds,
							error: userQueryError
						}
					);
					// Decide if you want to throw or return partial data. Here, we'll return without doctor names.
					response.allowedDoctorsInfo = []; // Or undefined, depending on desired behavior on error
				}
			} else {
				this.logger.log(
					'No allowedDoctorIds specified or array is empty',
					{
						clinicId
					}
				);
				// Ensure allowedDoctorsInfo is an empty array if no IDs are provided
				// response.allowedDoctorsInfo = []; // Already initialized above
			}

			// Return the populated response object
			return response;
		} catch (error) {
			this.logger.error('Error fetching client booking settings', {
				clinicId,
				error
			});
			// Re-throw known exceptions
			if (
				error instanceof NotFoundException ||
				error instanceof InternalServerErrorException
			) {
				throw error;
			}
			// Throw a generic server error for other unexpected issues
			throw new InternalServerErrorException(
				'An unexpected error occurred while retrieving client booking settings'
			);
		}
	}

	// Method to update client booking settings
	async updateClientBookingSettings(
		clinicId: string,
		dto: UpdateClientBookingSettingsDto,
		updatedBy: string
	): Promise<ClinicEntity> {
		this.logger.log('Updating client booking settings', {
			clinicId,
			dto,
			updatedBy
		});
		try {
			const clinic = await this.clinicRepository.findOne({
				where: { id: clinicId }
			});

			if (!clinic) {
				this.logger.warn('Clinic not found for settings update', {
					clinicId
				});
				throw new NotFoundException(
					`Clinic with ID "${clinicId}" not found`
				);
			}

			// Get existing customRule or initialize with default structure if null/undefined
			const currentCustomRule = clinic.customRule || {
				patientLastNameAsOwnerLastName: false, // Keep existing or default value
				clientBookingSettings: null
			};

			// Get existing settings or initialize an empty object if null/undefined
			// Provide a default structure conforming to ClientBookingSettings if null
			const currentSettings: ClientBookingSettings =
				currentCustomRule.clientBookingSettings || {
					isEnabled: false,
					allowAllDoctors: false, // Default for new field
					workingHours: null,
					allowedDoctorIds: null,
					minBookingLeadTime: null,
					modificationDeadlineTime: null,
					maxAdvanceBookingTime: null,
					minBookingLeadHours: null,
					modificationDeadlineHours: null
				};

			// Merge the DTO into the current settings
			// Ensure the result conforms to ClientBookingSettings
			const mergedSettings: ClientBookingSettings = {
				isEnabled:
					dto.isEnabled !== undefined
						? dto.isEnabled
						: currentSettings.isEnabled,
				allowAllDoctors:
					dto.allowAllDoctors !== undefined
						? dto.allowAllDoctors
						: currentSettings.allowAllDoctors,
				workingHours:
					dto.workingHours !== undefined
						? dto.workingHours
						: currentSettings.workingHours,
				allowedDoctorIds:
					dto.allowedDoctorIds !== undefined
						? dto.allowedDoctorIds
						: currentSettings.allowedDoctorIds,

				// New time duration fields
				minBookingLeadTime:
					dto.minBookingLeadTime !== undefined
						? dto.minBookingLeadTime
						: currentSettings.minBookingLeadTime,
				modificationDeadlineTime:
					dto.modificationDeadlineTime !== undefined
						? dto.modificationDeadlineTime
						: currentSettings.modificationDeadlineTime,
				maxAdvanceBookingTime:
					dto.maxAdvanceBookingTime !== undefined
						? dto.maxAdvanceBookingTime
						: currentSettings.maxAdvanceBookingTime,

				// Legacy fields for backward compatibility
				minBookingLeadHours:
					dto.minBookingLeadHours !== undefined
						? dto.minBookingLeadHours
						: currentSettings.minBookingLeadHours,
				modificationDeadlineHours:
					dto.modificationDeadlineHours !== undefined
						? dto.modificationDeadlineHours
						: currentSettings.modificationDeadlineHours
			};

			// If allowAllDoctors is true, force allowedDoctorIds to null
			if (mergedSettings.allowAllDoctors === true) {
				mergedSettings.allowedDoctorIds = null;
				this.logger.log(
					'allowAllDoctors is true, setting allowedDoctorIds to null',
					{ clinicId }
				);
			}

			// Update the customRule object
			currentCustomRule.clientBookingSettings = mergedSettings;

			// Assign the updated rule back to the clinic entity
			clinic.customRule = currentCustomRule;
			clinic.updatedBy = updatedBy; // Set who updated the record

			// Save the updated clinic entity
			const updatedClinic = await this.clinicRepository.save(clinic);
			this.logger.log('Client booking settings updated successfully', {
				clinicId
			});
			return updatedClinic;
		} catch (error) {
			this.logger.error('Error updating client booking settings', {
				clinicId,
				dto,
				error
			});
			if (error instanceof NotFoundException) {
				throw error;
			}
			// Consider specific error handling for JSON serialization if needed, though TypeORM handles JSONB well
			throw new InternalServerErrorException(
				'Failed to update client booking settings'
			);
		}
	}

	// Method to get clinic settings
	async getClinicSettings(
		clinicId: string
	): Promise<ClinicSettingsResponseDto> {
		this.logger.log('Fetching clinic settings', { clinicId });
		try {
			const clinic = await this.clinicRepository.findOne({
				where: { id: clinicId }
			});

			if (!clinic) {
				this.logger.warn('Clinic not found for settings retrieval', {
					clinicId
				});
				throw new NotFoundException(
					`Clinic with ID "${clinicId}" not found`
				);
			}

			// Get the settings from the customRule JSONB field
			const customRule = clinic.customRule || {};

			// Return settings with defaults if not set
			return {
				patientLastNameAsOwnerLastName:
					customRule.patientLastNameAsOwnerLastName ?? false,
				defaultPatientList: customRule.defaultPatientList ?? 'all',
				appointmentBookingList:
					customRule.appointmentBookingList ?? 'alive'
			};
		} catch (error) {
			this.logger.error('Error fetching clinic settings', {
				clinicId,
				error
			});
			if (error instanceof NotFoundException) {
				throw error;
			}
			throw new InternalServerErrorException(
				'Failed to fetch clinic settings'
			);
		}
	}

	// Method to update clinic settings
	async updateClinicSettings(
		clinicId: string,
		settingsDto: ClinicSettingsDto,
		updatedBy: string
	): Promise<ClinicSettingsResponseDto> {
		this.logger.log('Updating clinic settings', {
			clinicId,
			settingsDto,
			updatedBy
		});
		try {
			const clinic = await this.clinicRepository.findOne({
				where: { id: clinicId }
			});

			if (!clinic) {
				this.logger.warn('Clinic not found for settings update', {
					clinicId
				});
				throw new NotFoundException(
					`Clinic with ID "${clinicId}" not found`
				);
			}

			// Get existing customRule or initialize with default structure
			const currentCustomRule = clinic.customRule || {
				patientLastNameAsOwnerLastName: false,
				defaultPatientList: 'all',
				appointmentBookingList: 'alive'
			};

			// Update only the provided settings
			if (settingsDto.patientLastNameAsOwnerLastName !== undefined) {
				currentCustomRule.patientLastNameAsOwnerLastName =
					settingsDto.patientLastNameAsOwnerLastName;
			}
			if (settingsDto.defaultPatientList !== undefined) {
				currentCustomRule.defaultPatientList =
					settingsDto.defaultPatientList;
			}
			if (settingsDto.appointmentBookingList !== undefined) {
				currentCustomRule.appointmentBookingList =
					settingsDto.appointmentBookingList;
			}

			// Update the customRule object
			clinic.customRule = currentCustomRule;
			clinic.updatedBy = updatedBy;

			// Save the updated clinic entity
			await this.clinicRepository.save(clinic);
			this.logger.log('Clinic settings updated successfully', {
				clinicId
			});

			// Return the updated settings with defaults
			return {
				patientLastNameAsOwnerLastName:
					currentCustomRule.patientLastNameAsOwnerLastName ?? false,
				defaultPatientList:
					currentCustomRule.defaultPatientList ?? 'all',
				appointmentBookingList:
					currentCustomRule.appointmentBookingList ?? 'alive'
			};
		} catch (error) {
			this.logger.error('Error updating clinic settings', {
				clinicId,
				settingsDto,
				error
			});
			if (error instanceof NotFoundException) {
				throw error;
			}
			throw new InternalServerErrorException(
				'Failed to update clinic settings'
			);
		}
	}
}

import * as HttpService from './http.service';
import { CREATE_BRAND, UPDATE_BRAND, GET_BRAND, GET_BRAND_BY_SLUG, GET_BRANDS } from './url.service';

export const createBrand = (brandName: string) => {
    const payload = { name: brandName };
    return HttpService.postWithAuth(CREATE_BRAND(), payload);
};

export const updateBrand = (id: string, brandName: string) => {
    const payload = { name: brandName };
    return HttpService.putWithAuth(UPDATE_BRAND(id), payload);
};

export const getBrands = (
    page: number = 1,
    limit: number = 10,
    orderBy: string = 'DESC',
    search: string = ''
) => {
    return HttpService.getWithAuth(GET_BRANDS(page, limit, orderBy, search));
};

// Dedicated function for brand search in dropdowns
export const searchBrands = (
    search: string = '',
    limit: number = 20
) => {
    return HttpService.getWithAuth(GET_BRANDS(1, limit, 'DESC', search));
};

// Backward compatibility method for non-paginated calls
export const getBrandsSimple = () => {
    return HttpService.getWithAuth(GET_BRANDS());
};

export const getBrand = (id: string) => {
    return HttpService.getWithOutAuth(GET_BRAND(id));
};

export const getBrandBySlug = (slug: string) => {
    return HttpService.getWithOutAuth(GET_BRAND_BY_SLUG(slug));
};

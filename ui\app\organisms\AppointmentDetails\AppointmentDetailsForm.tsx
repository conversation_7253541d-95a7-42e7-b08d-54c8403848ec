import { useUpdateAppointmentDetailsMutation } from '../../services/appointment.queries';
import { Heading } from '@/app/atoms';
import RenderFields, { fieldsType } from '@/app/molecules/RenderFields';
import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import AppointmentObjectiveVitals from './AppointmentObjectiveVitals';
import AppointmentObjectivePhysicalExams from './AppointmentObjectivePhysicalExams';
import AppointmentObjectiveLabReports from './AppointmentObjectiveLabReports';
import AppointmentAssessment from './AppointmentAssessment';
import AppointmentObjectiveBodyMap from './AppointmentObjectiveBodyMap';
import { removeDuplicatePaths } from '@/app/utils/common';
import AppointmentPlan from './AppointmentPlan';
import AppointmentPrescription from './AppointmentPrescription';
import { useEffect, useState } from 'react';
import AppointmentFollowup from './AppointmentFollowup';
import AppointmentAttachements from './AppointmentAttachements';

interface AppointmentDetailsFormProps {
    appoinmentDetails: {
        assessment: any;
        plans: any;
        prescription: any;
        subjective: any;
        objective: any;
        followup: any;
        attachments: any;
    };
    longTermMedications: any;
    appointmentId: string;
    patientId: string;
}

const AppointmentDetailsForm: React.FC<AppointmentDetailsFormProps> = ({
    appoinmentDetails,
    appointmentId,
    patientId,
    longTermMedications,
}) => {
    // const subjective = appoinmentDetails?.subjective ?? null;
    // const objective = appoinmentDetails?.objective ?? null;
    // const assessment = appoinmentDetails?.assessment ?? null;
    // const plans = appoinmentDetails?.plans ?? null;
    // const prescription = appoinmentDetails?.prescription ?? null;
    const [appointmentData, setAppointmentData] = useState(appoinmentDetails);

    const validationSchema = yup.object().shape({
        subjective: yup
            .string()
            .required('Subjective is required')
            .max(500, 'subjective must not exceed 500 characters'),
        objective: yup.object().shape({
            physicalExam: yup.object().shape({
                oralTeeth: yup.object().shape({
                    status: yup.string().default('Not Captured'),
                    text: yup
                        .string()
                        .max(
                            50,
                            'oralTeeth - text should not exceed 50 characters'
                        ),
                }),
                eyes: yup.object().shape({
                    status: yup.string().default('Not Captured'),
                    text: yup
                        .string()
                        .max(50, 'eyes - text should not exceed 50 characters'),
                }),
                throat: yup.object().shape({
                    status: yup.string().default('Not Captured'),
                    text: yup
                        .string()
                        .max(
                            50,
                            'throat - text should not exceed 50 characters'
                        ),
                }),
                respiratory: yup.object().shape({
                    status: yup.string().default('Not Captured'),
                    text: yup
                        .string()
                        .max(
                            50,
                            'respiratory - text should not exceed 50 characters'
                        ),
                }),
                musculoskeletal: yup.object().shape({
                    status: yup.string().default('Not Captured'),
                    text: yup
                        .string()
                        .max(
                            50,
                            'musculoskeletal - text should not exceed 50 characters'
                        ),
                }),
                urogenital: yup.object().shape({
                    status: yup.string().default('Not Captured'),
                    text: yup
                        .string()
                        .max(
                            50,
                            'urogenital - text should not exceed 50 characters'
                        ),
                }),
                mucous_membranes: yup.object().shape({
                    status: yup.string().default('Not Captured'),
                    text: yup
                        .string()
                        .max(
                            50,
                            'mucous_membranes - text should not exceed 50 characters'
                        ),
                }),
                ears: yup.object().shape({
                    status: yup.string().default('Not Captured'),
                    text: yup
                        .string()
                        .max(50, 'ears - text should not exceed 50 characters'),
                }),
                cardiovascular: yup.object().shape({
                    status: yup.string().default('Not Captured'),
                    text: yup
                        .string()
                        .max(
                            50,
                            'cardiovascular - text should not exceed 50 characters'
                        ),
                }),
                abdomen: yup.object().shape({
                    status: yup.string().default('Not Captured'),
                    text: yup
                        .string()
                        .max(
                            50,
                            'abdomen - text should not exceed 50 characters'
                        ),
                }),
                glands: yup.object().shape({
                    status: yup.string().default('Not Captured'),
                    text: yup
                        .string()
                        .max(
                            50,
                            'glands - text should not exceed 50 characters'
                        ),
                }),
                rectal: yup.object().shape({
                    status: yup.string().default('Not Captured'),
                    text: yup
                        .string()
                        .max(
                            50,
                            'rectal - text should not exceed 50 characters'
                        ),
                }),
            }),
            notes: yup
                .string()
                .max(200, 'notes - text should not exceed 200 characters'),
            vitals: yup.array(
                yup.object().shape({
                    time: yup.string(),
                    weight: yup.string(),
                    temperature: yup.string(),
                    heartRate: yup.string(),
                    respRate: yup.string(),
                    attitude: yup.string(),
                    painScore: yup.string(),
                    mucousMembrane: yup.string(),
                    capillaryRefill: yup.string(),
                    hydrationStatus: yup.string(),
                    bcs: yup.string(),
                    bp: yup.string(),
                    map: yup.string(),
                })
            ),
            labReports: yup.array(
                yup.object().shape({
                    value: yup.string(),
                    label: yup.string(),
                    cartId: yup.string(),
                })
            ),
        }),
        assessment: yup.object().shape({
            list: yup.array(
                yup.object().shape({
                    value: yup.string(),
                    label: yup.string(),
                })
            ),
            notes: yup
                .string()
                .max(
                    500,
                    'assessment notes - text should not exceed 500 characters'
                ),
            bodyMaps: yup.array(
                yup.object().shape({
                    type: yup.object().shape({
                        value: yup.string(),
                        label: yup.string(),
                    }),
                    notes: yup.string(),
                    bodymapImage: yup.string(),
                    paths: yup.string(),
                    refs: yup.object(),
                    image: yup.string(),
                })
            ),
        }),
        plans: yup.object().shape({
            list: yup.array(
                yup.object().shape({
                    value: yup.string(),
                    label: yup.string(),
                    // dosage: yup.string(),
                    // frequency: yup.string(),
                    // duration: yup.string(),
                    quantity: yup.number(),
                    cartId: yup.string(),
                })
            ),
            notes: yup
                .string()
                .max(
                    500,
                    'plans notes - text should not exceed 500 characters'
                ),
        }),
        prescription: yup.object().shape({
            list: yup.array(
                yup.object().shape({
                    value: yup.string(),
                    label: yup.string(),
                    comment: yup.string(),
                    isRestricted: yup.boolean(),
                    isLongTerm: yup.boolean(),
                    cartId: yup.string(),
                })
            ),
            notes: yup
                .string()
                .max(
                    200,
                    'prescription notes - text should not exceed 200 characters'
                ),
        }),
        followup: yup.object().shape({
            label: yup.string(),
            type: yup.string(),
        }),
        attachments: yup.object().shape({
            list: yup.array(
                yup.object().shape({
                    attachementName: yup.string(),
                    fileName: yup.string(),
                    fileKey: yup.string(),
                })
            ),
        }),
    });

    const getDisplayName = (longTermMedicationItem: any) => {
        const { drug, name, form, strength, unit } =
            longTermMedicationItem.medication;

        const displayName =
            (drug ? drug + ', ' : '') +
            (name ? name + ', ' : '') +
            (form ? form + ', ' : '') +
            (strength ? strength + ' ' : '') +
            (unit ? unit : '');

        // Looks for a comma followed by any amount of whitespace at the end of the string and removes it.
        const formattedDisplayName = displayName.trim().replace(/,\s*$/, '');

        return formattedDisplayName;
    };

    const checkAndUpdatePrescription = (
        appointmentData: AppointmentDetailsFormProps['appoinmentDetails'],
        longTermMedications: AppointmentDetailsFormProps['longTermMedications']
    ) => {
        const updatedPrescriptionList = [
            ...(appointmentData?.prescription?.list || []),
        ];

        longTermMedications?.data?.forEach((longTermMedicationItem: any) => {
            const matchingMedication = updatedPrescriptionList.find(
                (med: any) => med.id === longTermMedicationItem.id
            );

            if (
                matchingMedication == false ||
                matchingMedication != undefined
            ) {
                // Update the existing medication item
                matchingMedication.isLongTerm = true;
            } else {
                // Add a new medication item to the list
                const newElement = {
                    value: longTermMedicationItem.medication.id,
                    label: longTermMedicationItem.medication.name,
                    isRestricted:
                        longTermMedicationItem.medication.isRestricted,
                    displayName: getDisplayName(longTermMedicationItem),
                    isLongTerm: true,
                    comment: '',
                };
                updatedPrescriptionList.push(newElement);
            }
        });

        // Update appointmentDetails with the modified prescription list
        const updatedAppointmentDetails = {
            ...appointmentData,
            prescription: {
                list: updatedPrescriptionList,
            },
        };

        return updatedAppointmentDetails.prescription;
    };

    const {
        register,
        getValues,
        setValue,
        control,
        watch,
        reset,
        formState: { errors },
    } = useForm({
        resolver: yupResolver(validationSchema),
        defaultValues: {
            subjective: appointmentData?.subjective,
            objective: appointmentData?.objective
                ? appointmentData?.objective
                : {
                      physicalExam: {
                          oralTeeth: {
                              status: 'Not Captured',
                          },
                          eyes: {
                              status: 'Not Captured',
                          },
                          throat: {
                              status: 'Not Captured',
                          },
                          respiratory: {
                              status: 'Not Captured',
                          },
                          musculoskeletal: {
                              status: 'Not Captured',
                          },
                          urogenital: {
                              status: 'Not Captured',
                          },
                          mucous_membranes: {
                              status: 'Not Captured',
                          },
                          ears: {
                              status: 'Not Captured',
                          },
                          cardiovascular: {
                              status: 'Not Captured',
                          },
                          abdomen: {
                              status: 'Not Captured',
                          },
                          glands: {
                              status: 'Not Captured',
                          },
                          rectal: {
                              status: 'Not Captured',
                          },
                      },
                      vitals: [
                          {
                              time: new Date(),
                          },
                      ],
                      labReports: [],
                      bodyMaps: [
                          {
                              type: null,
                              bodymapImage: null,
                              notes: null,
                              paths: null,
                              image: null,
                          },
                      ],
                  },
            plans: appointmentData?.plans,
            assessment: appointmentData?.assessment,
            prescription: checkAndUpdatePrescription(
                appointmentData,
                longTermMedications
            ),
            followup: appointmentData?.followup,
            // prescription: appointmentData?.prescription,
            attachments: appoinmentDetails?.attachments,
        },
        mode: 'onChange', // This will trigger validation on change
    });

    useEffect(() => {
        if (appoinmentDetails) {
            setAppointmentData(appoinmentDetails);
            reset(appoinmentDetails);
        }
    }, [appoinmentDetails]);

    const { updateAppointmentDetailsMutation } =
        useUpdateAppointmentDetailsMutation(patientId);

    const fields: fieldsType[] = [
        {
            id: 'subjective',
            name: 'subjective',
            label: 'Subjective',
            placeholder: '',
            type: 'textarea',
            fieldSize: 'large',
            rows: 10,
            required: false,
            disabled: false,
            value: getValues('subjective'),
            defaultValue: getValues('subjective'),
            onChange: (change: any) => {
                change &&
                    setValue('subjective', change.target.value, {
                        shouldValidate: true,
                    });
                localStorage.setItem(
                    appointmentId,
                    JSON.stringify(getValues())
                );
            },
            onBlur: (data: any) => {},
        },
    ];

    const objectiveNotesFields: fieldsType[] = [
        {
            id: 'objective_notes',
            name: 'objective_notes',
            label: 'Objective notes',
            placeholder: '',
            type: 'textarea',
            fieldSize: 'large',
            rows: 5,
            required: false,
            disabled: false,
            value: getValues('objective.notes'),
            defaultValue: getValues('objective.notes'),
            onChange: (change: any) => {
                change &&
                    setValue('objective.notes', change.target.value, {
                        shouldValidate: true,
                    });
            },
            onBlur: (data: any) => {
                saveData();
            },
        },
    ];

    const handleBlur = () => {
        // localStorage.setItem(appointmentId, JSON.stringify(getValues()));
        saveData();
    };

    const saveData = () => {
        localStorage.setItem(appointmentId, JSON.stringify(getValues()));
    };

    return (
        <div>
            <RenderFields
                control={control}
                errors={
                    errors as {
                        [key: string]: { message: string };
                    }
                }
                fields={fields}
                setValue={setValue}
                watch={watch}
            />
            <div className="pt-4 pb-2">
                <Heading type="h6">Objective</Heading>
            </div>
            <AppointmentObjectiveVitals
                getValues={getValues}
                setValue={setValue}
                register={register}
                handleBlur={handleBlur}
            />
            <AppointmentObjectivePhysicalExams
                getValues={getValues}
                setValue={setValue}
                register={register}
                handleBlur={handleBlur}
            />
            <AppointmentObjectiveBodyMap
                getValues={getValues}
                setValue={setValue}
                register={register}
                handleBlur={handleBlur}
                appointmentId={appointmentId}
            />
            <div className="pt-6">
                <RenderFields
                    control={control}
                    errors={
                        errors as {
                            [key: string]: { message: string };
                        }
                    }
                    fields={objectiveNotesFields}
                    setValue={setValue}
                    watch={watch}
                />
            </div>

            <AppointmentObjectiveLabReports
                getValues={getValues}
                setValue={setValue}
                watch={watch}
                saveData={saveData}
                appointmentId={appointmentId}
                patientId={patientId}
            />

            <AppointmentAssessment
                getValues={getValues}
                setValue={setValue}
                watch={watch}
                control={control}
                saveData={saveData}
            />

            <AppointmentPlan
                getValues={getValues}
                setValue={setValue}
                watch={watch}
                saveData={saveData}
                control={control}
                handleBlur={handleBlur}
                appointmentId={appointmentId}
            />

            <AppointmentPrescription
                getValues={getValues}
                setValue={setValue}
                watch={watch}
                saveData={saveData}
                control={control}
                handleBlur={handleBlur}
                patientId={patientId}
                appointmentId={appointmentId}
            />

            <AppointmentFollowup
                getValues={getValues}
                setValue={setValue}
                watch={watch}
                control={control}
                saveData={saveData}
                handleBlur={handleBlur}
            />

            <AppointmentAttachements
                getValues={getValues}
                setValue={setValue}
                saveData={saveData}
            ></AppointmentAttachements>
        </div>
    );
};

export default AppointmentDetailsForm;

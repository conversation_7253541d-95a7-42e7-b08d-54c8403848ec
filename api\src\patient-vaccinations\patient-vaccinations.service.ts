import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { PatientVaccination } from './entities/patient-vaccinations.entity';
import { Repository, IsNull, DataSource, EntityManager } from 'typeorm';
import { CreatePatientVaccinationDto } from './dto/create-patient-vaccination.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { UpdatePatientVaccinationDto } from './dto/update-patient-vaccination.dto';
import { S3Service } from '../utils/aws/s3/s3.service';

@Injectable()
export class PatientVaccinationsService {
	constructor(
		@InjectRepository(PatientVaccination)
		private readonly patientVaccinationRepository: Repository<PatientVaccination>,
		private readonly logger: WinstonLogger,
		private readonly s3Service: S3Service,
		private readonly dataSource: DataSource
	) { }

	async create(
		createPatientVaccinationDto: CreatePatientVaccinationDto
	): Promise<CreatePatientVaccinationDto> {
		this.logger.log(
			'calling createPatientVaccinationService',
			createPatientVaccinationDto
		);

		this.logger.log(
			'changed the time for dto file',
			createPatientVaccinationDto
		);
		return this.patientVaccinationRepository.save(
			createPatientVaccinationDto
		);
	}

	async get(patientId: string): Promise<PatientVaccination[]> {
		return this.patientVaccinationRepository.find({
			where: {
				patientId
			},
			order: {
				vaccinationDate: 'DESC',
				createdAt: 'DESC'
			}
		});
	}

	async update(
		id: string,
		updatePatientVaccination: UpdatePatientVaccinationDto
	) {
		const patientVaccination =
			await this.patientVaccinationRepository.findOne({
				where: {
					id,
					deletedAt: IsNull()
				}
			});

		if (!patientVaccination) {
			throw new NotFoundException(
				`Patient Vaccination with ID "${id}" not found`
			);
		}
		const updatedPatientVaccination = {
			...patientVaccination,
			...updatePatientVaccination
		};

		return await this.patientVaccinationRepository.save(
			updatedPatientVaccination
		);
	}

	/**
	 * Soft delete patient vaccinations by appointment ID
	 * Used when an appointment is deleted
	 * Handles S3 deletion after database transaction to avoid race conditions
	 */
	async softDeleteByAppointmentId(
		appointmentId: string,
		entityManager?: EntityManager
	): Promise<void> {
		if (entityManager) {
			// Use existing transaction - only do database operations
			await this.softDeleteInTransaction(appointmentId, entityManager);
		} else {
			// Create new transaction and handle S3 deletion after commit
			let s3FilesToDelete: string[] = [];

			await this.dataSource.transaction(async manager => {
				s3FilesToDelete = await this.softDeleteInTransaction(
					appointmentId,
					manager
				);
			});

			// Delete S3 files after transaction commits successfully
			if (s3FilesToDelete.length > 0) {
				// Use setImmediate to ensure this runs after transaction commit
				setImmediate(() => {
					this.deleteS3FilesAsync(appointmentId, s3FilesToDelete);
				});
			}
		}
	}

	/**
	 * Internal method to handle soft delete within a transaction
	 * Returns S3 file keys that need to be deleted after transaction commits
	 */
	private async softDeleteInTransaction(
		appointmentId: string,
		entityManager: EntityManager
	): Promise<string[]> {
		try {
			const repository = entityManager.getRepository(PatientVaccination);

			// First, find all vaccinations to get their S3 file keys
			const vaccinations = await repository.find({
				where: { appointmentId, deletedAt: IsNull() }
			});

			this.logger.log('Found vaccinations to delete', {
				appointmentId,
				count: vaccinations.length
			});

			if (vaccinations.length === 0) {
				this.logger.log('No vaccinations found to delete', {
					appointmentId
				});
				return [];
			}

			// Collect S3 file keys before deletion
			const s3FilesToDelete = vaccinations
				.filter(v => v.reportUrl)
				.map(v => v.reportUrl!);

			// Perform soft delete in database
			await repository.softDelete({ appointmentId });

			this.logger.log('Soft deleted patient vaccinations in database', {
				appointmentId,
				deletedCount: vaccinations.length,
				s3FilesCount: s3FilesToDelete.length
			});

			return s3FilesToDelete;
		} catch (error) {
			this.logger.error('Error soft deleting patient vaccinations', {
				appointmentId,
				error: error instanceof Error ? error.message : String(error)
			});
			throw error;
		}
	}

	/**
	 * Asynchronously delete S3 files after transaction commits
	 */
	private async deleteS3FilesAsync(
		appointmentId: string,
		filesToDelete: string[]
	): Promise<void> {
		this.logger.log('Starting async S3 file deletion', {
			appointmentId,
			fileCount: filesToDelete.length
		});

		for (const fileKey of filesToDelete) {
			try {
				await this.s3Service.deleteFile(fileKey);
				this.logger.log('Deleted S3 file for vaccination', {
					appointmentId,
					fileKey
				});
			} catch (s3Error) {
				this.logger.error('Failed to delete S3 file for vaccination', {
					appointmentId,
					fileKey,
					error:
						s3Error instanceof Error
							? s3Error.message
							: String(s3Error)
				});
				// Continue with other files even if one fails
			}
		}

		this.logger.log('Completed async S3 file deletion', {
			appointmentId,
			fileCount: filesToDelete.length
		});
	}

	/**
	 * Soft delete patient vaccinations by appointment ID and vaccination ID
	 * Used when specific vaccination items are removed from an invoice
	 * Handles S3 deletion after database transaction to avoid race conditions
	 */
	async softDeleteByAppointmentAndVaccinationId(
		appointmentId: string,
		vaccinationId: string,
		entityManager?: EntityManager
	): Promise<void> {
		if (entityManager) {
			// Use existing transaction - only do database operations
			await this.softDeleteSpecificInTransaction(
				appointmentId,
				vaccinationId,
				entityManager
			);
		} else {
			// Create new transaction and handle S3 deletion after commit
			let s3FilesToDelete: string[] = [];

			await this.dataSource.transaction(async manager => {
				s3FilesToDelete = await this.softDeleteSpecificInTransaction(
					appointmentId,
					vaccinationId,
					manager
				);
			});

			// Delete S3 files after transaction commits successfully
			if (s3FilesToDelete.length > 0) {
				// Use setImmediate to ensure this runs after transaction commit
				setImmediate(() => {
					this.deleteS3FilesAsync(appointmentId, s3FilesToDelete);
				});
			}
		}
	}

	/**
	 * Internal method to handle specific vaccination soft delete within a transaction
	 * Returns S3 file keys that need to be deleted after transaction commits
	 */
	private async softDeleteSpecificInTransaction(
		appointmentId: string,
		vaccinationId: string,
		entityManager: EntityManager
	): Promise<string[]> {
		try {
			const repository = entityManager.getRepository(PatientVaccination);

			// First, find all vaccinations to get their S3 file keys
			const vaccinations = await repository.find({
				where: {
					appointmentId,
					vaccinationId,
					deletedAt: IsNull()
				}
			});

			this.logger.log('Found specific vaccinations to delete', {
				appointmentId,
				vaccinationId,
				count: vaccinations.length
			});

			if (vaccinations.length === 0) {
				this.logger.log('No specific vaccinations found to delete', {
					appointmentId,
					vaccinationId
				});
				return [];
			}

			// Collect S3 file keys before deletion
			const s3FilesToDelete = vaccinations
				.filter(v => v.reportUrl)
				.map(v => v.reportUrl!);

			// Perform soft delete in database
			await repository.softDelete({
				appointmentId,
				vaccinationId
			});

			this.logger.log(
				'Soft deleted specific patient vaccinations in database',
				{
					appointmentId,
					vaccinationId,
					deletedCount: vaccinations.length,
					s3FilesCount: s3FilesToDelete.length
				}
			);

			return s3FilesToDelete;
		} catch (error) {
			this.logger.error(
				'Error soft deleting specific patient vaccinations',
				{
					appointmentId,
					vaccinationId,
					error:
						error instanceof Error ? error.message : String(error)
				}
			);
			throw error;
		}
	}

	/**
	 * Mark a specific patient vaccination as removed from invoice by patient vaccination ID
	 * Used when removing vaccination items from invoice using metadata mapping
	 */
	async markAsRemovedFromInvoiceByPatientVaccinationId(
		patientVaccinationId: string,
		entityManager?: EntityManager
	): Promise<void> {
		try {
			const manager = entityManager || this.dataSource.manager;

			// Find the specific vaccination to mark as removed
			const vaccination = await manager.findOne(PatientVaccination, {
				where: {
					id: patientVaccinationId,
					removedFromInvoice: false,
					deletedAt: IsNull()
				}
			});

			this.logger.log(
				'Found patient vaccination to mark as removed from invoice',
				{
					patientVaccinationId,
					found: !!vaccination,
					vaccineName: vaccination?.vaccineName
				}
			);

			if (!vaccination) {
				this.logger.log(
					'No patient vaccination found to mark as removed',
					{
						patientVaccinationId
					}
				);
				return;
			}

			// Mark as removed from invoice
			await manager.update(
				PatientVaccination,
				{
					id: patientVaccinationId
				},
				{
					removedFromInvoice: true
				}
			);

			this.logger.log(
				'Marked patient vaccination as removed from invoice',
				{
					patientVaccinationId,
					vaccineName: vaccination.vaccineName
				}
			);
		} catch (error) {
			this.logger.error(
				'Error marking patient vaccination as removed from invoice',
				{
					patientVaccinationId,
					error:
						error instanceof Error ? error.message : String(error)
				}
			);
			throw error;
		}
	}
}

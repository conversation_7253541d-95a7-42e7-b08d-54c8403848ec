import { createReadStream } from 'fs';
import { parse } from 'csv-parse';
import axios from 'axios';
import moment from 'moment';

const API_BASE_URL = 'http://localhost:8000/api/data-migration';

const escapeHtml = str => {
	return str
		.toString()
		.replace(/&/g, '&amp;')
		.replace(/</g, '&lt;')
		.replace(/>/g, '&gt;')
		.replace(/"/g, '&quot;')
		.replace(/'/g, '&#39;')
		.replace(/\n/g, ' ') // Replace newlines with spaces
		.replace(/\s+/g, ' '); // Normalize whitespace
};

const extractDoctors = entry => {
	const doctors = [];

	// Extract all segments containing A: or C:
	entry.split('|').forEach(segment => {
		if (segment.includes('A:')) {
			const doctorName = segment.split('A:')[1].trim();
			if (doctorName) doctors.push(doctorName);
		}
		if (segment.includes('C:')) {
			const doctorName = segment.split('C:')[1].trim();
			if (doctorName) doctors.push(doctorName);
		}
	});

	return doctors.join(', ');
};

const parseTreatmentData = data => {
	// Split into individual treatment entries and filter empty ones
	const treatmentEntries = data.split('|').filter(Boolean);
	const rows = [];

	// Process each entry
	treatmentEntries.forEach(entry => {
		const entryStr = entry.trim();

		// Only process entries that start with a number (treatments)
		if (/^\d+\.?\d*/.test(entryStr)) {
			// Extract quantity (always at the start)
			const quantityMatch = entryStr.match(/^\d+\.?\d*/);
			const quantity = quantityMatch ? quantityMatch[0] : '';

			// Extract treatment name (between quantity and date)
			let remainingText = entryStr.slice(quantity.length).trim();
			const dateIndex = remainingText.search(
				/(Mon|Tue|Wed|Thu|Fri|Sat|Sun),/
			);
			const treatmentName =
				dateIndex > -1
					? remainingText.slice(0, dateIndex).trim()
					: remainingText.trim();

			// Extract doctors (look for A: and C: patterns)
			const doctors = extractDoctors(
				entry +
					'|' +
					(treatmentEntries[treatmentEntries.indexOf(entry) + 1] ||
						'') +
					'|' +
					(treatmentEntries[treatmentEntries.indexOf(entry) + 2] ||
						'')
			);

			rows.push({
				Treatment: treatmentName,
				Quantity: quantity,
				Doctors: doctors
			});
		}
	});

	return rows;
};

const formatAsHtmlTable = data => {
	const parsedData = parseTreatmentData(data);
	console.log(parsedData);

	const tableContent =
		'<table class="min-w-full bg-white border-collapse shadow-sm rounded-lg overflow-hidden">' +
		'<thead>' +
		'<tr class="bg-gray-50 border-b border-gray-200">' +
		'<th class="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-x border-gray-200">Treatment</th>' +
		'<th class="px-6 py-4 text-center text-sm font-semibold text-gray-900 border-x border-gray-200">Quantity</th>' +
		'<th class="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-x border-gray-200">Doctors</th>' +
		'</tr>' +
		'</thead>' +
		'<tbody>' +
		parsedData
			.map(
				(row, index) =>
					'<tr class="' +
					(index % 2 === 0 ? 'bg-white' : 'bg-gray-50') +
					' hover:bg-gray-50">' +
					'<td class="px-6 py-4 text-sm text-gray-900 border-x border-b border-gray-200">' +
					escapeHtml(row.Treatment) +
					'</td>' +
					'<td class="px-6 py-4 text-sm text-gray-900 text-center border-x border-b border-gray-200">' +
					escapeHtml(row.Quantity) +
					'</td>' +
					'<td class="px-6 py-4 text-sm text-gray-900 border-x border-b border-gray-200">' +
					escapeHtml(row.Doctors) +
					'</td>' +
					'</tr>'
			)
			.join('') +
		'</tbody>' +
		'</table>';

	return tableContent;
};

const da =
	'1.00 Ultrasound - Follow Up Or Short Fri, 08 Dec 2023 12:35:00 | A: Nupura Apte | C: Daksh Mandot | 1.00 Idexx Chem 3 (Creat BUN ALT) Fri, 08 Dec 2023 11:28:18 | A: Nupura Apte | C: Daksh Mandot | 1.00 Idexx CBC Fri, 08 Dec 2023 11:28:18 | A: Nupura Apte | C: Daksh Mandot | 1.00 Synfosium Capsule Fri, 08 Dec 2023 12:35:09 | A: Nitin Dhandre | C: Nitin Dhandre | 1.00 SQ Fluids Administration Fri, 08 Dec 2023 12:34:46 | A: Nupura Apte | C: Daksh Mandot | 1.00 IV Fluid Administration Fri, 08 Dec 2023 17:22:03 | A: Nupura Apte | C: Daksh Mandot | 1.00 20 G Catheter Placement Fri, 08 Dec 2023 17:22:03 | A: Nupura Apte | C: Daksh Mandot';
const aa = formatAsHtmlTable(da);
console.log(aa);

async function readCSV(filePath) {
	const parser = createReadStream(filePath).pipe(
		parse({
			columns: true,
			skip_empty_lines: true
		})
	);

	const records = [];
	for await (const record of parser) {
		records.push(record);
	}
	return records;
}

function formatOwner(ownerData) {
	const addressParts = [
		ownerData['Address1'],
		ownerData['Address2'],
		ownerData['City'],
		ownerData['State'],
		ownerData['Zip']
	].filter(Boolean);

	const fullAddress = addressParts.join(', ');
	return {
		firstName: ownerData['First Name'],
		lastName: ownerData['Last Name'],
		phoneNumber:
			ownerData['Mobile Phone'] ||
			ownerData['Home Phone'] ||
			'**********', // dummy number
		email: ownerData['Email'],
		address: fullAddress.length > 0 ? fullAddress : undefined,
		countryCode: '91'
	};
}

function formatPatient(patientData, dummyPatientId) {
	const formatBreed = breed => {
		return breed.toLowerCase().replace(/\s+/g, '_');
	};

	const calculateAge = birthDate => {
		const [day, month, year] = birthDate.split('/').map(Number);
		const birth = new Date(year, month - 1, day); // month is 0-indexed in JS Date
		const today = new Date();
		let age = today.getFullYear() - birth.getFullYear();
		const monthDiff = today.getMonth() - birth.getMonth();

		if (
			monthDiff < 0 ||
			(monthDiff === 0 && today.getDate() < birth.getDate())
		) {
			age--;
		}

		return age;
	};

	return {
		patientName: patientData['Patient'],
		species: patientData['Species'].toLowerCase(),
		breed: formatBreed(patientData['Breed']),
		dateOfBirth: patientData['BirthDay'],
		age: calculateAge(patientData['BirthDay']),
		gender: mapGender(patientData['Gender']),
		weight: parseFloat(patientData['Weight']) || null,
		clinicId: '35a27e27-7b47-4386-b2f4-c5b947922c05',
		dummyData: { patientId: dummyPatientId }
	};
}

function mapGender(gender) {
	if (gender === 'Male' || gender === 'Male Castrate') return 'Male';
	if (gender === 'Female') return 'Female';
	return 'Unknown';
}

async function insertOwner(ownerData) {
	try {
		const response = await axios.post(`${API_BASE_URL}/owners`, ownerData);
		return response.data.id;
	} catch (error) {
		console.error('Error inserting owner:', error);
		throw error;
	}
}

async function insertPatient(patientData) {
	try {
		const response = await axios.post(
			`${API_BASE_URL}/patients`,
			patientData
		);
		return response.data.id;
	} catch (error) {
		console.error('Error inserting patient:', error);
		throw error;
	}
}

async function createPatientOwnerRelationship(patientId, ownerId) {
	try {
		console.log('Patient and Owners', patientId, ownerId);
		await axios.post(`${API_BASE_URL}/patient-owners`, {
			patientId,
			ownerId,
			isPrimary: true
		});
	} catch (error) {
		console.error('Error creating patient-owner relationship:', error);
		throw error;
	}
}

async function createAppointment(appointmentData) {
	try {
		const response = await axios.post(
			`${API_BASE_URL}/appointments`,
			appointmentData
		);
		return response.data.id;
	} catch (error) {
		console.error('Error creating appointment:', error);
		throw error;
	}
}

async function createAppointmentDetails(appointmentId, details) {
	try {
		console.log(details);
		await axios.post(`${API_BASE_URL}/appointment-details`, {
			appointmentId,
			details
		});
	} catch (error) {
		// console.error('Error creating appointment details:', error);
		throw error;
	}
}

function formatAppointmentDetails(appointmentData) {
	// Helper function to combine multiple fields into one string
	const combineFields = (data, fields) => {
		return fields
			.map(field => {
				const value = data[field];
				return value && value !== 'NA' ? `${field}: ${value}` : null;
			})
			.filter(Boolean)
			.join('\n');
	};

	// Helper function to handle "NA" values
	const handleNA = value => (value === 'NA' ? '' : value || '');

	// Format vitals
	const formatVitals = data => ({
		time: new Date().toLocaleTimeString('en-US', {
			hour: '2-digit',
			minute: '2-digit'
		}),
		weight: handleNA(data['Weight']),
		temperature: handleNA(data['Temperature']),
		heartRate: handleNA(data['Heart Rate']),
		respRate: handleNA(data['Respiratory Rate']),
		attitude: handleNA(data['Attitude']),
		painScore: handleNA(data['Pain Score']),
		mucousMembrane: handleNA(data['Mucous Membrane']),
		capillaryRefill: handleNA(data['Capillary Refill']),
		bcs: handleNA(data['BCS'])
	});

	// Predefined physical exam categories
	const physicalExamCategories = [
		{
			id: 'b697ca47-1bfd-4fd8-b074-be95a5b77203',
			category: 'Oral cavity/Teeth'
		},
		{ id: '2281db7b-ff5f-4393-8327-b3acd5b4e13e', category: 'Eyes/orbit' },
		{ id: '3305c387-ca98-463f-92c9-b2c12af7e0b8', category: 'Throat' },
		{ id: 'e6128510-6062-4469-a060-2c3db6cd0d60', category: 'Respiratory' },
		{
			id: '08c998cb-077e-4878-8a67-b4fc7be10288',
			category: 'Musculoskeletal'
		},
		{ id: '65614aec-6e21-4279-8119-4045ac415606', category: 'Urogenital' },
		{
			id: 'ab29000b-a953-4b11-98de-c3b9417e2246',
			category: 'Mucous membranes'
		},
		{ id: 'b908c810-f2d7-403e-95b8-e1bd9db58eb6', category: 'Ears' },
		{
			id: '5d34e610-3037-4105-a7da-b0e1ea8819eb',
			category: 'Cardio vascular'
		},
		{ id: 'db5c8112-4474-4b79-a959-56b6e209c0fa', category: 'Abdomen' },
		{
			id: '953de3f5-6096-4fa2-8b64-d127f07b2f37',
			category: 'Glands/Lymph Nodes'
		},
		{ id: 'd985a3c9-0072-4c78-ac21-a18f65291405', category: 'Rectal' }
	];

	const diagnostic_treatment_notes =
		handleNA(appointmentData['Diagnostic']) +
		handleNA(appointmentData['Treatment']);
	const planHTMLContent = formatAsHtmlTable(diagnostic_treatment_notes);
	const planNotes = handleNA(appointmentData['Plan']);

	return {
		invoiceAmount: handleNA(appointmentData['Invoice Amount']) || '0.00',
		memo: handleNA(appointmentData['Internal Memo']),
		subjective: combineFields(appointmentData, [
			'Subjective',
			'Message Board',
			'Abdominal Ultrasound'
		]),
		objective: {
			vitals: [formatVitals(appointmentData)],
			notes: handleNA(
				combineFields(appointmentData, [
					'Objective',
					'Objective (Attached)',
					'Idexx'
				])
			),
			physicalExam: physicalExamCategories.map(category => ({
				...category,
				status: handleNA(appointmentData[category.category]),
				notes: handleNA(appointmentData[`${category.category} Notes`])
			})),
			bodyMaps: [],
			labReports: []
		},
		assessment: {
			list: [],
			notes: handleNA(appointmentData['Assessment'])
		},
		plans: {
			list: [],
			notes: handleNA(planNotes),
			htmlContent: planHTMLContent
		},
		followup: null,
		prescription: {
			list: [],
			notes: handleNA(appointmentData['Prescription Notes'])
		},
		attachments: {
			list: []
		},
		isDataImported: true
	};
}

async function processAppointments(patientId, appointmentsData) {
	console.log('Inside Process app', patientId);
	const doctorId = '5aaee3e5-3513-4261-85e1-79fb16e3afb3';
	const clinicId = '35a27e27-7b47-4386-b2f4-c5b947922c05';
	for (const appointmentData of appointmentsData) {
		try {
			const cleanedDateString = appointmentData[
				'Appointment Date'
			].replace(/(\d+)(st|nd|rd|th)/, '$1');
			const appointmentDate = moment(
				cleanedDateString,
				['ddd DD MMM YYYY', 'DD MMM YYYY'],
				true
			);

			if (!appointmentDate.isValid()) {
				console.warn(
					`Skipping appointment with invalid date: ${appointmentData['Appointment Date']}`
				);
				continue;
			}
			// Format appointment data
			console.log(appointmentData['Appointment Date']);
			const formattedDate = appointmentDate.format('YYYY-MM-DD');
			const formattedAppointment = {
				clinicId: clinicId,
				doctorIds: [doctorId], // Assuming single doctor for now
				providerIds: [],
				patientId: patientId,
				date: formattedDate,
				startTime: '2024-10-17 17:00:00+05:30',
				endTime: '2024-10-17 17:30:00+05:30',
				reason: '',
				type: 'Consultation',
				status: 'Completed',
				isBlocked: false,
				weight: null
			};

			// Create appointment
			const appointmentId = await createAppointment(formattedAppointment);

			console.log('Appointment created', appointmentId);
			const appointmentDetails =
				formatAppointmentDetails(appointmentData);

			if (appointmentDetails) {
				console.log(
					`Appointment and details created/updated successfully for patient ${patientId} on ${formattedDate}`
				);
			} else {
				console.log(
					`Appointment created but details update failed for patient ${patientId} on ${formattedDate}`
				);
			}

			// // Create appointment details
			await createAppointmentDetails(appointmentId, appointmentDetails);

			console.log(
				`Appointment created successfully for patient ${patientId}`
			);
		} catch (error) {
			console.log('App eorr', error);
			console.error(
				`Error processing appointment for patient ${patientId}:`
			);
		}
	}
}

async function main() {
	try {
		const owners = await readCSV('owners.csv');
		const patients = await readCSV('patients.csv');
		const appointments = await readCSV('output.csv');

		console.log(
			`Loaded ${owners.length} owners, ${patients.length} patients, and ${appointments.length} appointments`
		);

		// await processAppointments(newPatientId, patientAppointments);
		const clientIdToOwnerId = new Map();
		const oldPatientIdToNewPatientId = new Map();

		// // Process owners
		for (const owner of owners) {
			const formattedOwner = formatOwner(owner);
			console.log(formattedOwner);
			try {
				const ownerId = await insertOwner(formattedOwner);
				clientIdToOwnerId.set(owner['ClientId'], ownerId);
				console.log(
					`Inserted owner: ${owner['F name']} ${owner['L name']}`
				);
			} catch (error) {
				console.error(
					`Error processing owner ${owner['ClientId']}:`,
					error
				);
			}
		}

		for (const patient of patients) {
			const formattedPatient = formatPatient(
				patient,
				patient['PatientId']
			);

			const ownerId = clientIdToOwnerId.get(patient['ClientId']);
			if (ownerId) {
				const formattedPatient = formatPatient(
					patient,
					patient['PatientId']
				);
				try {
					const newPatientId = await insertPatient(formattedPatient);
					await createPatientOwnerRelationship(newPatientId, ownerId);
					oldPatientIdToNewPatientId.set(
						patient['PatientId'],
						newPatientId
					);
					console.log(
						`Inserted patient: ${patient['Patient']} with new ID: ${newPatientId}`
					);

					// Process appointments for this patient
					const patientAppointments = appointments.filter(
						appt => appt['Patient ID'] === patient['PatientId']
					);
					console.log(newPatientId);
					await processAppointments(
						newPatientId,
						patientAppointments
					);
				} catch (error) {
					console.log(error);
					console.error(
						`Error processing patient ${patient['Patient']}:`,
						error
					);
				}
			} else {
				console.warn(
					`No owner found for patient ${patient['Patient']} with ClientID ${patient['ClientId']}`
				);
			}
		}
		console.log('Data migration completed successfully');
	} catch (error) {
		console.error('Error during data migration:', error);
	}
}

main().catch(console.error);

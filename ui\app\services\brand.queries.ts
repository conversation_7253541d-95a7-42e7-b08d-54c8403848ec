import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { createBrand, updateBrand, getBrand, getBrands, getBrandsSimple, searchBrands } from './brands.services';

export function useCreateBrand() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: createBrand,
        onSuccess: (data) => {
            if (data.status === true) {
                // Invalidate queries to reload the brands data
                queryClient.invalidateQueries({ queryKey: ['brands'] });
            }
        },
        onError: (error) => {
            console.error('Error creating brand:', error);
        },
    });
}

export function useUpdateBrand() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ id, name }: { id: string; name: string }) => updateBrand(id, name),
        onSuccess: (data) => {
            if (data.status === true) {
                // Invalidate queries to reload the brands data
                queryClient.invalidateQueries({ queryKey: ['brands'] });
                queryClient.invalidateQueries({ queryKey: ['brands-simple'] });
            }
        },
        onError: (error) => {
            console.error('Error updating brand:', error);
        },
    });
}


export function useGetAllBrands(
    page: number = 1,
    limit: number = 10,
    orderBy: string = 'DESC',
    search: string = ''
) {
    return useQuery({
        queryKey: ['brands', page, limit, orderBy, search],
        queryFn: () => getBrands(page, limit, orderBy, search),
    });
}

// Hook for brand search in dropdowns
export function useSearchBrands(search: string = '', enabled: boolean = true) {
    return useQuery({
        queryKey: ['brands-search', search],
        queryFn: () => searchBrands(search, 20),
        enabled: enabled,
        staleTime: 30000, // 30 seconds
    });
}

// Backward compatibility hook for non-paginated calls
export function useGetAllBrandsSimple() {
    return useQuery({
        queryKey: ['brands-simple'],
        queryFn: () => getBrandsSimple(),
    });
}

export function useGetBrand(id:string) {
    return useQuery({
        queryFn:()=> getBrand(id),
        queryKey: ['brands', id],
    });
}

import {
	Injectable,
	NotFoundException,
	ConflictException,
	InternalServerErrorException
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Not, Repository, DataSource } from 'typeorm';
import { DiagnosticTemplate } from './entities/diagnostic-template.entity';
import { CreateDiagnosticTemplateDto } from './dto/create-template.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import {
	CreateDiagnosticNoteDto,
	UpdateDiagnosticNoteDto
} from './dto/diagnostic-note.dto';
import { LabReport } from '../clinic-lab-report/entities/lab-report.entity';
import { ClinicLabReport } from '../clinic-lab-report/entities/clinic-lab-report.entity';
import { DiagnosticNote } from './entities/diagnostic-note.entity';
import { Patient } from '../patients/entities/patient.entity';
import { PatientsService } from '../patients/patients.service';
import moment = require('moment');
import { generatePDF } from '../utils/generatePdf';
import { uuidv4 } from 'uuidv7';
import { generateDiagnosticReportNote } from '../utils/pdfs/diagnosticNote';
import { generateDiagnosticReportTable } from '../utils/pdfs/diagnosticTable';
import { S3Service } from '../utils/aws/s3/s3.service';
import { generateUniqueCode } from '../utils/common/generate_alpha-numeric_code';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { AppointmentDetailsEntity } from '../appointments/entities/appointment-details.entity';
import { AppointmentGateway } from '../socket/socket.appointment.gateway';

@Injectable()
export class DiagnosticTemplatesService {
	constructor(
		@InjectRepository(DiagnosticTemplate)
		private readonly templateRepository: Repository<DiagnosticTemplate>,
		@InjectRepository(DiagnosticNote)
		private readonly diagnosticNoteRepository: Repository<DiagnosticNote>,
		@InjectRepository(LabReport)
		private readonly labReportRepository: Repository<LabReport>,
		@InjectRepository(ClinicLabReport)
		private readonly clinicLabReportRepository: Repository<ClinicLabReport>,
		@InjectRepository(AppointmentEntity)
		private readonly appointmentRepository: Repository<AppointmentEntity>,
		@InjectRepository(AppointmentDetailsEntity)
		private readonly appointmentDetailsRepository: Repository<AppointmentDetailsEntity>,
		private readonly logger: WinstonLogger,
		private readonly patientService: PatientsService,
		private readonly s3Service: S3Service,
		private readonly dataSource: DataSource,
		private readonly appointmentGateway: AppointmentGateway
	) {}

	async create(
		createDto: CreateDiagnosticTemplateDto,
		userId: string
	): Promise<DiagnosticTemplate> {
		try {
			// Check for existing template with same name in clinic
			const existing = await this.templateRepository.findOne({
				where: {
					clinicId: createDto.clinicId,
					templateName: createDto.templateName
				}
			});

			if (existing) {
				throw new ConflictException(
					'Template with this name already exists'
				);
			}

			const template = this.templateRepository.create({
				...createDto,
				createdBy: userId,
				updatedBy: userId
			});

			const savedTemplate = await this.templateRepository.save(template);
			this.logger.log('Template created successfully', {
				templateId: savedTemplate.id
			});
			return savedTemplate;
		} catch (error) {
			this.logger.error('Error creating template', {
				error,
				dto: createDto
			});
			throw error;
		}
	}

	async findAll(clinicId: string): Promise<DiagnosticTemplate[]> {
		try {
			return await this.templateRepository.find({
				where: { clinicId },
				order: { createdAt: 'DESC' }
			});
		} catch (error) {
			this.logger.error('Error fetching templates', { error, clinicId });
			throw error;
		}
	}

	async findOne(id: string, clinicId: string): Promise<DiagnosticTemplate> {
		const template = await this.templateRepository.findOne({
			where: { id, clinicId }
		});

		if (!template) {
			throw new NotFoundException('Template not found');
		}

		return template;
	}

	async update(
		id: string,
		updateDto: Partial<CreateDiagnosticTemplateDto>,
		userId: string,
		clinicId: string
	): Promise<DiagnosticTemplate> {
		const template = await this.findOne(id, clinicId);

		// Check name uniqueness if name is being updated
		if (
			updateDto.templateName &&
			updateDto.templateName !== template.templateName
		) {
			const existing = await this.templateRepository.findOne({
				where: {
					clinicId: updateDto.clinicId,
					templateName: updateDto.templateName,
					id: Not(id)
				}
			});

			if (existing) {
				throw new ConflictException(
					'Template with this name already exists'
				);
			}
		}

		Object.assign(template, {
			...updateDto,
			updatedBy: userId
		});

		return this.templateRepository.save(template);
	}

	async remove(id: string, clinicId: string): Promise<void> {
		// Start a transaction
		const queryRunner = this.dataSource.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			// Find the template
			const template = await this.templateRepository.findOne({
				where: { id, clinicId }
			});

			if (!template) {
				throw new NotFoundException('Template not found');
			}

			// Find all associated notes
			const associatedNotes = await this.diagnosticNoteRepository.find({
				where: { templateId: id }
			});

			// Delete all associated notes
			if (associatedNotes.length > 0) {
				await queryRunner.manager.remove(
					DiagnosticNote,
					associatedNotes
				);
				this.logger.log(
					`Deleted ${associatedNotes.length} associated notes for template ${id}`
				);
			}

			// Delete the template
			await queryRunner.manager.remove(DiagnosticTemplate, template);

			// Commit the transaction
			await queryRunner.commitTransaction();
			this.logger.log(
				'Template and associated notes deleted successfully',
				{ templateId: id }
			);
		} catch (error) {
			// Rollback transaction on error
			await queryRunner.rollbackTransaction();
			this.logger.error('Error deleting template and notes', {
				error,
				templateId: id
			});
			throw error;
		} finally {
			// Release the query runner
			await queryRunner.release();
		}
	}

	async createNote(createNoteDto: CreateDiagnosticNoteDto, userId: string) {
		const queryRunner = this.dataSource.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			const {
				labReportId,
				diagnosticId,
				clinicId,
				templateId,
				templateName,
				noteData
			} = createNoteDto;
			const labReport = await this.labReportRepository.findOne({
				where: { id: labReportId, clinicId },
				relations: ['clinicLabReport']
			});

			if (!labReport) {
				throw new NotFoundException('Lab report not found');
			}

			const diagnosticNumber = await generateUniqueCode(
				'diagnosticNumber',
				this.diagnosticNoteRepository
			);

			// Get complete data for PDF generation
			const completeNoteData = await this.getCompleteNoteData(
				templateName,
				noteData,
				labReport.patientId,
				diagnosticNumber,
				labReport.clinicLabReport.name,
				labReport.createdAt
			);

			// Generate and upload PDF
			const pdfFileKey = await this.generateAndUploadPDF(
				completeNoteData,
				noteData.values ? 'table' : 'notes'
			);

			// Create note entity
			const note = this.diagnosticNoteRepository.create({
				labReportId,
				clinicLabReportId: diagnosticId,
				appointmentId: labReport.appointmentId,
				clinicId,
				patientId: labReport.patientId,
				templateId,
				templateName,
				noteData,
				fileKey: pdfFileKey,
				fileName: `${templateName}_notes.pdf`,
				createdBy: userId,
				updatedBy: userId,
				diagnosticNumber
			});

			const savedNote = await queryRunner.manager.save(
				DiagnosticNote,
				note
			);
			await queryRunner.commitTransaction();

			// Update appointment details and broadcast the diagnostic note creation
			if (labReport.appointmentId) {
				await this.updateAppointmentDetailsAndBroadcastDiagnosticNotes(
					labReport.appointmentId,
					labReportId
				);
			}

			return savedNote;
		} catch (error) {
			await queryRunner.rollbackTransaction();
			console.log('Error creating diagnostic note', { error });
			this.logger.error('Error creating diagnostic note', { error });
			throw error;
		} finally {
			await queryRunner.release();
		}
	}

	async getTemplatesForLabReport(labReportId: string, clinicId: string) {
		const labReport = await this.clinicLabReportRepository.findOne({
			where: { id: labReportId, clinicId }
		});

		if (!labReport) {
			throw new NotFoundException('Lab report not found');
		}

		const templates = await this.templateRepository.find({
			where: {
				clinicId,
				isActive: true,
				assignedDiagnostics: {
					id: labReport.id
				}
			}
		});

		return templates;
	}

	async updateNote(
		id: string,
		updateNoteDto: UpdateDiagnosticNoteDto,
		userId: string
	) {
		const queryRunner = this.dataSource.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			const note = await this.findNoteForUpdate(id);
			await this.validateAndUpdateTemplate(note, updateNoteDto);
			await this.updateNoteDataAndPdf(note, updateNoteDto);
			this.updateNoteMetadata(note, userId);

			const updatedNote = await queryRunner.manager.save(
				DiagnosticNote,
				note
			);
			await queryRunner.commitTransaction();

			await this.handlePostUpdateActions(note, id);
			return updatedNote;
		} catch (error) {
			await queryRunner.rollbackTransaction();
			this.handleUpdateError(error, id, userId);
		} finally {
			await queryRunner.release();
		}
	}

	private async findNoteForUpdate(id: string): Promise<DiagnosticNote> {
		const note = await this.diagnosticNoteRepository.findOne({
			where: { id },
			relations: ['template']
		});

		if (!note) {
			throw new NotFoundException('Diagnostic note not found');
		}

		return note;
	}

	private async validateAndUpdateTemplate(
		note: DiagnosticNote,
		updateNoteDto: UpdateDiagnosticNoteDto
	): Promise<void> {
		if (
			!updateNoteDto.templateId ||
			updateNoteDto.templateId === note.templateId
		) {
			return;
		}

		const template = await this.templateRepository.findOne({
			where: {
				id: updateNoteDto.templateId,
				isActive: true
			}
		});

		if (!template) {
			throw new NotFoundException('Template not found or inactive');
		}

		note.templateId = template.id;
		if (updateNoteDto.templateName) {
			note.templateName = updateNoteDto.templateName;
		}
	}

	private async updateNoteDataAndPdf(
		note: DiagnosticNote,
		updateNoteDto: UpdateDiagnosticNoteDto
	): Promise<void> {
		if (!updateNoteDto.noteData) {
			return;
		}

		this.updateNoteDataByType(note, updateNoteDto);
		await this.regeneratePdfForNote(note, updateNoteDto);
	}

	private updateNoteDataByType(
		note: DiagnosticNote,
		updateNoteDto: UpdateDiagnosticNoteDto
	): void {
		if (updateNoteDto.templateType === 'notes') {
			note.noteData = {
				notes: updateNoteDto.noteData?.notes || '',
				values: {}
			};
		} else if (updateNoteDto.templateType === 'table') {
			note.noteData = {
				notes: '',
				values: updateNoteDto.noteData?.values || {}
			};
		}
	}

	private async regeneratePdfForNote(
		note: DiagnosticNote,
		updateNoteDto: UpdateDiagnosticNoteDto
	): Promise<void> {
		// Load the lab report to get diagnostic name and creation date (same as create method)
		const labReport = await this.labReportRepository.findOne({
			where: { id: note.labReportId },
			relations: ['clinicLabReport']
		});

		if (!labReport) {
			throw new NotFoundException('Lab report not found');
		}

		const completeNoteData = await this.getCompleteNoteData(
			updateNoteDto.templateName || note.templateName,
			note.noteData,
			note.patientId,
			note.diagnosticNumber,
			labReport.clinicLabReport.name,
			labReport.createdAt
		);

		const newPdfFileKey = await this.generateAndUploadPDF(
			completeNoteData,
			updateNoteDto.templateType === 'table' ? 'table' : 'notes'
		);

		await this.deleteOldPdfFile(note.fileKey);
		note.fileKey = newPdfFileKey;
		note.fileName = `${note.templateName}_notes.pdf`;
	}

	private async deleteOldPdfFile(fileKey: string | undefined): Promise<void> {
		if (!fileKey) {
			return;
		}

		try {
			await this.s3Service.deleteFile(fileKey);
		} catch (error) {
			this.logger.warn('Failed to delete old PDF file', {
				fileKey,
				error
			});
		}
	}

	private updateNoteMetadata(note: DiagnosticNote, userId: string): void {
		note.updatedBy = userId;
		note.version = (note.version || 0) + 1;
		note.updatedAt = new Date();
	}

	private async handlePostUpdateActions(
		note: DiagnosticNote,
		noteId: string
	): Promise<void> {
		if (note.appointmentId) {
			await this.updateAppointmentDetailsAndBroadcastDiagnosticNotes(
				note.appointmentId,
				note.labReportId
			);
		}

		this.logger.log('Note updated successfully', {
			noteId,
			version: note.version
		});
	}

	private handleUpdateError(
		error: any,
		noteId: string,
		userId: string
	): never {
		this.logger.error('Error updating diagnostic note', {
			error,
			noteId,
			userId
		});

		if (error instanceof NotFoundException) {
			throw error;
		}
		throw new InternalServerErrorException(
			'Failed to update diagnostic note'
		);
	}

	async findTemplatesByDiagnostic(
		clinicLabReportId: string,
		clinicId: string
	) {
		try {
			// Using TypeORM's query builder to match against JSONB array
			const templates = await this.templateRepository
				.createQueryBuilder('template')
				.where('template.clinicId = :clinicId', { clinicId })
				.andWhere('template.isActive = :isActive', { isActive: true })
				.andWhere(
					'template.assignedDiagnostics @> :diagnosticId::jsonb',
					{
						diagnosticId: JSON.stringify([
							{ id: clinicLabReportId }
						])
					}
				)
				.getMany();

			this.logger.log('Found templates for diagnostic', {
				clinicLabReportId,
				clinicId,
				count: templates.length
			});

			return {
				status: true,
				data: templates
			};
		} catch (error) {
			this.logger.error('Error finding templates', {
				error,
				clinicLabReportId,
				clinicId
			});
			throw error;
		}
	}

	async getNotesByLabReport(labReportId: string, clinicId: string) {
		const notes = await this.diagnosticNoteRepository.find({
			where: { labReportId, clinicId },
			relations: ['template', 'creator'],
			order: { createdAt: 'DESC' }
		});

		return notes;
	}

	async getPatientNotes(patientId: string) {
		const note = await this.diagnosticNoteRepository.find({
			where: { patientId }
		});

		if (!note) {
			throw new NotFoundException('Diagnostic notes were not found');
		}

		return note;
	}

	async getNote(noteId: string) {
		const note = await this.diagnosticNoteRepository.find({
			where: { id: noteId }
		});

		if (!note) {
			throw new NotFoundException('Diagnostic note not found');
		}

		return note;
	}

	async deleteNote(id: string) {
		const note = await this.diagnosticNoteRepository.findOne({
			where: { id }
		});

		if (!note) {
			throw new NotFoundException('Diagnostic note not found');
		}

		// Store appointment and lab report info before deletion for broadcasting
		const appointmentId = note.appointmentId;
		const labReportId = note.labReportId;

		await this.diagnosticNoteRepository.remove(note);

		// Update appointment details and broadcast the diagnostic note deletion
		if (appointmentId && labReportId) {
			await this.updateAppointmentDetailsAndBroadcastDiagnosticNotes(
				appointmentId,
				labReportId
			);
		}

		return { success: true };
	}

	/**
	 * Helper method to update appointment details with diagnostic notes and broadcast changes via socket
	 * Uses selective updates to only modify the specific lab report that had diagnostic notes changed.
	 */
	private async updateAppointmentDetailsAndBroadcastDiagnosticNotes(
		appointmentId: string,
		labReportId: string
	): Promise<void> {
		this.logger.log(
			`Updating appointment details and broadcasting diagnostic notes change for appointment ${appointmentId}`,
			{ appointmentId, labReportId }
		);

		try {
			// Fetch the appointment with appointment details relation
			const appointment = await this.appointmentRepository.findOne({
				where: { id: appointmentId },
				relations: ['appointmentDetails']
			});

			if (!appointment) {
				this.logger.warn(
					`Appointment ${appointmentId} not found for diagnostic notes update`,
					{ appointmentId }
				);
				return;
			}

			// Get current appointment details or initialize if not exists
			const currentDetails =
				appointment.appointmentDetails?.details || {};
			const currentObjective = (currentDetails as any).objective || {};
			const currentLabReports = currentObjective.labReports || [];

			// Find the specific lab report that needs updating
			const labReportIndex = currentLabReports.findIndex(
				(report: any) => report.labReportId === labReportId
			);

			if (labReportIndex === -1) {
				this.logger.warn(
					`Lab report ${labReportId} not found in appointment ${appointmentId} details`,
					{ appointmentId, labReportId }
				);
				return;
			}

			// Fetch updated diagnostic notes for only the specific lab report
			const updatedDiagnosticNotes =
				await this.diagnosticNoteRepository.find({
					where: {
						labReportId: labReportId,
						appointmentId: appointmentId
					},
					order: { createdAt: 'DESC' }
				});

			// Create updated lab reports array with selective update
			const updatedLabReports = [...currentLabReports];
			const existingLabReport = updatedLabReports[labReportIndex];

			// Only update the diagnostic notes field, preserve all other fields
			updatedLabReports[labReportIndex] = {
				...existingLabReport,
				diagnosticNotes: updatedDiagnosticNotes || []
			};

			// Update appointment details in the database - preserve other objective fields
			const updatedDetails = {
				...currentDetails,
				objective: {
					...currentObjective,
					labReports: updatedLabReports
				}
			};

			// Update or create the appointment details entity
			if (appointment.appointmentDetails) {
				// Update existing details - save the appointmentDetails entity directly
				appointment.appointmentDetails.details = updatedDetails;
				const savedDetails =
					await this.appointmentDetailsRepository.save(
						appointment.appointmentDetails
					);
				this.logger.log(
					`Updated existing appointment details with diagnostic notes for appointment ${appointmentId}. Details ID: ${savedDetails.id}`,
					{
						appointmentId,
						detailsId: savedDetails.id,
						updatedLabReportId: labReportId,
						diagnosticNotesCount: updatedDiagnosticNotes.length
					}
				);

				// Verify the save by fetching the data back
				const verifyDetails =
					await this.appointmentDetailsRepository.findOne({
						where: { id: savedDetails.id }
					});
				const verifyObjective =
					(verifyDetails?.details as any)?.objective || {};
				const verifyLabReports = verifyObjective.labReports || [];
				const verifyTargetReport = verifyLabReports.find(
					(r: any) => r.labReportId === labReportId
				);
				this.logger.log(
					`Verification: Fetched appointment details after diagnostic notes save`,
					{
						appointmentId,
						detailsId: savedDetails.id,
						verifyLabReportsCount: verifyLabReports.length,
						targetReportDiagnosticNotesCount:
							verifyTargetReport?.diagnosticNotes?.length || 0
					}
				);
			} else {
				// Create new details if none exist
				const newAppointmentDetails =
					this.appointmentDetailsRepository.create({
						appointmentId,
						details: updatedDetails
					});
				await this.appointmentDetailsRepository.save(
					newAppointmentDetails
				);
				this.logger.log(
					`Created new appointment details with diagnostic notes for appointment ${appointmentId}`,
					{
						appointmentId,
						labReportId,
						diagnosticNotesCount: updatedDiagnosticNotes.length
					}
				);
			}

			this.logger.log(
				`Updated appointment details with diagnostic notes for appointment ${appointmentId}`,
				{
					appointmentId,
					labReportId,
					totalLabReportsCount: updatedLabReports.length,
					updatedDiagnosticNotesCount: updatedDiagnosticNotes.length,
					updateType: 'selective_diagnostic_notes_update'
				}
			);

			// Broadcast the updated state via socket
			const broadcastPayload = {
				appointmentId: appointmentId,
				key: 'objective.labReports',
				value: updatedLabReports
			};

			this.logger.log(
				`Broadcasting diagnostic notes change via socket for appointment ${appointmentId}`,
				{ appointmentId, labReportId }
			);

			await this.appointmentGateway.publishAppointmentUpdate(
				appointmentId,
				broadcastPayload
			);

			this.logger.log(
				`Successfully updated appointment details and broadcasted diagnostic notes change for appointment ${appointmentId}`
			);
		} catch (error) {
			this.logger.error(
				'Error updating appointment details and broadcasting diagnostic notes change',
				{
					appointmentId,
					labReportId,
					error:
						error instanceof Error ? error.message : String(error),
					stack: error instanceof Error ? error.stack : undefined
				}
			);
			// Don't throw error to avoid breaking the main operation
		}
	}

	private async generateAndUploadPDF(
		noteData: any,
		templateType: 'notes' | 'table'
	): Promise<string> {
		try {
			const noteHTML =
				templateType === 'table'
					? generateDiagnosticReportTable(noteData)
					: generateDiagnosticReportNote(noteData);
			const pdfBuffer = await generatePDF(noteHTML);
			const fileKey = `diagnostic-notes/${uuidv4()}.pdf`;
			await this.s3Service.uploadPdfToS3(pdfBuffer, fileKey);
			return fileKey;
		} catch (error) {
			this.logger.error('Error generating/uploading PDF', { error });
			throw new InternalServerErrorException('Failed to generate PDF');
		}
	}

	getClinicAddress(patientDetail: Patient) {
		const addressParts = [];

		if (patientDetail?.clinic?.addressLine1) {
			addressParts.push(patientDetail.clinic.addressLine1);
		}

		if (patientDetail?.clinic?.addressPincode) {
			addressParts.push(`- ${patientDetail.clinic.addressPincode}`);
		}

		this.logger.log('Clinic Address formatted', {
			addressParts
		});
		return addressParts.join(', ').trim();
	}

	private async getCompleteNoteData(
		templateName: string,
		noteData: any,
		patientId: string,
		diagnosticNumber?: string,
		diagnosticName?: string,
		createdAt?: Date
	) {
		// Get patient details with all necessary relations
		const patientDetails =
			await this.patientService.getPatientDetails(patientId);

		const clinic = patientDetails.clinic;

		// Format data for PDF generation
		return {
			diagnosticName: diagnosticName || '',
			diagnosticNumber: diagnosticNumber,
			diagnosticDate: moment(createdAt).format('DD MMM YYYY'),

			// Clinic Information
			clinicName: patientDetails?.clinic?.name || '',
			clinicAddress: this.getClinicAddress(patientDetails),
			clinicCity: patientDetails?.clinic?.city,
			clinicPhone: patientDetails?.clinic?.phoneNumbers[0].number || '',
			clinicEmail: patientDetails?.clinic?.email || '',
			clinicWebsite: patientDetails?.clinic?.website || '',
			clinicLogoUrl: clinic?.logoUrl || '',

			// Patient Information
			petName: patientDetails?.patientName || '',
			petBreed:
				patientDetails?.breed
					?.split('_')
					.join(' ')
					.toLowerCase()
					.replace(/\b\w/g, char => char.toUpperCase()) || '',
			petSpecies: patientDetails?.species || '',
			petAge: patientDetails?.age || '',
			petGender: patientDetails?.gender || '',

			// Owner Information
			customerName: `${patientDetails?.patientOwners?.[0]?.ownerBrand?.firstName || ''} ${patientDetails?.patientOwners?.[0]?.ownerBrand?.lastName || ''}`,
			ownerName:
				patientDetails?.patientOwners?.[0]?.ownerBrand?.firstName || '',
			ownerEmail:
				patientDetails?.patientOwners?.[0]?.ownerBrand?.email || '',
			ownerPhone: `${patientDetails?.patientOwners[0]?.ownerBrand?.globalOwner?.countryCode} ${patientDetails?.patientOwners[0]?.ownerBrand?.globalOwner?.phoneNumber}`,

			// Template and Assessment Information
			templateName: templateName,
			assessmentText: noteData?.notes || '',
			tableData: noteData?.values || {}
		};
	}
}

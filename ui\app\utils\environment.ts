type Environment = 'development' | 'qa' | 'uat' | 'prod';

interface EnvironmentConfig {
    domain: string;
    port?: string;
}

/**
 * Environment configuration for different deployment environments
 */
const ENVIRONMENT_CONFIG: Record<Environment, EnvironmentConfig> = {
    development: {
        domain: 'localhost',
        port: '4201'
    },
    qa: {
        domain: 'nidanaqa-api.napses.in'
    },
    uat: {
        domain: 'nidana.tech'
    },
    prod: {
        domain: 'nidana.io'
    }
};

/**
 * Detects the current environment based on the hostname
 * @param hostname - Optional hostname to use (for server-side usage)
 * @returns The current environment
 */
export const getCurrentEnvironment = (hostname?: string): Environment => {
    const hostToCheck = hostname || (typeof window !== 'undefined' ? window.location.hostname : '');
    
    if (!hostToCheck) {
        // Server-side fallback - could be enhanced with process.env.NODE_ENV
        return 'development';
    }

    if (hostToCheck.includes('nidana.io')) {
        return 'prod';
    } else if (hostToCheck.includes('nidanaqa-api.napses.in')) {
        return 'qa';
    } else if (hostToCheck.includes('nidana.tech')) {
        return 'uat';
    } else if (hostToCheck.includes('localhost')) {
        return 'development';
    }

    // Default fallback
    return 'development';
};

/**
 * Gets the base domain for the current environment
 * @param hostname - Optional hostname to determine environment (for server-side usage)
 * @returns The base domain (e.g., 'nidana.io', 'localhost:4201')
 */
export const getBaseDomain = (hostname?: string): string => {
    const env = getCurrentEnvironment(hostname);
    const config = ENVIRONMENT_CONFIG[env];
    
    return config.port ? `${config.domain}:${config.port}` : config.domain;
};

/**
 * Constructs a brand URL for the current environment
 * @param brandSlug - The brand slug to use in the URL
 * @param path - Optional path to append (defaults to empty string)
 * @param hostname - Optional hostname to determine environment (for server-side usage)
 * @returns Complete URL for the brand
 */
export const getBrandUrl = (brandSlug: string, path: string = '', hostname?: string): string => {
    const baseDomain = getBaseDomain(hostname);
    const protocol = getCurrentEnvironment(hostname) === 'development' ? 'http' : 'https';
    
    return `${protocol}://${brandSlug}.${baseDomain}${path}`;
};

/**
 * Gets just the domain part for display purposes
 * @param brandSlug - The brand slug
 * @param hostname - Optional hostname to determine environment (for server-side usage)
 * @returns Domain string for display (e.g., 'clinic.nidana.io')
 */
export const getBrandDisplayDomain = (brandSlug: string, hostname?: string): string => {
    const baseDomain = getBaseDomain(hostname);
    return `${brandSlug}.${baseDomain}`;
};

/**
 * Extracts the brand slug from a hostname
 * Works both client-side and server-side
 * @param hostname - Optional hostname to parse (for server-side usage)
 * @returns The brand slug extracted from the hostname
 */
export const getBrandSlugFromHostname = (hostname?: string): string => {
    const hostToCheck = hostname || (typeof window !== 'undefined' ? window.location.hostname : '');
    
    if (!hostToCheck) {
        return '';
    }

    const environment = getCurrentEnvironment(hostToCheck);
    const config = ENVIRONMENT_CONFIG[environment];
    
    // Construct the base domain pattern to remove from hostname
    const baseDomain = config.port ? `${config.domain}:${config.port}` : config.domain;
    
    // Handle localhost development scenarios
    if (environment === 'development' && hostToCheck.includes('localhost')) {
        const parts = hostToCheck.split('.');
        return parts[0] === 'localhost' ? '' : parts[0];
    }
    
    // For other environments, extract subdomain by removing base domain
    if (hostToCheck.endsWith(`.${baseDomain}`)) {
        return hostToCheck.replace(`.${baseDomain}`, '');
    }
    
    // If hostname matches base domain exactly, no brand slug
    if (hostToCheck === baseDomain) {
        return '';
    }
    
    // Fallback: try to extract first part as subdomain
    const parts = hostToCheck.split('.');
    return parts.length > 1 ? parts[0] : '';
};

/**
 * Checks if the current environment is production
 * @param hostname - Optional hostname to check (for server-side usage)
 * @returns True if in production environment
 */
export const isProduction = (hostname?: string): boolean => {
    return getCurrentEnvironment(hostname) === 'prod';
};

/**
 * Checks if the current environment is QA
 * @param hostname - Optional hostname to check (for server-side usage) 
 * @returns True if in QA environment
 */
export const isQA = (hostname?: string): boolean => {
    return getCurrentEnvironment(hostname) === 'qa';
};

/**
 * Checks if the current environment is UAT
 * @param hostname - Optional hostname to check (for server-side usage)
 * @returns True if in UAT environment
 */
export const isUAT = (hostname?: string): boolean => {
    return getCurrentEnvironment(hostname) === 'uat';
};

/**
 * Checks if the current environment is local development
 * @param hostname - Optional hostname to check (for server-side usage)
 * @returns True if in local development environment
 */
export const isLocal = (hostname?: string): boolean => {
    return getCurrentEnvironment(hostname) === 'development';
};

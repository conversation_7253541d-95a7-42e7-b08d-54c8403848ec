import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import {
	GetRevenueChartDataDto,
	DownloadAnalyticsReportDto,
	GetAppointmentsChartDataDto,
	GetDoctorSummaryDto,
	GetSummaryDto,
	AnalyticsType,
	AnalyticsReportType,
	AppointmentAnalyticsType,
	AnalyticsTimeFrame,
	RevenueChartDataPoint,
	CollectedPaymentsChartDataPoint,
	AppointmentsChartDataPoint,
	AppointmentDurationDataPoint,
	AppointmentsChartResponse,
	DoctorSummaryResponseDto,
	SummaryResponseDto
} from './analytics.dto';

describe('Analytics DTOs', () => {
	describe('Enums', () => {
		it('should have correct AnalyticsTimeFrame values', () => {
			expect(AnalyticsTimeFrame.ONE_DAY).toBe('1D');
			expect(AnalyticsTimeFrame.ONE_WEEK).toBe('1W');
			expect(AnalyticsTimeFrame.ONE_MONTH).toBe('1M');
			expect(AnalyticsTimeFrame.ONE_YEAR).toBe('1Y');
		});

		it('should have correct AnalyticsType values', () => {
			expect(AnalyticsType.REVENUE).toBe('REVENUE');
			expect(AnalyticsType.APPOINTMENTS).toBe('APPOINTMENTS');
			expect(AnalyticsType.DOCTOR_PERFORMANCE).toBe('DOCTOR_PERFORMANCE');
			expect(AnalyticsType.OUTSTANDING_BALANCE).toBe(
				'OUTSTANDING_BALANCE'
			);
			expect(AnalyticsType.COLLECTED_PAYMENTS).toBe('COLLECTED_PAYMENTS');
		});

		it('should have correct AnalyticsReportType values', () => {
			expect(AnalyticsReportType.BY_BILLING).toBe('by-billing');
			expect(AnalyticsReportType.BY_PATIENT).toBe('by-patient');
		});

		it('should have correct AppointmentAnalyticsType values', () => {
			expect(AppointmentAnalyticsType.ALL).toBe('All');
			expect(AppointmentAnalyticsType.BUSIEST_DAYS).toBe('BusiestDays');
			expect(AppointmentAnalyticsType.BUSIEST_HOURS).toBe('BusiestHours');
			expect(AppointmentAnalyticsType.AVERAGE_DURATION).toBe(
				'AverageDuration'
			);
		});
	});

	describe('GetRevenueChartDataDto', () => {
		it('should validate valid data', async () => {
			const dto = plainToClass(GetRevenueChartDataDto, {
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123'
			});

			const errors = await validate(dto);
			expect(errors).toHaveLength(0);
		});

		it('should fail validation with missing startDate', async () => {
			const dto = plainToClass(GetRevenueChartDataDto, {
				endDate: '2023-01-31',
				clinicId: 'clinic-123'
			});

			const errors = await validate(dto);
			expect(errors).toHaveLength(1);
			expect(errors[0].property).toBe('startDate');
		});

		it('should fail validation with empty startDate', async () => {
			const dto = plainToClass(GetRevenueChartDataDto, {
				startDate: '',
				endDate: '2023-01-31',
				clinicId: 'clinic-123'
			});

			const errors = await validate(dto);
			expect(errors).toHaveLength(1);
			expect(errors[0].property).toBe('startDate');
		});

		it('should fail validation with missing endDate', async () => {
			const dto = plainToClass(GetRevenueChartDataDto, {
				startDate: '2023-01-01',
				clinicId: 'clinic-123'
			});

			const errors = await validate(dto);
			expect(errors).toHaveLength(1);
			expect(errors[0].property).toBe('endDate');
		});

		it('should fail validation with missing clinicId', async () => {
			const dto = plainToClass(GetRevenueChartDataDto, {
				startDate: '2023-01-01',
				endDate: '2023-01-31'
			});

			const errors = await validate(dto);
			expect(errors).toHaveLength(1);
			expect(errors[0].property).toBe('clinicId');
		});
	});

	describe('DownloadAnalyticsReportDto', () => {
		it('should validate valid data without reportType', async () => {
			const dto = plainToClass(DownloadAnalyticsReportDto, {
				type: AnalyticsType.REVENUE,
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123'
			});

			const errors = await validate(dto);
			expect(errors).toHaveLength(0);
		});

		it('should validate valid data with reportType', async () => {
			const dto = plainToClass(DownloadAnalyticsReportDto, {
				type: AnalyticsType.REVENUE,
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123',
				reportType: AnalyticsReportType.BY_BILLING
			});

			const errors = await validate(dto);
			expect(errors).toHaveLength(0);
		});

		it('should fail validation with invalid type', async () => {
			const dto = plainToClass(DownloadAnalyticsReportDto, {
				type: 'INVALID_TYPE',
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123'
			});

			const errors = await validate(dto);
			expect(errors).toHaveLength(1);
			expect(errors[0].property).toBe('type');
		});

		it('should fail validation with invalid reportType', async () => {
			const dto = plainToClass(DownloadAnalyticsReportDto, {
				type: AnalyticsType.REVENUE,
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123',
				reportType: 'invalid-report-type'
			});

			const errors = await validate(dto);
			expect(errors).toHaveLength(1);
			expect(errors[0].property).toBe('reportType');
		});
	});

	describe('GetAppointmentsChartDataDto', () => {
		it('should create valid instance', () => {
			const dto = new GetAppointmentsChartDataDto();
			dto.startDate = '2023-01-01';
			dto.endDate = '2023-01-31';
			dto.clinicId = 'clinic-123';
			dto.type = AppointmentAnalyticsType.ALL;

			expect(dto.startDate).toBe('2023-01-01');
			expect(dto.endDate).toBe('2023-01-31');
			expect(dto.clinicId).toBe('clinic-123');
			expect(dto.type).toBe(AppointmentAnalyticsType.ALL);
		});
	});

	describe('GetDoctorSummaryDto', () => {
		it('should validate valid data', async () => {
			const dto = plainToClass(GetDoctorSummaryDto, {
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123'
			});

			const errors = await validate(dto);
			expect(errors).toHaveLength(0);
		});

		it('should fail validation with missing required fields', async () => {
			const dto = plainToClass(GetDoctorSummaryDto, {});

			const errors = await validate(dto);
			expect(errors).toHaveLength(3);
			const properties = errors.map(error => error.property);
			expect(properties).toContain('startDate');
			expect(properties).toContain('endDate');
			expect(properties).toContain('clinicId');
		});
	});

	describe('GetSummaryDto', () => {
		it('should validate valid data', async () => {
			const dto = plainToClass(GetSummaryDto, {
				startDate: '2023-01-01',
				endDate: '2023-01-31',
				clinicId: 'clinic-123'
			});

			const errors = await validate(dto);
			expect(errors).toHaveLength(0);
		});

		it('should fail validation with missing required fields', async () => {
			const dto = plainToClass(GetSummaryDto, {});

			const errors = await validate(dto);
			expect(errors).toHaveLength(3);
			const properties = errors.map(error => error.property);
			expect(properties).toContain('startDate');
			expect(properties).toContain('endDate');
			expect(properties).toContain('clinicId');
		});
	});

	describe('Interface Types', () => {
		it('should create RevenueChartDataPoint', () => {
			const dataPoint: RevenueChartDataPoint = {
				date: '2023-01-01',
				products: 100,
				services: 200,
				diagnostics: 50,
				medications: 75,
				vaccinations: 25
			};

			expect(dataPoint.date).toBe('2023-01-01');
			expect(dataPoint.products).toBe(100);
			expect(dataPoint.services).toBe(200);
			expect(dataPoint.diagnostics).toBe(50);
			expect(dataPoint.medications).toBe(75);
			expect(dataPoint.vaccinations).toBe(25);
		});

		it('should create CollectedPaymentsChartDataPoint', () => {
			const dataPoint: CollectedPaymentsChartDataPoint = {
				date: '2023-01-01',
				cash: 100,
				card: 200,
				wallet: 50,
				cheque: 75,
				bankTransfer: 25
			};

			expect(dataPoint.date).toBe('2023-01-01');
			expect(dataPoint.cash).toBe(100);
			expect(dataPoint.card).toBe(200);
			expect(dataPoint.wallet).toBe(50);
			expect(dataPoint.cheque).toBe(75);
			expect(dataPoint.bankTransfer).toBe(25);
		});

		it('should create AppointmentsChartDataPoint', () => {
			const dataPoint: AppointmentsChartDataPoint = {
				date: '2023-01-01',
				total: 10,
				missed: 2,
				averageDuration: 30
			};

			expect(dataPoint.date).toBe('2023-01-01');
			expect(dataPoint.total).toBe(10);
			expect(dataPoint.missed).toBe(2);
			expect(dataPoint.averageDuration).toBe(30);
		});

		it('should create AppointmentDurationDataPoint', () => {
			const dataPoint: AppointmentDurationDataPoint = {
				date: '2023-01-01',
				checkinDuration: 5,
				receivingCareDuration: 20,
				checkoutDuration: 5,
				totalDuration: 30
			};

			expect(dataPoint.date).toBe('2023-01-01');
			expect(dataPoint.checkinDuration).toBe(5);
			expect(dataPoint.receivingCareDuration).toBe(20);
			expect(dataPoint.checkoutDuration).toBe(5);
			expect(dataPoint.totalDuration).toBe(30);
		});

		it('should create AppointmentsChartResponse', () => {
			const response: AppointmentsChartResponse = {
				total: [{ date: '2023-01-01', total: 10 }],
				missed: [{ date: '2023-01-01', missed: 2 }],
				busiestDays: [
					{
						day: 'Monday',
						count: 15,
						weeksCount: 4,
						total: 60
					}
				],
				busiestHours: [
					{
						hour: '10',
						count: 5,
						daysCount: 20,
						total: 100
					}
				],
				averageDuration: [
					{
						date: '2023-01-01',
						checkinDuration: 5,
						receivingCareDuration: 20,
						checkoutDuration: 5,
						totalDuration: 30
					}
				]
			};

			expect(response.total).toHaveLength(1);
			expect(response.missed).toHaveLength(1);
			expect(response.busiestDays).toHaveLength(1);
			expect(response.busiestHours).toHaveLength(1);
			expect(response.averageDuration).toHaveLength(1);
		});

		it('should create DoctorSummaryResponseDto', () => {
			const response: DoctorSummaryResponseDto = {
				doctorName: 'Dr. Smith',
				numAppointments: 50,
				totalRevenue: 5000,
				revenuePerAppointment: 100,
				avgAppointmentDurationMinutes: 30
			};

			expect(response.doctorName).toBe('Dr. Smith');
			expect(response.numAppointments).toBe(50);
			expect(response.totalRevenue).toBe(5000);
			expect(response.revenuePerAppointment).toBe(100);
			expect(response.avgAppointmentDurationMinutes).toBe(30);
		});

		it('should create SummaryResponseDto', () => {
			const response: SummaryResponseDto = {
				appointmentsCompleted: 100,
				invoicesGenerated: 95,
				totalBilling: 10000,
				creditNotesGenerated: 5,
				totalCreditNotes: 500,
				amountCollected: {
					cash: 2000,
					card: 3000,
					wallet: 1000,
					cheque: 500,
					bankTransfer: 1500,
					total: 8000
				},
				amountRefunded: {
					cash: 100,
					card: 200,
					wallet: 50,
					cheque: 25,
					bankTransfer: 75,
					total: 450
				},
				badDebts: 1000
			};

			expect(response.appointmentsCompleted).toBe(100);
			expect(response.invoicesGenerated).toBe(95);
			expect(response.totalBilling).toBe(10000);
			expect(response.creditNotesGenerated).toBe(5);
			expect(response.totalCreditNotes).toBe(500);
			expect(response.amountCollected.total).toBe(8000);
			expect(response.amountRefunded.total).toBe(450);
			expect(response.badDebts).toBe(1000);
		});
	});
});

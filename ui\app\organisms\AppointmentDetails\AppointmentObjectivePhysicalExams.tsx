import { ChangeEvent } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { Heading, Input } from '@/app/atoms';

interface AppointmentObjectivePhysicalExamsProps {
    getValues: UseFormReturn['getValues'];
    setValue: UseFormReturn['setValue'];
    register: UseFormReturn['register'];
    handleBlur: (event: ChangeEvent<HTMLInputElement>) => void;
}

const AppointmentObjectivePhysicalExams: React.FC<
    AppointmentObjectivePhysicalExamsProps
> = ({ getValues, setValue, register, handleBlur }) => {
    const physicalExamsArray = [
        {
            title: 'oralTeeth',
            name: 'Oral cavity/Teeth',
        },
        {
            title: 'eyes',
            name: 'Eyes/orbit',
        },
        {
            title: 'throat',
            name: 'Throat',
        },
        {
            title: 'respiratory',
            name: 'Respiratory',
        },
        {
            title: 'musculoskeletal',
            name: 'Musculoskeletal',
        },
        {
            title: 'urogenital',
            name: 'Urogenital',
        },
        {
            title: 'mucous_membranes',
            name: 'Mucous membranes',
        },
        {
            title: 'ears',
            name: 'E<PERSON>',
        },
        {
            title: 'cardiovascular',
            name: 'Cardio vascular',
        },
        {
            title: 'abdomen',
            name: 'Abdomen',
        },
        {
            title: 'glands',
            name: 'Glands/Lymph Nodes',
        },
        {
            title: 'rectal',
            name: 'Rectal',
        },
    ];

    return (
        <div>
            <div className="flex flex-col gap-2 w-full border  px-4 pb-4 rounded-2xl">
                <div className="pt-4 pb-4">
                    <Heading type="h6">Physical Exams</Heading>
                </div>
                {physicalExamsArray.map((item) => {
                    return (
                        <div
                            key={item.title}
                            id="pe-item"
                            className="w-full border border-gray-400 h-fit p-2 py-1 grid grid-cols-[15%_25%_53.5%] gap-10 items-center rounded"
                        >
                            <div className="w-full text-black-500 text-xs font-normal">
                                {item.name}
                            </div>
                            <div
                                onBlur={handleBlur}
                                className="w-full flex items-center bg-gray-200 rounded-full p-0.5"
                            >
                                <div
                                    className="flex w-80 h-6 relative"
                                    onClick={(event) => {
                                        const currentStatus = getValues(
                                            `objective.physicalExam.${item.title}.status`
                                        );
                                        const statusOrder = [
                                            'Normal',
                                            'Not Captured',
                                            'Abnormal',
                                        ];
                                        const currentIndex =
                                            statusOrder.indexOf(currentStatus);
                                        const newStatus =
                                            statusOrder[(currentIndex + 1) % 3];

                                        setValue(
                                            `objective.physicalExam.${item.title}.status`,
                                            newStatus,
                                            { shouldValidate: true }
                                        );
                                        handleBlur(event);
                                    }}
                                >
                                    {['Normal', 'Not Captured', 'Abnormal'].map(
                                        (status) => (
                                            <span
                                                key={status}
                                                className={`flex-1 flex items-center justify-center text-xs rounded-full transition-colors duration-200 ease-in-out z-10 ${
                                                    getValues(
                                                        `objective.physicalExam.${item.title}.status`
                                                    ) === status
                                                        ? 'text-white'
                                                        : 'text-gray-600'
                                                }`}
                                            >
                                                {status}
                                            </span>
                                        )
                                    )}
                                    <span
                                        className={`absolute w-1/3 h-full bg-gray-800 rounded-full transition-transform duration-200 ease-in-out ${
                                            getValues(
                                                `objective.physicalExam.${item.title}.status`
                                            ) === 'Normal'
                                                ? 'transform translate-x-0'
                                                : getValues(
                                                        `objective.physicalExam.${item.title}.status`
                                                    ) === 'Not Captured'
                                                  ? 'transform translate-x-full'
                                                  : 'transform translate-x-[200%]'
                                        }`}
                                    ></span>
                                </div>
                            </div>
                            <Input
                                className="w-full border border-gray-400 rounded-none p-2"
                                id={`objective.physicalExam.${item.title}.text`}
                                name={`objective.physicalExam.${item.title}.text`}
                                register={register}
                                value={getValues(
                                    `objective.physicalExam.${item.title}.text`
                                )}
                                onChange={(e) => {
                                    setValue(
                                        `objective.physicalExam.${item.title}.text`,
                                        e.target.value,
                                        { shouldValidate: true }
                                    );
                                    handleBlur(e);
                                }}
                                onBlur={handleBlur}
                            />
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

export default AppointmentObjectivePhysicalExams;

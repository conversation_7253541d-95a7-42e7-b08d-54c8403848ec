# SonarQube Local Setup for Nidana

This guide explains how to set up and use SonarQube locally for code quality analysis of the Nidana project components (API, UI, and PatientPortal).

## Prerequisites

- Docker and Docker Compose installed on your machine
- Git repository cloned locally
- Bash shell (for running the provided scripts)

## Components

The setup includes:

1. **SonarQube Server** - The main SonarQube application that provides the web interface and analysis engine
2. **PostgreSQL Database** - Required by SonarQube to store analysis results and configuration
3. **Sonar Scanner** - CLI tool to analyze code and send results to SonarQube server

## Directory Structure

```
Nidana2/
├── api/                          # NestJS API application
│   └── sonar-project.properties  # SonarQube configuration for API
├── ui/                           # Next.js UI application
│   └── sonar-project.properties  # SonarQube configuration for UI
├── patientportal/                # Next.js Patient Portal application
│   └── sonar-project.properties  # SonarQube configuration for Patient Portal
├── sonar-configs/                # SonarQube configuration templates
├── docker-compose.sonarqube.yml  # Docker Compose configuration for SonarQube
└── sonar-scan.sh                 # Helper script for running SonarQube
```

## Getting Started

### 1. Start SonarQube Server

```bash
./sonar-scan.sh start
```

This command:
- Creates necessary directories and configuration files
- Starts the SonarQube server and PostgreSQL database
- Waits for SonarQube to be ready

Once started, SonarQube will be available at http://localhost:9000

**Default credentials:**
- Username: admin
- Password: admin

You will be prompted to change the password on first login.

### 2. Run Code Analysis

To analyze a specific component:

```bash
# For API
./sonar-scan.sh scan api

# For UI
./sonar-scan.sh scan ui

# For Patient Portal
./sonar-scan.sh scan patientportal

# For all components
./sonar-scan.sh scan all
```

### 3. View Results

1. Open http://localhost:9000 in your browser
2. Log in with your credentials
3. Navigate to "Projects" to see analysis results
4. Click on a project to see detailed code quality metrics

### 4. Stop SonarQube Server

```bash
./sonar-scan.sh stop
```

### 5. Restart SonarQube Server

```bash
./sonar-scan.sh restart
```

## Configuration Files

Each component has its own SonarQube configuration file:

- **API**: `api/sonar-project.properties`
- **UI**: `ui/sonar-project.properties`
- **Patient Portal**: `patientportal/sonar-project.properties`

These files define:
- Project key and name
- Source directories to analyze
- Files to exclude from analysis
- Coverage report paths
- Quality gate settings

## Troubleshooting

### SonarQube Fails to Start

If SonarQube fails to start, check the following:

1. **Memory Settings**: Ensure your Docker has enough memory allocated
   ```bash
   # Check logs for errors
   docker logs sonarqube-server
   ```

2. **Elasticsearch Issues**: SonarQube uses Elasticsearch which requires specific system settings
   ```bash
   # On Linux, set the following
   sudo sysctl -w vm.max_map_count=524288
   ```

3. **Port Conflicts**: Ensure port 9000 is not already in use
   ```bash
   # Check if port 9000 is in use
   lsof -i :9000
   ```

### Scanner Issues

If the scanner fails to run:

1. **Check Connectivity**: Ensure the scanner can connect to SonarQube server
2. **Check Permissions**: Ensure the scanner has access to source code directories
3. **Check Configuration**: Verify sonar-project.properties files are correctly configured

## Advanced Configuration

### Quality Gates

Quality Gates define the criteria that your code must meet before it can be considered production-ready. You can configure custom Quality Gates in the SonarQube web interface:

1. Go to Quality Gates in the SonarQube UI
2. Create a new Quality Gate or modify the default one
3. Add conditions based on metrics like code coverage, duplications, etc.

### Custom Rules

You can define custom rules or modify existing ones:

1. Go to Rules in the SonarQube UI
2. Create a new rule or modify existing ones
3. Apply the rules to your Quality Profile

## Integrating with CI/CD

For CI/CD integration, you can use the same scanner commands in your pipeline scripts. The Bitbucket pipeline configuration already includes SonarQube analysis for the remote server.

# Makefile for SonarQube operations
# Usage: make -f Makefile.sonar [target]

.PHONY: help sonar-start sonar-stop sonar-restart sonar-scan-api sonar-scan-ui sonar-scan-patientportal sonar-scan-all sonar-clean sonar-logs

# Default target
help:
	@echo "SonarQube Local Development Commands"
	@echo "===================================="
	@echo ""
	@echo "Setup and Management:"
	@echo "  sonar-start           Start SonarQube server and database (standard version)"
	@echo "  sonar-start-lite      Start SonarQube server and database (lite version for macOS)"
	@echo "  sonar-stop            Stop SonarQube server and database"
	@echo "  sonar-restart         Restart SonarQube server"
	@echo "  sonar-restart-lite    Restart SonarQube server (lite version)"
	@echo ""
	@echo "Code Analysis:"
	@echo "  sonar-scan-api        Run SonarQube analysis for API component"
	@echo "  sonar-scan-ui         Run SonarQube analysis for UI component"
	@echo "  sonar-scan-patientportal  Run SonarQube analysis for Patient Portal"
	@echo "  sonar-scan-all        Run SonarQube analysis for all components"
	@echo ""
	@echo "Utilities:"
	@echo "  sonar-logs            Show SonarQube server logs"
	@echo "  sonar-clean           Clean up SonarQube containers and volumes"
	@echo "  sonar-status          Check SonarQube server status"
	@echo ""
	@echo "Prerequisites:"
	@echo "  - Docker and Docker Compose must be installed"
	@echo "  - For macOS users, use 'make sonar-start-lite' to avoid vm.max_map_count issues"
	@echo "  - Access SonarQube at http://localhost:9000 (admin/admin)"
	@echo ""
	@echo "Note: The lite version uses less memory and is more compatible with macOS"

# Start SonarQube server
sonar-start:
	@echo "Starting SonarQube server..."
	./sonar-scan.sh start

# Start SonarQube server (lite version for macOS)
sonar-start-lite:
	@echo "Starting SonarQube server (lite version)..."
	./sonar-scan.sh start-lite

# Stop SonarQube server
sonar-stop:
	@echo "Stopping SonarQube server..."
	./sonar-scan.sh stop

# Restart SonarQube server
sonar-restart:
	@echo "Restarting SonarQube server..."
	./sonar-scan.sh restart

# Restart SonarQube server (lite version)
sonar-restart-lite:
	@echo "Restarting SonarQube server (lite version)..."
	./sonar-scan.sh restart lite

# Scan API component
sonar-scan-api:
	@echo "Running SonarQube analysis for API..."
	./sonar-scan.sh scan api

# Scan UI component
sonar-scan-ui:
	@echo "Running SonarQube analysis for UI..."
	./sonar-scan.sh scan ui

# Scan Patient Portal component
sonar-scan-patientportal:
	@echo "Running SonarQube analysis for Patient Portal..."
	./sonar-scan.sh scan patientportal

# Scan all components
sonar-scan-all:
	@echo "Running SonarQube analysis for all components..."
	./sonar-scan.sh scan all

# Show SonarQube logs
sonar-logs:
	@echo "Showing SonarQube server logs..."
	./sonar-scan.sh logs

# Check SonarQube status
sonar-status:
	@echo "Checking SonarQube server status..."
	@if docker ps | grep -q sonarqube-server-lite; then \
		echo "✅ SonarQube server (lite version) is running"; \
		echo "🌐 Access at: http://localhost:9000"; \
		curl -s http://localhost:9000/api/system/status | grep -q '"status":"UP"' && echo "✅ SonarQube is ready" || echo "⏳ SonarQube is starting up"; \
	elif docker ps | grep -q sonarqube-server; then \
		echo "✅ SonarQube server (standard version) is running"; \
		echo "🌐 Access at: http://localhost:9000"; \
		curl -s http://localhost:9000/api/system/status | grep -q '"status":"UP"' && echo "✅ SonarQube is ready" || echo "⏳ SonarQube is starting up"; \
	else \
		echo "❌ SonarQube server is not running"; \
		echo "💡 Run 'make sonar-start' to start the server"; \
		echo "💡 For macOS users, run 'make sonar-start-lite' to avoid vm.max_map_count issues"; \
	fi

# Clean up SonarQube containers and volumes
sonar-clean:
	@echo "Cleaning up SonarQube containers and volumes..."
	@echo "⚠️  This will remove all SonarQube data including analysis history!"
	@read -p "Are you sure? (y/N): " confirm && [ "$$confirm" = "y" ] || exit 1
	docker-compose -f docker-compose.sonarqube.yml down -v
	docker volume prune -f
	@echo "✅ SonarQube cleanup completed"

# Generate test coverage for API before scanning
api-test-coverage:
	@echo "Generating test coverage for API..."
	cd api && npm run test:cov

# Generate test coverage for UI before scanning (if tests exist)
ui-test-coverage:
	@echo "Checking for UI tests..."
	@if [ -f ui/package.json ] && grep -q '"test"' ui/package.json; then \
		echo "Running UI tests..."; \
		cd ui && npm test; \
	else \
		echo "No test script found in UI package.json"; \
	fi

# Generate test coverage for Patient Portal before scanning (if tests exist)
patientportal-test-coverage:
	@echo "Checking for Patient Portal tests..."
	@if [ -f patientportal/package.json ] && grep -q '"test"' patientportal/package.json; then \
		echo "Running Patient Portal tests..."; \
		cd patientportal && npm test; \
	else \
		echo "No test script found in Patient Portal package.json"; \
	fi

# Full workflow: test and scan API
api-full:
	@echo "Running full API analysis workflow..."
	$(MAKE) -f Makefile.sonar api-test-coverage
	$(MAKE) -f Makefile.sonar sonar-scan-api

# Full workflow: test and scan UI
ui-full:
	@echo "Running full UI analysis workflow..."
	$(MAKE) -f Makefile.sonar ui-test-coverage
	$(MAKE) -f Makefile.sonar sonar-scan-ui

# Full workflow: test and scan Patient Portal
patientportal-full:
	@echo "Running full Patient Portal analysis workflow..."
	$(MAKE) -f Makefile.sonar patientportal-test-coverage
	$(MAKE) -f Makefile.sonar sonar-scan-patientportal

# Full workflow: test and scan all components
full-analysis:
	@echo "Running full analysis workflow for all components..."
	$(MAKE) -f Makefile.sonar api-test-coverage
	$(MAKE) -f Makefile.sonar ui-test-coverage
	$(MAKE) -f Makefile.sonar patientportal-test-coverage
	$(MAKE) -f Makefile.sonar sonar-scan-all

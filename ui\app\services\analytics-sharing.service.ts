import { httpService } from './http.service';

export interface ShareAnalyticsDocumentsRequest {
  clinicId: string;
  startDate: string;
  endDate: string;
  documentTypes: string[];
  shareMode: string[];
  recipientType: 'client' | 'other';
  email?: string;
  phoneNumber?: string;
  includeExcelReport?: boolean;
}

export interface ShareAnalyticsDocumentsResponse {
  requestId: string;
  message: string;
}

export interface AnalyticsDocumentRequestStatus {
  requestId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  createdAt: string;
  updatedAt: string;
  expiresAt?: string;
  errorMessage?: string;
}

export interface AnalyticsDocumentRequest {
  requestId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  documentTypes: string[];
  shareMode: string[];
  recipientType: 'client' | 'other';
  createdAt: string;
  expiresAt?: string;
}

export interface GetRequestsResponse {
  requests: AnalyticsDocumentRequest[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export const shareAnalyticsDocuments = async (
  data: ShareAnalyticsDocumentsRequest
): Promise<ShareAnalyticsDocumentsResponse> => {
  const response = await httpService.post('/analytics-sharing/share-documents', data);
  return response.data;
};

export const getAnalyticsDocumentRequestStatus = async (
  requestId: string
): Promise<AnalyticsDocumentRequestStatus> => {
  const response = await httpService.get(`/analytics-sharing/requests/${requestId}/status`);
  return response.data;
};

export const getAnalyticsDocumentRequests = async (
  clinicId: string,
  page: number = 1,
  limit: number = 10
): Promise<GetRequestsResponse> => {
  const response = await httpService.get('/analytics-sharing/requests', {
    params: { clinicId, page, limit }
  });
  return response.data;
};

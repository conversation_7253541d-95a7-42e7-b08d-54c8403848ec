import {
	Body,
	Controller,
	Get,
	HttpException,
	HttpStatus,
	Param,
	Post,
	Put,
	UsePipes,
	ValidationPipe
} from '@nestjs/common';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { PatientVaccinationsService } from './patient-vaccinations.service';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { ApiDocumentationBase } from '../base/api-documentation-base';
import { CreatePatientVaccinationDto } from './dto/create-patient-vaccination.dto';
import { UpdatePatientVaccinationDto } from './dto/update-patient-vaccination.dto';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';

@ApiTags('Patient-Vaccinations')
@Controller('patient-vaccinations')
export class PatientVaccinationsController extends ApiDocumentationBase {
	constructor(
		private readonly logger: WinstonLogger,
		private readonly patientVaccinationsService: PatientVaccinationsService
	) {
		super();
	}

	@ApiOkResponse({
		description: 'create new patient-vaccinations',
		type: PatientVaccinationsService
	})
	@Post()
	@UsePipes(new ValidationPipe())
	@TrackMethod('create-patient-vaccinations')
	async create(
		@Body() createPaitentVaccinationDto: CreatePatientVaccinationDto
	) {
		try {
			return await this.patientVaccinationsService.create(
				createPaitentVaccinationDto
			);
		} catch (error) {
			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@ApiOkResponse({
		description: 'get patient-vaccinations',
		type: PatientVaccinationsService
	})
	@Get(':patientId')
	@TrackMethod('get-patient-vaccinations')
	async get(@Param('patientId') patientId: string) {
		try {
			return await this.patientVaccinationsService.get(patientId);
		} catch (error) {
			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@ApiOkResponse({
		description: 'update patient-vaccinations',
		type: 'PatientVaccinationsService'
	})
	@Put(':id')
	@TrackMethod('update-patient-vaccinations')
	async update(
		@Param('id') id: string,
		@Body() updatePatientVaccinationDto: UpdatePatientVaccinationDto
	) {
		try {
			return await this.patientVaccinationsService.update(
				id,
				updatePatientVaccinationDto
			);
		} catch (error) {
			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}
}

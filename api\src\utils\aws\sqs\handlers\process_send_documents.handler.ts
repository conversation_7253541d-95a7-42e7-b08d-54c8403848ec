/* eslint-disable prettier/prettier */
import { Message } from '@aws-sdk/client-sqs';
import {
	forwardRef,
	HttpException,
	HttpStatus,
	Inject,
	Injectable
} from '@nestjs/common';
import { Queue<PERSON>and<PERSON> } from '../interfaces/queue-handler.interface';
import { SendDocuments } from '../../../common/send-document.service';
import { EmrService } from '../../../../emr/emr.service';
import { WinstonLogger } from '../../../logger/winston-logger.service';
import { S3Service } from '../../../aws/s3/s3.service';
import { StatementService } from '../../../../statement/statement.service';

@Injectable()
export class ProcessSendDocumentsHandler implements QueueHandler {
	constructor(
		private readonly sendDocuments: SendDocuments,
		@Inject(forwardRef(() => EmrService))
		private readonly emrService: EmrService,
		private readonly logger: WinstonLogger,
		private readonly s3Service: S3Service,
		@Inject(forwardRef(() => StatementService))
		private readonly statementService: StatementService
	) {}

	async handle(message: Message): Promise<void> {
		const body = JSON.parse(message.Body || '{}');
		const data = body.data;

		this.logger.log('Processing SQS message', {
			messageId: message.MessageId,
			serviceType: data.serviceType,
			data: JSON.stringify(data)
		});

		switch (data.serviceType) {
			case 'sendIndividualDocuments':
				{
					// Process documents for email and whatsapp
					await this.sendDocumentForIndividualAppointment({
						appointmentId: data?.appointmentId,
						shareMode: data?.shareMode,
						documentType: data?.documentType,
						fileKeys: data?.fileKeys,
						type: data?.type,
						email: data?.email,
						phoneNumber: data?.phoneNumber
					});
				}
				break;

			case 'sendReminderNotification':
				{
					await this.sendDocuments.sendReminderNotification(
						data?.reminders
					);
				}
				break;

			case 'sendPrescriptionDocuments':
				{
					await this.sendDocuments.sendUpdatedPrescriptionDocument(
						data?.appointmentId,
						data?.shareingArray,
						data?.type || 'client',
						data?.phoneNumber,
						data?.email
					);
				}
				break;

			case 'createPrescriptionDocuments':
				{
					await this.sendDocuments.createUpdatedPrescriptionPdf(
						data?.appointmentId
					);
				}
				break;

			case 'sendGlobalDocuments':
				{
					const patientId = data?.patientId;
					const shareMode = JSON.parse(data?.shareMode || 'null');
					const documentType = JSON.parse(data?.documentType || '[]');

					try {
						await this.sendDocuments.sendMedicalRecords(
							patientId,
							shareMode,
							documentType
						);
					} catch (err) {
						this.logger.error('failed to send global documents', {
							err,
							patientId
						});
						throw new HttpException(
							err as Error,
							HttpStatus.BAD_REQUEST
						);
					}
				}
				break;

			case 'sendInvoiceTabDocuments':
				{
					const patientId = data?.patientId;
					const shareMode = data?.shareMode;
					const fileKeys = Array.isArray(data?.fileKeys)
						? data.fileKeys
						: [];

					if (fileKeys.length === 0) {
						this.logger.error('No file keys provided', {
							patientId
						});
						throw new HttpException(
							'No file keys provided',
							HttpStatus.BAD_REQUEST
						);
					}

					await this.sendDocuments.sendInvoiceTabDocuments(
						patientId,
						fileKeys,
						JSON.parse(shareMode)
					);
				}
				break;

			case 'sendLedgerDocuments':
				{
					const {
						invoiceReferenceIds,
						paymentIdsArray,
						type,
						email,
						phoneNumber,
						shareMode,
						shareingArray,
						filters
					} = data;

					const invoiceIds =
						invoiceReferenceIds || paymentIdsArray || [];
					const shareModeToUse = shareMode || shareingArray || [];

					await this.sendDocuments.shareledgerDocuments(
						invoiceIds,
						shareModeToUse,
						type,
						email,
						phoneNumber,
						filters
					);
				}
				break;

			case 'storeLedgerDocuments':
				{
					const { invoiceReferenceIds, paymentIdsArray, requestId, filters, userContext } =
						data;
					const invoiceIds =
						invoiceReferenceIds || paymentIdsArray || [];

					await this.sendDocuments.storeLedgerDocuments(
						invoiceIds,
						requestId,
						filters,
						userContext
					);
				}
				break;

			case 'sendVaccinationDocuments':
				{
					const shareMode = data?.shareMode;
					const fileKey = data?.fileKey || '';
					await this.sendDocuments.sendVaccinationFromFileKey(
						fileKey,
						JSON.parse(shareMode),
						data?.type || 'client',
						data?.email,
						data?.phoneNumber
					);
				}
				break;

			case 'diagnosticNotes':
					this.logger.log('Processing diagnostic notes for appointment', data?.appointmentId);
					await this.sendDocuments.sendDiagnosticTabDocument(data?.appointmentId, JSON.parse(data?.shareMode || 'null'), data?.fileKeys || [], data?.type || 'client', data?.email || '');
				break;

			case 'sendPaymentReceipts':
				{
					const {
						paymentReferenceIds,
						type,
						email,
						phoneNumber,
						shareOptions
					} = data;

					const receiptIds = Array.isArray(paymentReferenceIds)
						? paymentReferenceIds
						: [];
					const shareParams = Array.isArray(shareOptions)
						? shareOptions
						: [];

					await this.sendDocuments.sharePaymentReceipts(
						receiptIds,
						shareParams,
						type,
						email,
						phoneNumber
					);
				}
				break;

			case 'storePaymentReceipts':
				{
					const { paymentReferenceIds, requestId } = data;

					const receiptIds = Array.isArray(paymentReferenceIds)
						? paymentReferenceIds
						: [];

					await this.sendDocuments.storePaymentReceipts(
						receiptIds,
						requestId
					);
				}
				break;

			case 'generateAndProcessStatement':
				this.logger.log(
					'Processing statement generation task from SQS',
					{
						requestId: data.requestId
					}
				);
				try {
					await this.statementService.processStatementGenerationTask(
						data
					);
				} catch (error) {
					this.logger.error(
						`Error processing generateAndProcessStatement task for requestId: ${data.requestId}`,
						{
							error,
							requestId: data.requestId
						}
					);
				}
				break;

			default:
				this.logger.warn('Unknown serviceType', {
					serviceType: data.serviceType
				});
				throw new HttpException(
					'Invalid serviceType',
					HttpStatus.BAD_REQUEST
				);
		}
	}

	async sendDocumentForIndividualAppointment(data: {
		appointmentId: string;
		shareMode: string;
		documentType: string;
		fileKeys?: string[] | null;
		type?: 'client' | 'other';
		email?: string;
		phoneNumber?: string;
	}) {
		const appointmentId = data?.appointmentId;
		const shareMode = JSON.parse(data?.shareMode || 'null');
		const documentType = JSON.parse(data?.documentType || '[]');
		const fileKeys = data?.fileKeys || [];
		const recipientType = data?.type;
		const recipientEmail = data?.email;
		const recipientPhone = data?.phoneNumber;

		try {
			// Check if email is included in share mode
			if (shareMode.includes('email')) {
				this.logger.log('Email delivery requested - will process email documents in one go');

				// First collect all documents if email delivery is requested
				// Create a proxy for the sendMail method to collect attachments
				const buffers: Buffer[] = [];
				const fileNames: string[] = [];

				// Save original sendMail method
				const originalSendMail = this.sendDocuments.sendMail;
				let clientEmail = recipientEmail || ''; // Use provided email if available
				let emailBody = '';
				let emailSubject = '';

				// Replace sendMail with a collector function
				this.sendDocuments.sendMail = async (body, docBuffers, docFileNames, email, subject) => {
					// Add validation to ensure we have valid data
					if (Array.isArray(docBuffers) && docBuffers.length > 0 && 
						Array.isArray(docFileNames) && docFileNames.length > 0) {
						buffers.push(...docBuffers);
						fileNames.push(...docFileNames);

						// Store the email details from the first call with valid data
						if (!clientEmail && email) {
							clientEmail = email;
							emailBody = body;
							emailSubject = subject || 'Medical Documents';
						} else if (!emailBody) {
							// Still capture the email body and subject even if we already have an email
							emailBody = body;
							emailSubject = subject || 'Medical Documents';
						}
					} else {
						this.logger.warn('Invalid document buffers or filenames in sendMail call', {
								appointmentId,
							hasDocBuffers: Array.isArray(docBuffers) && docBuffers.length > 0,
							hasDocFileNames: Array.isArray(docFileNames) && docFileNames.length > 0
						});
					}

					// Don't actually send the email yet
					return;
				};

				// Process each document type to collect files
				const processPromises = documentType.map((docType: string) => {
					// If using a custom recipient, pass that information to methods that support it
					const customRecipient = recipientType === 'other' && recipientEmail;

					switch (docType) {
						case 'emr':
							// If custom recipient is specified, use direct method
							if (customRecipient) {
								return this.emrService.sendEmrOnEmail(
									appointmentId,
									'other',
									recipientEmail
								);
							}
							return this.emrService.getIndividualEMR(appointmentId, ['email']);
						case 'invoices':
							return this.sendDocuments.sendInvoiceDocument(appointmentId, ['email'], recipientType, recipientEmail);
						case 'diagnostics':
							return this.sendDocuments.sendDiagnosticDocument(appointmentId, ['email'], recipientType, recipientEmail);
						case 'diagnosticTab':
							return this.sendDocuments.sendDiagnosticTabDocument(appointmentId, ['email'], [], recipientType, recipientEmail);
						case 'diagnosticNotes':
							return this.sendDocuments.sendDiagnosticTabDocument(appointmentId, ['email'], fileKeys, recipientType, recipientEmail);
						case 'vaccinations':
							return this.sendDocuments.sendVaccinationDocument(appointmentId, ['email'], recipientType, recipientEmail);
						default:
							return null;
					}
				});

				await Promise.all(processPromises);

				// Restore original sendMail function
				this.sendDocuments.sendMail = originalSendMail;

				// Now send all documents in one email if we collected any
				if (buffers.length > 0 && clientEmail) {
					this.logger.log(`Sending ${buffers.length} documents in one consolidated email to ${clientEmail}`);
					await this.sendDocuments.sendMail(
						emailBody,
						buffers,
						fileNames,
						clientEmail,
						emailSubject
					);
				} else {
					this.logger.warn('No documents collected for email', {
						appointmentId,
						hasBuffers: buffers.length > 0,
						hasClientEmail: !!clientEmail
					});
				}
			}

			// If WhatsApp is also requested, process it separately
			if (shareMode.includes('whatsapp')) {
				await this.processDocumentsForWhatsApp(documentType, appointmentId, fileKeys, recipientType, recipientPhone);
			}
		} catch (err) {
			this.logger.error('Failed to process documents', { err, appointmentId });
			throw new HttpException(err as Error, HttpStatus.BAD_REQUEST);
		}
	}

	// Helper method to process documents for WhatsApp
	private async processDocumentsForWhatsApp(
		documentType: string[],
		appointmentId: string,
		fileKeys: string[] = [],
		recipientType?: 'client' | 'other',
		recipientPhone?: string
	) {
		// For whatsapp processing with custom recipient if provided
		const customRecipient = recipientType === 'other' && recipientPhone;

		return Promise.all(
			documentType.map((docType: string) => {
				switch (docType) {
					case 'emr':
						// If there's a custom recipient, use the appropriate logic
						if (customRecipient) {
							// The EMR service has methods to handle custom recipients
							return this.emrService.sendEmrOnWhatsapp(appointmentId, 'other', recipientPhone);
						}
						return this.emrService.getIndividualEMR(appointmentId, ['whatsapp']);
					case 'invoices':
						return this.sendDocuments.sendInvoiceDocument(appointmentId, ['whatsapp'], recipientType, undefined, recipientPhone);
					case 'diagnostics':
						return this.sendDocuments.sendDiagnosticDocument(appointmentId, ['whatsapp'], recipientType, undefined, recipientPhone);
					case 'diagnosticTab':
						return this.sendDocuments.sendDiagnosticTabDocument(appointmentId, ['whatsapp'], [], recipientType, undefined, recipientPhone);
					case 'diagnosticNotes':
						return this.sendDocuments.sendDiagnosticTabDocument(appointmentId, ['whatsapp'], fileKeys, recipientType, undefined, recipientPhone);
					case 'vaccinations':
						return this.sendDocuments.sendVaccinationDocument(appointmentId, ['whatsapp'], recipientType, undefined, recipientPhone);
					default:
						return null;
				}
			})
		);
	}
}

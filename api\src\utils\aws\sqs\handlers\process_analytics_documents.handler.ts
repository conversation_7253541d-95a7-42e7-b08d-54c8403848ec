import { Message } from '@aws-sdk/client-sqs';
import {
  forwardRef,
  HttpException,
  HttpStatus,
  Inject,
  Injectable
} from '@nestjs/common';
import { QueueHandler } from '../interfaces/queue-handler.interface';
import { <PERSON><PERSON><PERSON><PERSON> } from '../../../logger/winston-logger.service';
import { AnalyticsDocumentGenerationService } from '../../../analytics-sharing/services/analytics-document-generation.service';

@Injectable()
export class ProcessAnalyticsDocumentsHandler implements QueueHandler {
  constructor(
    @Inject(forwardRef(() => AnalyticsDocumentGenerationService))
    private readonly documentGenerationService: AnalyticsDocumentGenerationService,
    private readonly logger: WinstonLogger
  ) {}

  async handle(message: Message): Promise<void> {
    const body = JSON.parse(message.Body || '{}');
    const data = body.data;

    this.logger.log('Processing Analytics Documents SQS message', {
      messageId: message.MessageId,
      serviceType: data.serviceType,
      requestId: data.requestId
    });

    try {
      switch (data.serviceType) {
        case 'generateAnalyticsDocuments':
          await this.documentGenerationService.processDocumentGeneration(data);
          break;

        default:
          this.logger.warn('Unknown service type for analytics documents', {
            serviceType: data.serviceType,
            messageId: message.MessageId
          });
          break;
      }
    } catch (error) {
      this.logger.error('Error processing analytics documents message', {
        error: error.message,
        stack: error.stack,
        messageId: message.MessageId,
        requestId: data.requestId
      });

      // Update request status to failed
      if (data.requestId) {
        try {
          await this.documentGenerationService.updateRequestStatus(
            data.requestId,
            'failed',
            undefined,
            undefined,
            error.message
          );
        } catch (updateError) {
          this.logger.error('Failed to update request status to failed', {
            requestId: data.requestId,
            error: updateError.message
          });
        }
      }

      throw new HttpException(
        'Failed to process analytics documents',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}

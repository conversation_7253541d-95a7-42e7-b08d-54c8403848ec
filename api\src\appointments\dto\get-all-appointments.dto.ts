import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString, Min } from 'class-validator';
import { Transform } from 'class-transformer';

export class GetAllAppointmentsDto {
	@ApiProperty({
		description: 'Page number for pagination',
		example: 1,
		required: false,
		default: 1
	})
	@IsOptional()
	@Transform(({ value }) => parseInt(value, 10))
	@IsNumber()
	@Min(1)
	page: number = 1;

	@ApiProperty({
		description: 'Number of items per page',
		example: 10,
		required: false,
		default: 10
	})
	@IsOptional()
	@Transform(({ value }) => parseInt(value, 10))
	@IsNumber()
	@Min(1)
	limit: number = 10;

	@ApiProperty({
		description: 'Order of sorting',
		example: 'DESC',
		required: false,
		default: 'DESC'
	})
	@IsOptional()
	@IsString()
	orderBy: string = 'DESC';

	@ApiProperty({
		description: 'Date for filtering appointments',
		example: '2024-07-23',
		required: false
	})
	@IsOptional()
	@IsString()
	date: string = '';

	@ApiProperty({
		description: 'Search term for filtering appointments',
		example: '<PERSON> Do<PERSON>',
		required: false
	})
	@IsOptional()
	@IsString()
	search: string = '';

	@ApiProperty({
		description: 'Comma-separated list of doctor IDs',
		example: 'uuid1,uuid2',
		required: false
	})
	@IsOptional()
	@IsString()
	doctors: string = '';

	@ApiProperty({
		description: 'Status for filtering appointments',
		example: 'confirmed',
		required: false
	})
	@IsOptional()
	@IsString()
	status: string = '';

	@ApiProperty({
		description: 'Filter for primary doctor appointments',
		example: 'false',
		required: false,
		default: 'false'
	})
	@IsOptional()
	@IsString()
	onlyPrimary: string = 'false';
}

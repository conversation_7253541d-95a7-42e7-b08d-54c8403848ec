import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, Length } from 'class-validator';

export class CreateBrandDto {
	@ApiProperty({
		description: 'The name of the brand.',
		example: 'Apple',
		minLength: 1,
		maxLength: 50,
	})
	@IsNotEmpty({ message: 'Brand name is required' })
	@IsString({ message: 'Brand name must be a string' })
	@Length(1, 50, { message: 'Brand name must be between 1 and 50 characters' })
	name!: string;
}

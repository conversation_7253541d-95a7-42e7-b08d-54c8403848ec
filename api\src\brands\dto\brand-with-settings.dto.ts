import { ApiProperty } from '@nestjs/swagger';
import { Expose, Transform, Type } from 'class-transformer';

/**
 * Simple DTO for clinic information including contact details
 */
export class ClinicSummaryDto {
	@ApiProperty({ description: 'The unique identifier of the clinic' })
	@Expose()
	id!: string;

	@ApiProperty({ description: 'The name of the clinic' })
	@Expose()
	name!: string;

	@ApiProperty({
		description: 'Phone numbers of the clinic',
		type: 'array',
		items: {
			type: 'object',
			properties: {
				country_code: { type: 'string' },
				number: { type: 'string' }
			}
		}
	})
	@Expose()
	@Transform(({ value }) => value || [])
	phoneNumbers!: Array<{ country_code: string; number: string }>;
}

/**
 * DTO for Brand response with additional client booking settings information
 * Contains only essential brand information plus the client booking flag
 */
export class BrandWithSettingsDto {
	@ApiProperty({ description: 'The unique identifier of the brand' })
	@Expose()
	id!: string;

	@ApiProperty({ description: 'The name of the brand' })
	@Expose()
	name!: string;

	@ApiProperty({ description: 'URL-friendly slug for the brand' })
	@Expose()
	slug!: string;

	@ApiProperty({ description: 'When the brand was created' })
	@Expose()
	createdAt!: Date;

	@ApiProperty({ description: 'When the brand was last updated' })
	@Expose()
	updatedAt!: Date;

	@ApiProperty({
		description: 'User ID who created the brand',
		nullable: true
	})
	@Expose()
	createdBy!: string | null;

	@ApiProperty({
		description: 'User ID who last updated the brand',
		nullable: true
	})
	@Expose()
	updatedBy!: string | null;

	@ApiProperty({
		description:
			'Indicates if at least one clinic in the brand has client booking enabled'
	})
	@Expose()
	hasClientBookingEnabled!: boolean;

	@ApiProperty({
		description:
			'Summary information about clinics associated with this brand',
		type: [ClinicSummaryDto],
		nullable: true
	})
	@Expose()
	@Type(() => ClinicSummaryDto)
	@Transform(({ obj }) => {
		// Transform the clinics relation to ClinicSummaryDto structure
		if (obj.clinics && obj.clinics.length > 0) {
			return obj.clinics.map((clinic: any) => ({
				id: clinic.id,
				name: clinic.name,
				phoneNumbers: clinic.phoneNumbers || []
			}));
		}
		return undefined;
	})
	clinics?: ClinicSummaryDto[];
}

import 'reflect-metadata';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { CreatePatientVaccinationDto } from './create-patient-vaccination.dto';

describe('create_patient-vaccinations-dto', () => {
	it('should validate successfully', async () => {
		const dto = new CreatePatientVaccinationDto();

		dto.patientId = 'b1e57271-d5d6-4ef7-bc2f-bb0b0cf0e5db';
		(dto.appointmentId = 'b1e57271-d5d6-4ef7-bc2f-bb0b0cf0e5db'),
			(dto.doctorName = 'Dr. Suresh'),
			(dto.reportUrl = 'www.url.com'),
			(dto.systemGenerated = false),
			(dto.urlMeta = { file: 'abc', type: 'image' }),
			(dto.vaccinationDate = new Date()),
			(dto.vaccineName = 'covaccine');

		const errors = await validate(dto);

		expect(errors).toHaveLength(0);
	});

	it('should validate successfully with date string from frontend', async () => {
		const plainObject = {
			patientId: 'b1e57271-d5d6-4ef7-bc2f-bb0b0cf0e5db',
			appointmentId: 'b1e57271-d5d6-4ef7-bc2f-bb0b0cf0e5db',
			doctorName: 'Dr. Suresh',
			reportUrl: 'www.url.com',
			systemGenerated: false,
			urlMeta: { file: 'abc', type: 'image' },
			vaccinationDate: '2025-06-30T18:30:00.000Z', // String as sent from frontend
			vaccineName: 'covaccine'
		};

		const dto = plainToClass(CreatePatientVaccinationDto, plainObject);
		const errors = await validate(dto);

		expect(errors).toHaveLength(0);
		expect(dto.vaccinationDate).toBeInstanceOf(Date);
	});

	it('should give validation error if patientId, vaccinationDate or vaccineName is not given', async () => {
		const dto = new CreatePatientVaccinationDto();

		(dto.appointmentId = 'b1e57271-d5d6-4ef7-bc2f-bb0b0cf0e5db'),
			(dto.doctorName = 'Dr. Suresh'),
			(dto.reportUrl = 'www.url.com'),
			(dto.systemGenerated = false),
			(dto.urlMeta = { file: 'abc', type: 'image' }),
			(dto.vaccineName = 'covaccine');

		const errors = await validate(dto);

		expect(errors).toHaveLength(2);
	});
});

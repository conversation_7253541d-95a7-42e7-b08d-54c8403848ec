import { Test, TestingModule } from '@nestjs/testing';
import { PatientVaccinationsController } from './patient-vaccinations.controller';
import { PatientVaccinationsService } from './patient-vaccinations.service';
import { CreatePatientVaccinationDto } from './dto/create-patient-vaccination.dto';
import { PatientVaccination } from './entities/patient-vaccinations.entity';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { Patient } from '../patients/entities/patient.entity';
import { HttpException, HttpStatus } from '@nestjs/common';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { UpdatePatientVaccinationDto } from './dto/update-patient-vaccination.dto';
import { ClinicVaccinationEntity } from '../clinic-vaccinations/entities/clinic-vaccination.entity';

describe('PatientVaccinationsController', () => {
	let controller: PatientVaccinationsController;
	let patientVaccinationService: jest.Mocked<PatientVaccinationsService>;

	const mockCreatePatientVaccinationDto: CreatePatientVaccinationDto = {
		patientId: 'p_1',
		systemGenerated: true,
		vaccinationDate: new Date(),
		vaccineName: 'co-vaccine'
	};

	const mockPatientVaccination: PatientVaccination = {
		id: 'u_1',
		patientId: 'p_1',
		vaccinationId: 'v_1',
		systemGenerated: true,
		vaccinationDate: new Date(),
		vaccineName: 'co-vaccine',
		createdAt: new Date(),
		updatedAt: new Date(),
		appointment: {} as AppointmentEntity,
		reportUrl: 'some_url.com',
		urlMeta: { file: 'file A', type: 'image' },
		patient: {} as Patient,
		vaccineId: 'v_1',
		vaccination: {} as ClinicVaccinationEntity,
		removedFromInvoice: false
	};

	const mockUpdatePatientVaccinationDto: UpdatePatientVaccinationDto = {
		vaccineName: 'cova-shield'
	};

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [PatientVaccinationsController],
			providers: [
				{
					provide: PatientVaccinationsService,
					useValue: {
						create: jest.fn(),
						get: jest.fn(),
						update: jest.fn()
					}
				},
				{
					provide: WinstonLogger,
					useValue: {
						log: jest.fn(),
						error: jest.fn()
					}
				}
			]
		}).compile();

		controller = module.get<PatientVaccinationsController>(
			PatientVaccinationsController
		);
		patientVaccinationService = module.get(PatientVaccinationsService);
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	describe('create paient-vaccinations', () => {
		it('should be defined', () => {
			expect(controller.create).toBeDefined();
		});

		it('should create patient-vaccinations', async () => {
			patientVaccinationService.create.mockResolvedValue(
				mockPatientVaccination
			);

			const response = await controller.create(
				mockCreatePatientVaccinationDto
			);

			expect(patientVaccinationService.create).toHaveBeenCalled();
			expect(response).toEqual(mockPatientVaccination);
		});

		it('should throw HttpException with BAD_REQUEST status when service throws error', async () => {
			const serviceError = new Error('Service error message');
			patientVaccinationService.create.mockRejectedValue(serviceError);

			await expect(
				controller.create(mockCreatePatientVaccinationDto)
			).rejects.toThrow(
				new HttpException(
					'Service error message',
					HttpStatus.BAD_REQUEST
				)
			);
		});
	});

	describe('get paient-vaccinations', () => {
		it('should be defined', () => {
			expect(controller.get).toBeDefined();
		});

		it('should return patient-vaccinations', async () => {
			const mockPatientId = 'p_1';
			patientVaccinationService.get.mockResolvedValue([
				mockPatientVaccination
			]);

			const response = await controller.get(mockPatientId);

			expect(patientVaccinationService.get).toHaveBeenCalledWith(
				mockPatientId
			);
			expect(response).toEqual([mockPatientVaccination]);
		});

		it('should throw HttpException with BAD_REQUEST status when service throws error', async () => {
			const mockPatientId = 'p_1';
			const serviceError = new Error('Get service error');
			patientVaccinationService.get.mockRejectedValue(serviceError);

			await expect(controller.get(mockPatientId)).rejects.toThrow(
				new HttpException('Get service error', HttpStatus.BAD_REQUEST)
			);
		});
	});

	describe('update patient-vaccinations', () => {
		it('should be defined', () => {
			expect(controller.update).toBeDefined();
		});

		it('should update the patient_vaccinations', async () => {
			const mockId: string = 'm_1';
			const updatedPatientVaccination = {
				...mockPatientVaccination,
				...mockUpdatePatientVaccinationDto
			};

			patientVaccinationService.update.mockResolvedValue(
				updatedPatientVaccination
			);

			const response = await controller.update(
				mockId,
				mockUpdatePatientVaccinationDto
			);

			expect(patientVaccinationService.update).toHaveBeenCalledWith(
				mockId,
				mockUpdatePatientVaccinationDto
			);
			expect(response).toEqual(updatedPatientVaccination);
		});

		it('should throw HttpException with BAD_REQUEST status when service throws error', async () => {
			const mockId: string = 'm_1';
			const serviceError = new Error('Update service error');
			patientVaccinationService.update.mockRejectedValue(serviceError);

			await expect(
				controller.update(mockId, mockUpdatePatientVaccinationDto)
			).rejects.toThrow(
				new HttpException(
					'Update service error',
					HttpStatus.BAD_REQUEST
				)
			);
		});
	});
});

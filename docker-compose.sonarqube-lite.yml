version: "3.8"

services:
  sonarqube:
    image: sonarqube:lts-community
    container_name: sonarqube-server-lite
    depends_on:
      - sonarqube-db
    environment:
      SONAR_JDBC_URL: *****************************************
      SONAR_JDBC_USERNAME: sonar
      SONAR_JDBC_PASSWORD: sonar
      SONAR_ES_BOOTSTRAP_CHECKS_DISABLE: true
      # Additional settings for macOS compatibility
      SONAR_SEARCH_JAVAADDITIONALOPTS: "-Dbootstrap.system_call_filter=false -Xms512m -Xmx2g"
      SONAR_WEB_JAVAADDITIONALOPTS: "-Xms256m -Xmx1g"
      SONAR_CE_JAVAADDITIONALOPTS: "-Xms256m -Xmx1g"
      # Allow anonymous access for local development
      SONAR_FORCEAUTHENTICATION: false
    volumes:
      - sonarqube_data_lite:/opt/sonarqube/data
      - sonarqube_extensions_lite:/opt/sonarqube/extensions
      - sonarqube_logs_lite:/opt/sonarqube/logs
    ports:
      - "9000:9000"
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
    restart: unless-stopped
    # Add memory limits for better resource management
    mem_limit: 3g
    memswap_limit: 3g

  sonarqube-db:
    image: postgres:13
    container_name: sonarqube-postgres-lite
    environment:
      POSTGRES_USER: sonar
      POSTGRES_PASSWORD: sonar
      POSTGRES_DB: sonar
    volumes:
      - sonarqube_postgresql_lite:/var/lib/postgresql
      - sonarqube_postgresql_data_lite:/var/lib/postgresql/data
    ports:
      - "5433:5432" # Use port 5433 externally to avoid conflict
    restart: unless-stopped
    # Add memory limits
    mem_limit: 512m

  # Scanner service for API (NestJS/TypeScript)
  sonar-scanner-api:
    image: sonarsource/sonar-scanner-cli:latest
    container_name: sonar-scanner-api-lite
    depends_on:
      - sonarqube
    environment:
      SONAR_HOST_URL: http://sonarqube:9000
    volumes:
      - ./api:/usr/src/api
      - ./sonar-configs:/usr/src/configs
    working_dir: /usr/src/api
    profiles:
      - scan
    command: >
      sh -c "
        echo 'Waiting for SonarQube to be ready...' &&
        for i in {1..30}; do
          if curl -s http://sonarqube:9000/api/system/status | grep -q 'UP'; then
            echo 'SonarQube is ready, starting API scan...'
            break
          fi
          echo 'Waiting for SonarQube... attempt $$i/30'
          sleep 10
          if [ $$i -eq 30 ]; then
            echo 'SonarQube did not become ready in time. Proceeding anyway...'
          fi
        done &&
        sonar-scanner -Dsonar.projectKey=nidana_api -Dsonar.projectName='Nidana API (Local)' -Dsonar.projectVersion=1.0 -Dsonar.sources=src -Dsonar.exclusions='**/test/**,**/migrations/**,**/notifications/**,**/lib/**,**/seeders/**,**/manualseeder/**,**/node_modules/**,**/coverage/**,**/dist/**,**/build/**' -Dsonar.typescript.lcov.reportPaths=coverage/lcov.info -Dsonar.coverage.exclusions='**/test/**,**/migrations/**,**/notifications/**,**/lib/**,**/seeders/**,**/manualseeder/**,**/scripts/**,**/utils/**,**/config/**' -Dsonar.cpd.exclusions='**/test/**,**/migrations/**,**/notifications/**,**/lib/**,**/seeders/**,**/manualseeder/**' -Dsonar.sourceEncoding=UTF-8 -Dsonar.host.url=http://sonarqube:9000 -Dsonar.login=sqp_61c5fb588e340ffd4b69f83157908e52393e9f3d
      "

  # Scanner service for UI (Next.js)
  sonar-scanner-ui:
    image: sonarsource/sonar-scanner-cli:latest
    container_name: sonar-scanner-ui-lite
    depends_on:
      - sonarqube
    environment:
      SONAR_HOST_URL: http://sonarqube:9000
    volumes:
      - ./ui:/usr/src/ui
      - ./sonar-configs:/usr/src/configs
    working_dir: /usr/src/ui
    profiles:
      - scan
    command: >
      sh -c "
        echo 'Waiting for SonarQube to be ready...' &&
        for i in {1..30}; do
          if curl -s http://sonarqube:9000/api/system/status | grep -q 'UP'; then
            echo 'SonarQube is ready, starting UI scan...'
            break
          fi
          echo 'Waiting for SonarQube... attempt $$i/30'
          sleep 10
          if [ $$i -eq 30 ]; then
            echo 'SonarQube did not become ready in time. Proceeding anyway...'
          fi
        done &&
        sonar-scanner -Dsonar.projectKey=nidana_ui -Dsonar.projectName='Nidana UI (Local)' -Dsonar.projectVersion=1.0 -Dsonar.sources=app,context -Dsonar.exclusions='**/node_modules/**,**/.next/**,**/out/**,**/build/**,**/stories/**,**/ui-tests/**,**/public/**,**/*.spec.ts,**/*.spec.tsx,**/*.test.ts,**/*.test.tsx' -Dsonar.sourceEncoding=UTF-8 -Dsonar.host.url=http://sonarqube:9000 -Dsonar.login=sqp_7ecc5d2582423cfdf8d1d5a3d7324d4a1b78045e
      "

  # Scanner service for Patient Portal (Next.js)
  sonar-scanner-patientportal:
    image: sonarsource/sonar-scanner-cli:latest
    container_name: sonar-scanner-patientportal-lite
    depends_on:
      - sonarqube
    environment:
      SONAR_HOST_URL: http://sonarqube:9000
    volumes:
      - ./patientportal:/usr/src/patientportal
      - ./sonar-configs:/usr/src/configs
    working_dir: /usr/src/patientportal
    profiles:
      - scan
    command: >
      sh -c "
        echo 'Waiting for SonarQube to be ready...' &&
        for i in {1..30}; do
          if curl -s http://sonarqube:9000/api/system/status | grep -q 'UP'; then
            echo 'SonarQube is ready, starting Patient Portal scan...'
            break
          fi
          echo 'Waiting for SonarQube... attempt $$i/30'
          sleep 10
          if [ $$i -eq 30 ]; then
            echo 'SonarQube did not become ready in time. Proceeding anyway...'
          fi
        done &&
        sonar-scanner -Dsonar.projectKey=nidana-patientportal-local -Dsonar.projectName='Nidana Patient Portal (Local)' -Dsonar.projectVersion=1.0 -Dsonar.sources=app -Dsonar.exclusions='**/node_modules/**,**/.next/**,**/out/**,**/build/**,**/public/**,**/env/**,**/*.spec.ts,**/*.spec.tsx,**/*.test.ts,**/*.test.tsx' -Dsonar.sourceEncoding=UTF-8 -Dsonar.host.url=http://sonarqube:9000 -Dsonar.login=sqp_61c5fb588e340ffd4b69f83157908e52393e9f3d
      "

volumes:
  sonarqube_data_lite:
  sonarqube_extensions_lite:
  sonarqube_logs_lite:
  sonarqube_postgresql_lite:
  sonarqube_postgresql_data_lite:

networks:
  default:
    name: sonarqube-network-lite

import {
  Controller,
  Post,
  Body,
  UseGuards,
  Request,
  Get,
  Param,
  Query
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '../auth/enums/role.enum';
import { AnalyticsSharingService } from './analytics-sharing.service';
import { ShareAnalyticsDocumentsDto } from './dto/share-analytics-documents.dto';

@ApiTags('Analytics Sharing')
@Controller('analytics-sharing')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class AnalyticsSharingController {
  constructor(
    private readonly analyticsSharingService: AnalyticsSharingService
  ) {}

  @Post('share-documents')
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Share analytics documents via email/WhatsApp' })
  @ApiResponse({
    status: 201,
    description: 'Document sharing request created successfully'
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request parameters'
  })
  async shareDocuments(
    @Body() dto: ShareAnalyticsDocumentsDto,
    @Request() req: any
  ) {
    const userId = req.user.id;
    return await this.analyticsSharingService.initiateDocumentSharing(
      dto,
      userId
    );
  }

  @Get('requests/:requestId/status')
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Get document sharing request status' })
  @ApiResponse({
    status: 200,
    description: 'Request status retrieved successfully'
  })
  async getRequestStatus(@Param('requestId') requestId: string) {
    return await this.analyticsSharingService.getRequestStatus(requestId);
  }

  @Get('requests')
  @Roles(Role.ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({ summary: 'Get document sharing requests for clinic' })
  @ApiResponse({
    status: 200,
    description: 'Requests retrieved successfully'
  })
  async getRequests(
    @Query('clinicId') clinicId: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10
  ) {
    return await this.analyticsSharingService.getRequestsForClinic(
      clinicId,
      page,
      limit
    );
  }
}

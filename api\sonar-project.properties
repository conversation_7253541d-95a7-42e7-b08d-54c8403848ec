# SonarQube project configuration for Nidana API (NestJS/TypeScript)
sonar.projectKey=nidana-api-local
sonar.projectName=Nidana API (Local)
sonar.projectVersion=1.0

# Source code configuration
sonar.sources=src
sonar.sourceEncoding=UTF-8

# Exclusions - files/directories to exclude from analysis
sonar.exclusions=**/test/**,\
                 **/migrations/**,\
                 **/notifications/**,\
                 **/lib/**,\
                 **/seeders/**,\
                 **/manualseeder/**,\
                 **/node_modules/**,\
                 **/dist/**,\
                 **/build/**,\
                 **/scripts/**,\
                 **/*.spec.ts,\
                 **/*.test.ts,\
                 **/Dockerfile,\
                 **/newrelic.js

# Coverage exclusions - files to exclude from coverage calculation
sonar.coverage.exclusions=**/test/**,\
                          **/migrations/**,\
                          **/notifications/**,\
                          **/lib/**,\
                          **/seeders/**,\
                          **/manualseeder/**,\
                          **/scripts/**,\
                          **/main.ts,\
                          **/cron.ts,\
                          **/sqs.ts,\
                          **/*.module.ts,\
                          **/*.spec.ts,\
                          **/*.test.ts

# Duplication exclusions - files to exclude from duplication detection
sonar.cpd.exclusions=**/test/**,\
                     **/migrations/**,\
                     **/notifications/**,\
                     **/lib/**,\
                     **/seeders/**,\
                     **/manualseeder/**,\
                     **/scripts/**

# TypeScript/JavaScript specific settings
sonar.typescript.lcov.reportPaths=coverage/lcov.info
sonar.javascript.lcov.reportPaths=coverage/lcov.info

# Test execution reports (if available)
# sonar.testExecutionReportPaths=coverage/test-reporter.xml

# SonarQube server configuration
sonar.host.url=http://localhost:9000

# Language settings
sonar.language=ts

# Quality gate settings
sonar.qualitygate.wait=true

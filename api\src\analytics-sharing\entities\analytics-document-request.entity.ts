import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index
} from 'typeorm';
import { AnalyticsDocumentType, ShareMode, RecipientType } from '../dto/analytics-document-types.enum';

@Entity('analytics_document_requests')
@Index(['clinicId', 'requestId'])
@Index(['clinicId', 'status'])
export class AnalyticsDocumentRequest {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  requestId: string;

  @Column({ type: 'varchar', length: 255 })
  clinicId: string;

  @Column({ type: 'varchar', length: 255 })
  userId: string;

  @Column({ type: 'date' })
  startDate: Date;

  @Column({ type: 'date' })
  endDate: Date;

  @Column({ type: 'simple-array' })
  documentTypes: AnalyticsDocumentType[];

  @Column({ type: 'simple-array' })
  shareMode: ShareMode[];

  @Column({ type: 'enum', enum: RecipientType })
  recipientType: RecipientType;

  @Column({ type: 'varchar', length: 255, nullable: true })
  email?: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  phoneNumber?: string;

  @Column({ type: 'boolean', default: true })
  includeExcelReport: boolean;

  @Column({ 
    type: 'enum', 
    enum: ['pending', 'processing', 'completed', 'failed'],
    default: 'pending'
  })
  status: 'pending' | 'processing' | 'completed' | 'failed';

  @Column({ type: 'text', nullable: true })
  errorMessage?: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  s3DocumentKey?: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  s3ExcelKey?: string;

  @Column({ type: 'timestamp', nullable: true })
  expiresAt?: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

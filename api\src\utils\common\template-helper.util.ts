// Note: getClientBookingUrl is still available for email templates that need full URLs
// WhatsApp templates now use only brand slugs for centralized URL handling

/**
 * Helper function to determine if client booking should be enabled
 * and returns the appropriate value for WhatsApp templates
 *
 * @param clinic The clinic entity with customRule and brand
 * @returns Object with isClientBookingEnabled and clientBookingUrl
 *
 * Important: For WhatsApp templates, clientBookingUrl contains only the brand slug.
 * The actual URL construction is handled by nginx proxy using the format:
 * https://nidana.io/[template-specific-path]?brand=[slug]&[additional-params]
 *
 * For email templates, continue using getClientBookingUrl() for full URLs.
 */
export function getClientBookingSettings(clinic: any): {
	isClientBookingEnabled: boolean;
	clientBookingUrl?: string;
} {
	// Check if client booking settings are enabled for the clinic
	const isClientBookingEnabled =
		!!clinic?.customRule?.clientBookingSettings?.isEnabled;

	// If enabled, return the brand slug for WhatsApp templates
	// nginx proxy will construct the full URL based on the template context
	if (isClientBookingEnabled && clinic?.brand?.slug) {
		const clientBookingUrl = clinic.brand.slug; // Brand slug only for WhatsApp
		return { isClientBookingEnabled, clientBookingUrl };
	}

	// Either not enabled or no slug available
	return { isClientBookingEnabled: false };
}

/**
 * Helper function to select appropriate template based on client booking settings
 *
 * @param clinic The clinic entity to check settings for
 * @param baseArgs The base arguments for the template
 * @param standardTemplate Function to generate the standard template (no booking link)
 * @param clinicLinkTemplate Function to generate the clinic link template (with booking capability)
 * @returns The appropriate template data
 *
 * Note: For WhatsApp templates, the client_booking_URL parameter will contain only the brand slug.
 * The WhatsApp template system and nginx proxy handle the full URL construction.
 */
export function selectTemplate<T>(
	clinic: any,
	baseArgs: T,
	standardTemplate: (args: T) => any,
	clinicLinkTemplate: (args: T & { client_booking_URL: string }) => any
): any {
	console.log('clinic', clinic);
	console.log('baseArgs', baseArgs);
	console.log('standardTemplate', standardTemplate);
	console.log('clinicLinkTemplate', clinicLinkTemplate);

	const { isClientBookingEnabled, clientBookingUrl } =
		getClientBookingSettings(clinic);

	if (isClientBookingEnabled && clientBookingUrl) {
		return clinicLinkTemplate({
			...(baseArgs as any),
			client_booking_URL: clientBookingUrl // Brand slug for WhatsApp, full URL for email
		});
	}

	return standardTemplate(baseArgs);
}

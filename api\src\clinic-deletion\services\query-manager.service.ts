import { Injectable } from '@nestjs/common';
import { DeletionType } from '../dto/clinic-deletion.dto';

export interface QueryDefinition {
	sql: string;
	params: any[];
	description?: string;
	dependencies?: string[];
	requiresConstraintHandling?: boolean;
}

export interface QuerySet {
	[tableName: string]: QueryDefinition;
}

/**
 * Centralized query manager for all clinic deletion operations
 * This ensures consistency across impact analysis, backup, and deletion operations
 */
@Injectable()
export class QueryManagerService {
	/**
	 * Get database queries for impact analysis (COUNT queries)
	 */
	getDatabaseAnalysisQueries(
		targetType: DeletionType,
		targetId: string
	): QuerySet {
		const queries: QuerySet = {};

		// Get all table definitions and convert to COUNT queries
		const tableQueries = this.getDatabaseTableQueries(targetType, targetId);

		for (const [tableName, query] of Object.entries(tableQueries)) {
			// Skip no-op queries entirely to maintain consistency with deletion queries
			if (this.isNoOpQuery(query.sql)) {
				// Skip no-op queries - they won't be analyzed or deleted
				// This ensures analysis and deletion report the same table counts
				continue;
			}

			let analysisQuery = query;

			// For tables with constraint handling, use the same logic as backup for consistency
			if (
				query.requiresConstraintHandling &&
				tableName === 'chat_rooms'
			) {
				// Use original JOIN-based logic for analysis to match backup behavior
				analysisQuery = {
					sql:
						targetType === DeletionType.CLINIC
							? `SELECT DISTINCT cr.* FROM chat_rooms cr
							JOIN chat_room_users cru ON cr.id = cru.chat_room_id
							JOIN clinic_users cu ON cru.user_id = cu.id
							WHERE cu.clinic_id = $1`
							: `SELECT DISTINCT cr.* FROM chat_rooms cr
							JOIN chat_room_users cru ON cr.id = cru.chat_room_id
							JOIN clinic_users cu ON cru.user_id = cu.id
							WHERE cu.clinic_id IN (
								SELECT id FROM clinics WHERE brand_id = $1
							)`,
					params: [targetId],
					description:
						'Chat rooms with users from clinic being deleted',
					dependencies: query.dependencies
				};
			}

			// Convert SELECT ... to SELECT COUNT(*) AS count for analysis
			// Handle various SELECT patterns: SELECT *, SELECT ad.*, SELECT DISTINCT cr.*, etc.
			// Preserve DISTINCT behavior when converting to COUNT
			const hasDistinct = /^SELECT\s+DISTINCT/i.test(analysisQuery.sql);
			let countSql;

			if (hasDistinct) {
				// For DISTINCT queries, extract the main table alias and use COUNT(DISTINCT alias.id)
				const aliasMatch = /FROM\s+(\w+)\s+(\w+)/i.exec(
					analysisQuery.sql
				);
				const tableAlias = aliasMatch
					? aliasMatch[2]
					: tableName.substring(0, 2);
				countSql = analysisQuery.sql.replace(
					/^SELECT\s+DISTINCT\s+[^F]+FROM/i,
					`SELECT COUNT(DISTINCT ${tableAlias}.id) AS count FROM`
				);
			} else {
				countSql = analysisQuery.sql.replace(
					/^SELECT\s+[^F]+FROM/i,
					'SELECT COUNT(*) AS count FROM'
				);
			}

			queries[tableName] = {
				sql: countSql,
				params: analysisQuery.params,
				description: analysisQuery.description,
				dependencies: analysisQuery.dependencies
			};
		}

		return queries;
	}

	/**
	 * Get database queries for backup operations (SELECT * queries)
	 * Excludes no-op queries to maintain consistency with analysis and deletion
	 */
	getDatabaseBackupQueries(
		targetType: DeletionType,
		targetId: string
	): QuerySet {
		const tableQueries = this.getDatabaseTableQueries(targetType, targetId);
		const queries: QuerySet = {};

		for (const [tableName, query] of Object.entries(tableQueries)) {
			// Skip no-op queries to maintain consistency with analysis and deletion
			if (this.isNoOpQuery(query.sql)) {
				continue;
			}

			// For tables with constraint handling, use original identification logic for backup
			if (
				query.requiresConstraintHandling &&
				tableName === 'chat_rooms'
			) {
				// Use original JOIN-based logic for backup to identify chat rooms before deletion
				const backupQuery = {
					sql:
						targetType === DeletionType.CLINIC
							? `SELECT DISTINCT cr.* FROM chat_rooms cr
							JOIN chat_room_users cru ON cr.id = cru.chat_room_id
							JOIN clinic_users cu ON cru.user_id = cu.id
							WHERE cu.clinic_id = $1`
							: `SELECT DISTINCT cr.* FROM chat_rooms cr
							JOIN chat_room_users cru ON cr.id = cru.chat_room_id
							JOIN clinic_users cu ON cru.user_id = cu.id
							WHERE cu.clinic_id IN (
								SELECT id FROM clinics WHERE brand_id = $1
							)`,
					params: [targetId],
					description:
						'Chat rooms with users from clinic being deleted',
					dependencies: query.dependencies
				};

				queries[tableName] = backupQuery;
			} else {
				queries[tableName] = query;
			}
		}

		return queries;
	}

	/**
	 * Get database queries for deletion operations (DELETE queries)
	 */
	getDatabaseDeletionQueries(
		targetType: DeletionType,
		targetId: string
	): QuerySet {
		const tableQueries = this.getDatabaseTableQueries(targetType, targetId);
		const deletionQueries: QuerySet = {};

		// Convert SELECT queries to DELETE queries in proper order
		const deletionOrder = this.getDeletionOrder();

		for (const tableName of deletionOrder) {
			if (tableQueries[tableName]) {
				const selectQuery = tableQueries[tableName];

				// Check if this is a no-op query that should be skipped
				if (this.isNoOpQuery(selectQuery.sql)) {
					// Skip no-op queries - they don't need deletion statements
					// This handles cases like "SELECT 1 WHERE 1 = 0" for users table during clinic deletion
					continue;
				}

				// Convert SELECT queries to DELETE queries with proper PostgreSQL syntax
				let deleteSql = selectQuery.sql;

				// Special handling for tables with foreign key constraints
				if (
					selectQuery.requiresConstraintHandling &&
					tableName === 'chat_rooms'
				) {
					// For chat_rooms, we need to handle the last_message_sender foreign key constraint
					// First update last_message_sender to NULL, then delete the records
					const updateSql = this.generateConstraintHandlingQuery(
						selectQuery.sql,
						tableName
					);
					deleteSql = updateSql;
				} else if (deleteSql.includes('JOIN')) {
					// Handle queries with JOINs - convert to DELETE with subqueries
					deleteSql = this.convertSelectWithJoinsToDelete(
						deleteSql,
						tableName
					);
				} else {
					// Simple queries without JOINs - direct conversion
					deleteSql = deleteSql
						.replace(/^SELECT \* FROM/, 'DELETE FROM')
						.replace(
							/^SELECT .+ FROM ([a-zA-Z_]+)/,
							'DELETE FROM $1'
						);
				}

				deletionQueries[tableName] = {
					sql: deleteSql,
					params: selectQuery.params,
					description: selectQuery.description,
					dependencies: selectQuery.dependencies
				};
			}
		}

		return deletionQueries;
	}

	/**
	 * Get S3 file queries for analysis (with file metadata)
	 */
	getS3AnalysisQueries(targetType: DeletionType, targetId: string): QuerySet {
		return this.getS3FileQueries(targetType, targetId, true);
	}

	/**
	 * Get S3 file queries for backup operations (simple file keys)
	 */
	getS3BackupQueries(targetType: DeletionType, targetId: string): QuerySet {
		return this.getS3FileQueries(targetType, targetId, false);
	}

	/**
	 * Get database queries for restore conflict detection
	 * These check if records already exist that would conflict with restore
	 */
	getDatabaseRestoreConflictQueries(
		targetType: DeletionType,
		targetId: string
	): QuerySet {
		const tableQueries = this.getDatabaseTableQueries(targetType, targetId);
		const conflictQueries: QuerySet = {};

		for (const [tableName, query] of Object.entries(tableQueries)) {
			// Convert SELECT * to SELECT id for conflict detection
			const conflictSql = query.sql
				.replace(/^SELECT \*/, 'SELECT id')
				.replace(/^SELECT .+ FROM ([a-zA-Z_]+)/, 'SELECT id FROM $1');

			conflictQueries[tableName] = {
				sql: conflictSql,
				params: query.params,
				description: `Conflict detection for ${query.description}`,
				dependencies: query.dependencies
			};
		}

		return conflictQueries;
	}

	/**
	 * Get database queries for restore operations (INSERT queries)
	 * These will be generated dynamically based on the backup data structure
	 */
	getDatabaseRestoreInsertQueries(
		tableName: string,
		columns: string[],
		recordCount: number
	): QueryDefinition {
		// Generate parameterized INSERT query
		const columnList = columns.join(', ');

		// For bulk inserts, we'll need multiple value sets
		const valueSets: string[] = [];
		for (let i = 0; i < recordCount; i++) {
			const offset = i * columns.length;
			const paramSet = columns
				.map((_, colIndex) => `$${offset + colIndex + 1}`)
				.join(', ');
			valueSets.push(`(${paramSet})`);
		}

		return {
			sql: `INSERT INTO ${tableName} (${columnList}) VALUES ${valueSets.join(', ')}`,
			params: [], // Will be populated with actual data during restore
			description: `Restore data to ${tableName}`,
			dependencies: []
		};
	}

	/**
	 * Get a single INSERT query for restore operations
	 * This is for inserting one record at a time with proper error handling
	 */
	getDatabaseRestoreSingleInsertQuery(
		tableName: string,
		columns: string[]
	): QueryDefinition {
		const columnList = columns.join(', ');
		const parameterPlaceholders = columns
			.map((_, index) => `$${index + 1}`)
			.join(', ');

		return {
			sql: `INSERT INTO ${tableName} (${columnList}) VALUES (${parameterPlaceholders})`,
			params: [], // Will be populated with actual record data during restore
			description: `Restore single record to ${tableName}`,
			dependencies: []
		};
	}

	/**
	 * Get UPSERT query for restore operations (INSERT ... ON CONFLICT)
	 * This handles cases where records might already exist
	 */
	getDatabaseRestoreUpsertQuery(
		tableName: string,
		columns: string[],
		conflictColumns: string[] = ['id'],
		updateColumns?: string[]
	): QueryDefinition {
		const columnList = columns.join(', ');
		const parameterPlaceholders = columns
			.map((_, index) => `$${index + 1}`)
			.join(', ');
		const conflictColumnList = conflictColumns.join(', ');

		// If no update columns specified, update all columns except conflict columns
		const columnsToUpdate =
			updateColumns ||
			columns.filter(col => !conflictColumns.includes(col));
		const updateSetClause = columnsToUpdate
			.map(col => `${col} = EXCLUDED.${col}`)
			.join(', ');

		let sql = `INSERT INTO ${tableName} (${columnList}) VALUES (${parameterPlaceholders})`;

		if (columnsToUpdate.length > 0) {
			sql += ` ON CONFLICT (${conflictColumnList}) DO UPDATE SET ${updateSetClause}`;
		} else {
			sql += ` ON CONFLICT (${conflictColumnList}) DO NOTHING`;
		}

		return {
			sql,
			params: [], // Will be populated with actual record data during restore
			description: `Upsert record to ${tableName}`,
			dependencies: []
		};
	}

	/**
	 * Get database queries for restore validation
	 * These verify that the restore was successful
	 */
	getDatabaseRestoreValidationQueries(
		targetType: DeletionType,
		targetId: string
	): QuerySet {
		// Use the same queries as analysis but return actual counts for validation
		return this.getDatabaseAnalysisQueries(targetType, targetId);
	}

	/**
	 * Get S3 file queries for restore conflict detection
	 */
	getS3RestoreConflictQueries(
		targetType: DeletionType,
		targetId: string
	): QuerySet {
		// For S3 conflicts, we just need to know which files exist
		// The actual conflict detection will be done via S3 API calls
		return this.getS3BackupQueries(targetType, targetId);
	}

	/**
	 * Get the proper restore order (parents first, then children)
	 * This is the reverse of deletion order to maintain referential integrity
	 */
	getRestoreOrder(): string[] {
		const deletionOrder = this.getDeletionOrder();
		return [...deletionOrder].reverse();
	}

	/**
	 * Core method that defines all database table queries
	 * This is the single source of truth for all table queries
	 */
	private getDatabaseTableQueries(
		targetType: DeletionType,
		targetId: string
	): QuerySet {
		const baseCondition = this.getBaseCondition(targetType, targetId);

		return {
			// Core entities (no dependencies)
			brands: {
				sql: `SELECT * FROM brands WHERE id = $1`,
				params: [targetType === DeletionType.BRAND ? targetId : null],
				description: 'Brand organization records and configuration',
				dependencies: []
			},
			clinics: {
				sql: `SELECT * FROM clinics WHERE ${
					targetType === DeletionType.CLINIC ? 'id' : 'brand_id'
				} = $1`,
				params: [targetId],
				description: 'Clinic location records and settings',
				dependencies: ['brands']
			},

			// Patient-related entities
			patients: {
				sql: `SELECT * FROM patients WHERE ${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description: 'Patient demographic and medical information',
				dependencies: ['clinics', 'brands']
			},
			patient_owners: {
				sql: `SELECT * FROM patient_owners WHERE ${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description: 'Patient ownership and guardian relationships',
				dependencies: ['clinics', 'brands', 'owner_brands']
			},

			// Appointment-related entities
			appointments: {
				sql: `SELECT * FROM appointments WHERE ${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description: 'Scheduled patient appointments and visit records',
				dependencies: ['patients', 'clinics', 'brands']
			},
			appointment_details: {
				sql: `SELECT ad.* FROM appointment_details ad
					  JOIN appointments a ON ad.appointment_id = a.id
					  WHERE a.${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description: 'Detailed appointment information and notes',
				dependencies: ['appointments']
			},
			appointment_doctors: {
				sql: `SELECT ad.* FROM appointment_doctors ad
					  JOIN appointments a ON ad.appointment_id = a.id
					  WHERE a.${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description: 'Doctor assignments and roles for appointments',
				dependencies: ['appointments', 'clinic_users']
			},
			appointment_assessments: {
				sql: `SELECT * FROM appointment_assessments WHERE ${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description:
					'Clinical assessments and evaluations from appointments',
				dependencies: ['appointments', 'clinics', 'brands', 'users']
			},

			// Audit log entities (must be deleted before parent records)
			appointment_audit_log: {
				sql: `SELECT * FROM appointment_audit_log WHERE "appointmentId" IN (
					SELECT id FROM appointments WHERE ${baseCondition.field} = $1
				)`,
				params: [baseCondition.value],
				description:
					'Audit trail logs for appointment changes and updates',
				dependencies: ['appointments']
			},
			invoice_audit_log: {
				sql: `SELECT * FROM invoice_audit_log WHERE invoice_id IN (
					SELECT id FROM invoices WHERE ${baseCondition.field} = $1
				)`,
				params: [baseCondition.value],
				description:
					'Audit trail logs for invoice modifications and history',
				dependencies: ['invoices', 'users']
			},

			// Financial entities
			credit_transactions: {
				sql: `SELECT * FROM credit_transactions WHERE ${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description:
					'Financial credit transactions and payment adjustments',
				dependencies: ['invoices', 'payment_details', 'owner_brands']
			},
			merged_invoice_documents: {
				sql:
					targetType === DeletionType.CLINIC
						? `SELECT * FROM merged_invoice_documents WHERE owner_id IN (
						SELECT id FROM owner_brands WHERE brand_id IN (
							SELECT brand_id FROM clinics WHERE id = $1
						)
					)`
						: `SELECT * FROM merged_invoice_documents WHERE owner_id IN (
						SELECT id FROM owner_brands WHERE brand_id = $1
					)`,
				params: [baseCondition.value],
				description: 'Consolidated PDF documents for multiple invoices',
				dependencies: ['owner_brands', 'invoices']
			},
			invoices: {
				sql: `SELECT * FROM invoices WHERE ${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description:
					'Financial invoices and billing records for services rendered',
				dependencies: ['appointments', 'patients', 'brands', 'carts']
			},
			payment_details: {
				sql: `SELECT * FROM payment_details WHERE ${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description:
					'Payment transaction details and financial records',
				dependencies: ['invoices', 'brands', 'patients']
			},

			// Cart and commerce (must maintain proper deletion order)
			cart_items: {
				sql: `SELECT ci.* FROM cart_items ci
					  JOIN carts c ON ci.cart_id = c.id
					  JOIN appointments a ON c.appointment_id = a.id
					  WHERE a.${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description:
					'Individual items added to shopping carts (services, products, medications)',
				dependencies: [
					'carts',
					'appointments',
					'clinic_lab_reports',
					'clinic_medications',
					'clinic_products',
					'clinic_services',
					'clinic_vaccinations'
				]
			},
			carts: {
				sql: `SELECT c.* FROM carts c
					  JOIN appointments a ON c.appointment_id = a.id
					  WHERE a.${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description:
					'Shopping carts containing items for purchase during appointments',
				dependencies: ['appointments']
			},

			// Medical records
			emr: {
				sql: `SELECT * FROM emr WHERE ${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description:
					'Electronic medical records and patient health data',
				dependencies: ['appointments', 'patients', 'brands', 'clinics']
			},
			lab_reports: {
				sql: `SELECT * FROM lab_reports WHERE ${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description: 'Laboratory test results and diagnostic reports',
				dependencies: [
					'appointments',
					'patients',
					'clinic_lab_reports',
					'brands',
					'clinics'
				]
			},
			diagnostic_notes: {
				sql:
					targetType === DeletionType.CLINIC
						? `SELECT * FROM diagnostic_notes WHERE clinic_id = $1`
						: `SELECT dn.* FROM diagnostic_notes dn
					   JOIN clinics c ON dn.clinic_id = c.id
					   WHERE c.brand_id = $1`,
				params: [baseCondition.value],
				description:
					'Clinical diagnostic notes and observations from appointments',
				dependencies: [
					'clinics',
					'appointments',
					'lab_reports',
					'patients',
					'diagnostic_templates'
				]
			},
			diagnostic_templates: {
				sql:
					targetType === DeletionType.CLINIC
						? `SELECT * FROM diagnostic_templates WHERE clinic_id = $1`
						: `SELECT dt.* FROM diagnostic_templates dt
					   JOIN clinics c ON dt.clinic_id = c.id
					   WHERE c.brand_id = $1`,
				params: [baseCondition.value],
				description: 'Reusable diagnostic note templates and forms',
				dependencies: ['clinics']
			},
			'long-term-medications': {
				sql: `SELECT * FROM "long-term-medications" WHERE ${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description:
					'Ongoing medication prescriptions and treatment plans',
				dependencies: ['patients', 'appointments']
			},

			// Patient medical records
			patient_vaccinations: {
				sql: `SELECT pv.* FROM patient_vaccinations pv
					  JOIN patients p ON pv.patient_id = p.id
					  WHERE p.${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description: 'Vaccination records and immunization history',
				dependencies: [
					'patients',
					'appointments',
					'clinic_vaccinations'
				]
			},
			patient_alerts: {
				sql: `SELECT pa.* FROM patient_alerts pa
					  JOIN patients p ON pa.patient_id = p.id
					  WHERE p.${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description: 'Patient alerts',
				dependencies: ['patients']
			},

			// Document entities
			document_library: {
				sql: `SELECT * FROM document_library WHERE ${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description: 'Document library',
				dependencies: ['clinics', 'brands']
			},
			patient_document_libraries: {
				sql: `SELECT pdl.* FROM patient_document_libraries pdl
					  JOIN patients p ON pdl.patient_id = p.id
					  WHERE p.${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description: 'Patient document libraries',
				dependencies: ['patients', 'document_library', 'users']
			},

			// Patient estimate entities
			patient_estimate: {
				sql:
					targetType === DeletionType.CLINIC
						? `SELECT * FROM patient_estimate WHERE clinic_id = $1`
						: `SELECT pe.* FROM patient_estimate pe
					   JOIN clinics c ON pe.clinic_id = c.id
					   WHERE c.brand_id = $1`,
				params: [baseCondition.value],
				description: 'Patient estimates',
				dependencies: ['clinics', 'patients', 'users']
			},

			// Reminder entities
			patient_reminders: {
				sql: `SELECT * FROM patient_reminders WHERE ${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description: 'Patient reminders and scheduled notifications',
				dependencies: [
					'patients',
					'brands',
					'clinics',
					'users',
					'appointments',
					'global_reminder_rules'
				]
			},
			patient_reminder_history: {
				sql: `SELECT prh.* FROM patient_reminder_history prh
					  JOIN patient_reminders pr ON prh.reminder_id = pr.id
					  WHERE pr.${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description: 'Patient reminder change history and audit trail',
				dependencies: ['patient_reminders', 'users']
			},

			// Pet transfer
			pet_transfer_history: {
				sql: `SELECT * FROM pet_transfer_history WHERE ${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description: 'Pet transfer history',
				dependencies: ['patients', 'clinics', 'brands']
			},

			// Chat-related entities (must be backed up/deleted before clinic_users)
			chat_rooms: {
				sql: `SELECT cr.* FROM chat_rooms cr
					WHERE cr.id NOT IN (
						SELECT DISTINCT chat_room_id FROM chat_room_users
					)`,
				params: [], // No parameters needed for orphaned chat room detection
				description:
					'Chat rooms that have no remaining users (orphaned after user deletion)',
				dependencies: ['clinic_users'],
				// Special handling for foreign key constraint on last_message_sender
				requiresConstraintHandling: true
			},
			chat_room_messages: {
				sql:
					targetType === DeletionType.CLINIC
						? `SELECT * FROM chat_room_messages WHERE sender_id IN (
						SELECT id FROM clinic_users WHERE clinic_id = $1
					)`
						: `SELECT * FROM chat_room_messages WHERE sender_id IN (
						SELECT id FROM clinic_users WHERE clinic_id IN (
							SELECT id FROM clinics WHERE brand_id = $1
						)
					)`,
				params: [baseCondition.value],
				description: 'Chat room messages',
				dependencies: ['clinic_users', 'chat_rooms']
			},
			chat_room_users: {
				sql:
					targetType === DeletionType.CLINIC
						? `SELECT * FROM chat_room_users WHERE user_id IN (
						SELECT id FROM clinic_users WHERE clinic_id = $1
					)`
						: `SELECT * FROM chat_room_users WHERE user_id IN (
						SELECT id FROM clinic_users WHERE clinic_id IN (
							SELECT id FROM clinics WHERE brand_id = $1
						)
					)`,
				params: [baseCondition.value],
				description: 'Chat room users',
				dependencies: ['clinic_users', 'chat_rooms']
			},
			chat_user_sessions: {
				sql:
					targetType === DeletionType.CLINIC
						? `SELECT * FROM chat_user_sessions WHERE user_id IN (
						SELECT id FROM clinic_users WHERE clinic_id = $1
					)`
						: `SELECT * FROM chat_user_sessions WHERE user_id IN (
						SELECT id FROM clinic_users WHERE clinic_id IN (
							SELECT id FROM clinics WHERE brand_id = $1
						)
					)`,
				params: [baseCondition.value],
				description: 'Chat user sessions',
				dependencies: ['clinic_users']
			},

			// User management entities
			clinic_users: {
				sql:
					targetType === DeletionType.CLINIC
						? `SELECT * FROM clinic_users WHERE clinic_id = $1`
						: `SELECT * FROM clinic_users WHERE clinic_id IN (
						SELECT id FROM clinics WHERE brand_id = $1
					)`,
				params: [baseCondition.value],
				description:
					'User accounts and roles associated with clinic operations',
				dependencies: ['clinics', 'users']
			},
			users: {
				sql:
					targetType === DeletionType.BRAND
						? `SELECT DISTINCT u.* FROM users u
						   JOIN clinic_users cu ON u.id = cu.user_id
						   JOIN clinics c ON cu.clinic_id = c.id
						   WHERE c.brand_id = $1`
						: `SELECT DISTINCT u.* FROM users u
						   JOIN clinic_users cu ON u.id = cu.user_id
						   WHERE cu.clinic_id = $1
						   AND u.id NOT IN (
						       SELECT DISTINCT cu2.user_id
						       FROM clinic_users cu2
						       WHERE cu2.clinic_id != $1
						   )`, // Only select users who belong exclusively to this clinic
				params: [baseCondition.value],
				description:
					targetType === DeletionType.BRAND
						? 'System user accounts and authentication data (all users in brand clinics)'
						: 'Users who belong exclusively to this clinic',
				dependencies: [] // No dependencies to avoid circular dependency with clinic_users
			},

			// Owner management
			owner_brands: {
				sql:
					targetType === DeletionType.CLINIC
						? `SELECT DISTINCT ob.* FROM owner_brands ob 
						   WHERE ob.brand_id IN (SELECT brand_id FROM clinics WHERE id = $1)
						   OR ob.id IN (SELECT DISTINCT owner_id FROM patient_owners WHERE clinic_id = $1)`
						: `SELECT * FROM owner_brands WHERE brand_id = $1`,
				params: [baseCondition.value],
				description: 'Owner brands',
				dependencies: ['brands']
			},

			// Staff availability
			availability_exceptions: {
				sql:
					targetType === DeletionType.CLINIC
						? `SELECT * FROM availability_exceptions WHERE clinic_user_id IN (
						SELECT id FROM clinic_users WHERE clinic_id = $1
					)`
						: `SELECT * FROM availability_exceptions WHERE clinic_user_id IN (
						SELECT id FROM clinic_users WHERE clinic_id IN (
							SELECT id FROM clinics WHERE brand_id = $1
						)
					)`,
				params: [baseCondition.value],
				description: 'Availability exceptions',
				dependencies: ['clinic_users']
			},
			clinic_availability_slots: {
				sql:
					targetType === DeletionType.CLINIC
						? `SELECT * FROM clinic_availability_slots WHERE clinic_user_id IN (
						SELECT id FROM clinic_users WHERE clinic_id = $1
					)`
						: `SELECT * FROM clinic_availability_slots WHERE clinic_user_id IN (
						SELECT id FROM clinic_users WHERE clinic_id IN (
							SELECT id FROM clinics WHERE brand_id = $1
						)
					)`,
				params: [baseCondition.value],
				description: 'Clinic availability slots',
				dependencies: ['clinic_users']
			},

			// Task management
			tasks: {
				sql:
					targetType === DeletionType.CLINIC
						? `SELECT * FROM tasks WHERE user_id IN (
						SELECT id FROM clinic_users WHERE clinic_id = $1
					)`
						: `SELECT * FROM tasks WHERE user_id IN (
						SELECT id FROM clinic_users WHERE clinic_id IN (
							SELECT id FROM clinics WHERE brand_id = $1
						)
					)`,
				params: [baseCondition.value],
				description:
					'Task assignments and workflow management for clinic staff',
				dependencies: ['clinic_users']
			},

			// *** MISSING CLINIC CONFIGURATION TABLES ***
			clinic_alerts: {
				sql: `SELECT * FROM clinic_alerts WHERE ${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description: 'Clinic alerts',
				dependencies: ['clinics']
			},
			clinic_consumables: {
				sql: `SELECT * FROM clinic_consumables WHERE ${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description: 'Clinic consumables',
				dependencies: ['clinics', 'brands']
			},
			clinic_integrations: {
				sql: `SELECT * FROM clinic_integrations WHERE ${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description: 'Clinic integrations',
				dependencies: ['clinics']
			},
			clinic_lab_reports: {
				sql: `SELECT * FROM clinic_lab_reports WHERE ${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description: 'Clinic lab reports configuration',
				dependencies: ['clinics']
			},
			clinic_medications: {
				sql: `SELECT * FROM clinic_medications WHERE ${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description: 'Clinic medications',
				dependencies: ['clinics', 'users']
			},
			clinic_plans: {
				sql: `SELECT * FROM clinic_plans WHERE ${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description: 'Clinic plans',
				dependencies: ['clinics']
			},
			clinic_products: {
				sql: `SELECT * FROM clinic_products WHERE ${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description: 'Clinic products',
				dependencies: ['clinics', 'brands']
			},
			clinic_rooms: {
				sql: `SELECT * FROM clinic_rooms WHERE ${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description: 'Clinic rooms',
				dependencies: ['clinics', 'brands']
			},
			clinic_services: {
				sql: `SELECT * FROM clinic_services WHERE ${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description: 'Clinic services',
				dependencies: ['clinics', 'brands']
			},
			clinic_vaccinations: {
				sql: `SELECT * FROM clinic_vaccinations WHERE ${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description: 'Clinic vaccinations',
				dependencies: ['clinics', 'brands']
			},

			// *** MISSING GLOBAL REMINDER TABLES ***
			global_reminder_rules: {
				sql: `SELECT * FROM global_reminder_rules WHERE ${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description: 'Global reminder rules',
				dependencies: ['clinics', 'brands']
			},

			// *** MISSING TAB ACTIVITY TABLES ***
			tab_activities: {
				sql: `SELECT * FROM tab_activities WHERE ${baseCondition.field} = $1`,
				params: [baseCondition.value],
				description: 'Tab activities',
				dependencies: ['clinics']
			}
		};
	}

	/**
	 * Core method that defines all S3 file queries
	 * This is the single source of truth for all S3 file queries
	 */
	private getS3FileQueries(
		targetType: DeletionType,
		targetId: string,
		includeMetadata: boolean = false
	): QuerySet {
		const baseCondition = this.getBaseCondition(targetType, targetId);
		const metadataCreatedAt = includeMetadata ? ', created_at' : '';
		const metadataUpdatedAt = includeMetadata
			? ', updated_at as created_at'
			: '';
		const metadataPdlCreatedAt = includeMetadata ? ', pdl.created_at' : '';
		const metadataDnUpdatedAt = includeMetadata
			? ', dn.updated_at as created_at'
			: '';
		const metadataPeUpdatedAt = includeMetadata
			? ', pe.updated_at as created_at'
			: '';
		const metadataPvCreatedAt = includeMetadata ? ', pv.created_at' : '';
		const metadataAdCreatedAt = includeMetadata ? ', ad.created_at' : '';

		return {
			// Document library files
			document_library: {
				sql: `SELECT file_key${metadataCreatedAt} FROM document_library
					  WHERE ${baseCondition.field} = $1 AND file_key IS NOT NULL`,
				params: [baseCondition.value],
				description: 'Document library files'
			},

			// Patient document files
			patient_document_libraries: {
				sql: `SELECT pdl.file_key${metadataPdlCreatedAt} FROM patient_document_libraries pdl
					  JOIN patients p ON pdl.patient_id = p.id
					  WHERE p.${baseCondition.field} = $1 AND pdl.file_key IS NOT NULL`,
				params: [baseCondition.value],
				description: 'Patient document library files'
			},

			// EMR files - standardized field naming
			emr_documents: {
				sql: `SELECT emr_file_key as file_key${metadataCreatedAt} FROM emr
					  WHERE ${baseCondition.field} = $1 AND emr_file_key IS NOT NULL`,
				params: [baseCondition.value],
				description: 'EMR document attachments'
			},

			// Lab report files (JSONB) - standardized handling
			lab_reports: {
				sql: includeMetadata
					? `SELECT (jsonb_array_elements(files)->>'fileKey') as file_key, created_at
					   FROM lab_reports
					   WHERE ${baseCondition.field} = $1
					   AND files IS NOT NULL
					   AND jsonb_array_length(files) > 0`
					: `SELECT (jsonb_array_elements(files)->>'fileKey') as file_key
					   FROM lab_reports
					   WHERE ${baseCondition.field} = $1
					   AND files IS NOT NULL
					   AND jsonb_array_length(files) > 0`,
				params: [baseCondition.value],
				description: 'Laboratory report files'
			},

			// Diagnostic notes files
			diagnostic_notes: {
				sql:
					targetType === DeletionType.CLINIC
						? `SELECT file_key${metadataUpdatedAt} FROM diagnostic_notes
					   WHERE clinic_id = $1 AND file_key IS NOT NULL`
						: `SELECT dn.file_key${metadataDnUpdatedAt} FROM diagnostic_notes dn
					   JOIN clinics c ON dn.clinic_id = c.id
					   WHERE c.brand_id = $1 AND dn.file_key IS NOT NULL`,
				params: [baseCondition.value],
				description: 'Diagnostic note attachments'
			},

			// Patient estimate files
			patient_estimate: {
				sql:
					targetType === DeletionType.CLINIC
						? `SELECT file_key${metadataUpdatedAt} FROM patient_estimate
					   WHERE clinic_id = $1 AND file_key IS NOT NULL`
						: `SELECT pe.file_key${metadataPeUpdatedAt} FROM patient_estimate pe
					   JOIN clinics c ON pe.clinic_id = c.id
					   WHERE c.brand_id = $1 AND pe.file_key IS NOT NULL`,
				params: [baseCondition.value],
				description: 'Patient estimate documents'
			},

			// Patient vaccination reports - standardized field naming
			patient_vaccinations: {
				sql: `SELECT pv.report_url as file_key${metadataPvCreatedAt} FROM patient_vaccinations pv
					  JOIN patients p ON pv.patient_id = p.id
					  WHERE p.${baseCondition.field} = $1 AND pv.report_url IS NOT NULL`,
				params: [baseCondition.value],
				description: 'Vaccination report files'
			},

			// Invoice files - standardized field naming
			invoices: {
				sql: `SELECT file_url as file_key${metadataCreatedAt} FROM invoices
					  WHERE ${baseCondition.field} = $1 AND file_url IS NOT NULL`,
				params: [baseCondition.value],
				description: 'Invoice files'
			},

			// Clinic logos - standardized field naming
			clinics: {
				sql: `SELECT logo_url as file_key${metadataUpdatedAt} FROM clinics
					  WHERE ${targetType === DeletionType.CLINIC ? 'id' : 'brand_id'} = $1
					  AND logo_url IS NOT NULL`,
				params: [targetId],
				description: 'Clinic logo images'
			},

			// Chat attachments - standardized JSONB handling
			chat_attachments: {
				sql:
					targetType === DeletionType.CLINIC
						? `SELECT (file->>'fileKey') as file_key${metadataCreatedAt}
					   FROM chat_room_messages
					   WHERE sender_id IN (SELECT id FROM clinic_users WHERE clinic_id = $1)
					   AND file IS NOT NULL`
						: `SELECT (file->>'fileKey') as file_key${metadataCreatedAt}
					   FROM chat_room_messages
					   WHERE sender_id IN (
						   SELECT id FROM clinic_users WHERE clinic_id IN (
							   SELECT id FROM clinics WHERE brand_id = $1
						   )
					   ) AND file IS NOT NULL`,
				params: [baseCondition.value],
				description: 'Chat message attachments'
			},

			// Merged invoice documents
			merged_invoice_documents: {
				sql:
					targetType === DeletionType.CLINIC
						? `SELECT file_key${metadataUpdatedAt} FROM merged_invoice_documents
					   WHERE owner_id IN (
						   SELECT id FROM owner_brands WHERE brand_id IN (
							   SELECT brand_id FROM clinics WHERE id = $1
						   )
					   ) AND file_key IS NOT NULL`
						: `SELECT file_key${metadataUpdatedAt} FROM merged_invoice_documents
					   WHERE owner_id IN (
						   SELECT id FROM owner_brands WHERE brand_id = $1
					   ) AND file_key IS NOT NULL`,
				params: [baseCondition.value],
				description: 'Merged invoice document files'
			},

			// Appointment attachments - standardized JSONB handling
			appointment_attachments: {
				sql: `SELECT (jsonb_array_elements(details->'attachments'->'list')->>'fileKey') as file_key${metadataAdCreatedAt}
					  FROM appointment_details ad
					  JOIN appointments a ON ad.appointment_id = a.id
					  WHERE a.${baseCondition.field} = $1
					  AND jsonb_array_length(details->'attachments'->'list') > 0`,
				params: [baseCondition.value],
				description: 'Appointment detail attachments'
			}
		};
	}

	/**
	 * Convert SELECT queries with JOINs to proper PostgreSQL DELETE syntax
	 * PostgreSQL requires DELETE FROM table WHERE id IN (subquery) format
	 */
	private convertSelectWithJoinsToDelete(
		selectSql: string,
		tableName: string
	): string {
		// Find the table alias for the main table from the original SELECT
		const aliasMatch = new RegExp(`FROM ${tableName}\\s+(\\w+)`).exec(
			selectSql
		);
		const tableAlias = aliasMatch ? aliasMatch[1] : tableName.charAt(0);

		// Convert to DELETE FROM table WHERE id IN (SELECT table_alias.id FROM table JOIN ... WHERE ...)
		const subquery = selectSql.replace(
			/^SELECT .+ FROM/,
			`SELECT ${tableAlias}.id FROM`
		);

		return `DELETE FROM ${tableName} WHERE id IN (${subquery})`;
	}

	/**
	 * Get base condition for queries based on deletion type
	 */
	private getBaseCondition(targetType: DeletionType, targetId: string) {
		return targetType === DeletionType.CLINIC
			? { field: 'clinic_id', value: targetId }
			: { field: 'brand_id', value: targetId };
	}

	/**
	 * Get proper deletion order (children first, then parents)
	 * This order is critical to avoid foreign key constraint violations
	 * Uses topological sort to ensure dependencies are respected
	 */
	private getDeletionOrder(): string[] {
		// Get table queries to extract dependencies
		const tableQueries = this.getDatabaseTableQueries(
			DeletionType.CLINIC,
			'dummy-id'
		);

		// Build dependency graph
		const dependencies: { [tableName: string]: string[] } = {};
		const allTables = new Set<string>();

		for (const [tableName, queryDef] of Object.entries(tableQueries)) {
			dependencies[tableName] = (queryDef as any).dependencies || [];
			allTables.add(tableName);
			// Add all dependencies to the set of tables
			dependencies[tableName].forEach(dep => allTables.add(dep));
		}

		// Perform topological sort (Kahn's algorithm)
		// For deletion order, we want children before parents, so we reverse the dependency graph
		const reversedDeps: { [tableName: string]: string[] } = {};
		const inDegree: { [tableName: string]: number } = {};

		// Initialize
		for (const table of allTables) {
			reversedDeps[table] = [];
			inDegree[table] = 0;
		}

		// Build reversed dependency graph (children point to parents)
		for (const [table, deps] of Object.entries(dependencies)) {
			for (const dep of deps) {
				if (allTables.has(dep)) {
					reversedDeps[dep].push(table);
					inDegree[table]++;
				}
			}
		}

		// Kahn's algorithm
		const queue: string[] = [];
		const result: string[] = [];

		// Find all nodes with no incoming edges (parents with no dependencies)
		for (const table of allTables) {
			if (inDegree[table] === 0) {
				queue.push(table);
			}
		}

		while (queue.length > 0) {
			const current = queue.shift()!;
			result.push(current);

			// Remove edges from current node
			for (const neighbor of reversedDeps[current]) {
				inDegree[neighbor]--;
				if (inDegree[neighbor] === 0) {
					queue.push(neighbor);
				}
			}
		}

		// Check for cycles
		if (result.length !== allTables.size) {
			throw new Error(
				'Circular dependency detected in table dependencies'
			);
		}

		// For deletion, we want children first, so reverse the result
		return result.reverse();
	}

	/**
	 * Validate that all query sets have consistent table coverage
	 */
	validateQueryConsistency(
		targetType: DeletionType,
		targetId: string
	): {
		valid: boolean;
		errors: string[];
		warnings: string[];
	} {
		const errors: string[] = [];
		const warnings: string[] = [];

		try {
			const analysisQueries = this.getDatabaseAnalysisQueries(
				targetType,
				targetId
			);
			const backupQueries = this.getDatabaseBackupQueries(
				targetType,
				targetId
			);
			const deletionQueries = this.getDatabaseDeletionQueries(
				targetType,
				targetId
			);
			const s3AnalysisQueries = this.getS3AnalysisQueries(
				targetType,
				targetId
			);
			const s3BackupQueries = this.getS3BackupQueries(
				targetType,
				targetId
			);

			const analysisTables = new Set(Object.keys(analysisQueries));
			const backupTables = new Set(Object.keys(backupQueries));
			const deletionTables = new Set(Object.keys(deletionQueries));
			const s3AnalysisTables = new Set(Object.keys(s3AnalysisQueries));
			const s3BackupTables = new Set(Object.keys(s3BackupQueries));

			// Check database query consistency
			const analysisOnly = [...analysisTables].filter(
				t => !backupTables.has(t)
			);
			const backupOnly = [...backupTables].filter(
				t => !analysisTables.has(t)
			);
			const deletionOnly = [...deletionTables].filter(
				t => !backupTables.has(t)
			);
			const backupNotInDeletion = [...backupTables].filter(
				t => !deletionTables.has(t)
			);

			if (analysisOnly.length > 0) {
				warnings.push(
					`Tables in analysis but not backup: ${analysisOnly.join(', ')}`
				);
			}
			if (backupOnly.length > 0) {
				warnings.push(
					`Tables in backup but not analysis: ${backupOnly.join(', ')}`
				);
			}
			if (deletionOnly.length > 0) {
				warnings.push(
					`Tables in deletion but not backup: ${deletionOnly.join(', ')}`
				);
			}
			if (backupNotInDeletion.length > 0) {
				warnings.push(
					`Tables in backup but not deletion: ${backupNotInDeletion.join(', ')}`
				);
			}

			// Check S3 query consistency
			const s3AnalysisOnly = [...s3AnalysisTables].filter(
				t => !s3BackupTables.has(t)
			);
			const s3BackupOnly = [...s3BackupTables].filter(
				t => !s3AnalysisTables.has(t)
			);

			if (s3AnalysisOnly.length > 0) {
				warnings.push(
					`S3 tables in analysis but not backup: ${s3AnalysisOnly.join(', ')}`
				);
			}
			if (s3BackupOnly.length > 0) {
				warnings.push(
					`S3 tables in backup but not analysis: ${s3BackupOnly.join(', ')}`
				);
			}

			return {
				valid: errors.length === 0,
				errors,
				warnings
			};
		} catch (error) {
			errors.push(
				`Query validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
			);
			return { valid: false, errors, warnings };
		}
	}

	/**
	 * Detects if a SQL query is a no-op query that should be skipped during deletion
	 * No-op queries are designed to return no results and don't need corresponding DELETE statements
	 */
	private isNoOpQuery(sql: string): boolean {
		const trimmedSql = sql.trim();

		// Common no-op patterns
		const noOpPatterns = [
			/^SELECT\s+1\s+WHERE\s+1\s*=\s*0$/i, // SELECT 1 WHERE 1 = 0
			/^SELECT\s+\*\s+FROM\s+\w+\s+WHERE\s+1\s*=\s*0$/i, // SELECT * FROM table WHERE 1 = 0
			/^SELECT\s+.+\s+WHERE\s+1\s*=\s*0$/i, // SELECT ... WHERE 1 = 0
			/^SELECT\s+1\s+WHERE\s+false$/i, // SELECT 1 WHERE false
			/^SELECT\s+\*\s+FROM\s+\w+\s+WHERE\s+false$/i // SELECT * FROM table WHERE false
		];

		return noOpPatterns.some(pattern => pattern.test(trimmedSql));
	}

	/**
	 * Generate special deletion queries for tables with foreign key constraints
	 * This handles cases where direct deletion would violate foreign key constraints
	 */
	private generateConstraintHandlingQuery(
		selectSql: string,
		tableName: string
	): string {
		if (tableName === 'chat_rooms') {
			// For chat_rooms, we need to handle the last_message_sender foreign key constraint
			// Use a CTE to first update last_message_sender to NULL, then delete in a single statement

			// Normalize the SQL by removing extra whitespace and line breaks for easier parsing
			const normalizedSql = selectSql.replace(/\s+/g, ' ').trim();

			// Extract the WHERE clause from the original SELECT query
			const whereMatch = /WHERE\s+(.+)$/i.exec(normalizedSql);

			if (whereMatch) {
				const whereClause = whereMatch[1];

				// Use a CTE to update and delete in a single statement
				// This ensures atomicity and returns the correct affected row count
				return `
					WITH rooms_to_delete AS (
						SELECT cr.id FROM chat_rooms cr
						WHERE ${whereClause}
					),
					updated_rooms AS (
						UPDATE chat_rooms
						SET last_message_sender = NULL
						WHERE id IN (SELECT id FROM rooms_to_delete)
						RETURNING id
					)
					DELETE FROM chat_rooms
					WHERE id IN (SELECT id FROM rooms_to_delete)
				`.trim();
			}
		}

		// Fallback to regular conversion if no special handling is defined
		return this.convertSelectWithJoinsToDelete(selectSql, tableName);
	}
}

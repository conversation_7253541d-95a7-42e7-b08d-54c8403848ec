import {
  Injectable,
  HttpException,
  HttpStatus,
  forwardRef,
  Inject
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { PDFDocument } from 'pdf-lib';
import * as ExcelJS from 'exceljs';
import { v4 as uuidv4 } from 'uuid';
import { AnalyticsDocumentRequest } from '../entities/analytics-document-request.entity';
import { AnalyticsDocumentGenerationJob, DocumentGenerationResult } from '../interfaces/analytics-sharing.interface';
import { AnalyticsDocumentType, ShareMode } from '../dto/analytics-document-types.enum';
import { S3Service } from '../../utils/aws/s3/s3.service';
import { WinstonLogger } from '../../utils/logger/winston-logger.service';
import { SendDocuments } from '../../utils/common/send-document.service';
import { InvoiceEntity } from '../../invoice/entities/invoice.entity';
import { ReceiptEntity } from '../../receipt/entities/receipt.entity';
import { CreditNoteEntity } from '../../credit-note/entities/credit-note.entity';

@Injectable()
export class AnalyticsDocumentGenerationService {
  constructor(
    @InjectRepository(AnalyticsDocumentRequest)
    private readonly requestRepository: Repository<AnalyticsDocumentRequest>,
    @InjectRepository(InvoiceEntity)
    private readonly invoiceRepository: Repository<InvoiceEntity>,
    @InjectRepository(ReceiptEntity)
    private readonly receiptRepository: Repository<ReceiptEntity>,
    @InjectRepository(CreditNoteEntity)
    private readonly creditNoteRepository: Repository<CreditNoteEntity>,
    private readonly s3Service: S3Service,
    private readonly logger: WinstonLogger,
    @Inject(forwardRef(() => SendDocuments))
    private readonly sendDocuments: SendDocuments
  ) {}

  async processDocumentGeneration(job: AnalyticsDocumentGenerationJob): Promise<void> {
    const { requestId } = job;

    try {
      this.logger.log('Starting analytics document generation', {
        requestId,
        clinicId: job.clinicId,
        documentTypes: job.documentTypes
      });

      // Update status to processing
      await this.updateRequestStatus(requestId, 'processing');

      // Generate documents based on types
      const documentBuffers: Buffer[] = [];
      const documentMetadata: any[] = [];

      for (const docType of job.documentTypes) {
        const { buffer, metadata } = await this.generateDocumentByType(
          docType,
          job.clinicId,
          new Date(job.startDate),
          new Date(job.endDate)
        );
        
        if (buffer) {
          documentBuffers.push(buffer);
          documentMetadata.push(metadata);
        }
      }

      if (documentBuffers.length === 0) {
        throw new Error('No documents found for the specified criteria');
      }

      // Merge PDFs using streaming approach (memory efficient)
      const mergedPdfKey = await this.mergeAndUploadPdfs(
        documentBuffers,
        requestId,
        job.clinicId
      );

      let excelKey: string | undefined;
      
      // Generate Excel report if requested
      if (job.includeExcelReport) {
        excelKey = await this.generateAndUploadExcelReport(
          documentMetadata,
          requestId,
          job.clinicId,
          new Date(job.startDate),
          new Date(job.endDate)
        );
      }

      // Update request with S3 keys
      await this.updateRequestStatus(
        requestId,
        'completed',
        mergedPdfKey,
        excelKey
      );

      // Send documents via email/WhatsApp
      await this.sendGeneratedDocuments(job, mergedPdfKey, excelKey);

      this.logger.log('Analytics document generation completed', {
        requestId,
        mergedPdfKey,
        excelKey
      });

    } catch (error) {
      this.logger.error('Error in analytics document generation', {
        requestId,
        error: error.message,
        stack: error.stack
      });

      await this.updateRequestStatus(
        requestId,
        'failed',
        undefined,
        undefined,
        error.message
      );

      throw error;
    }
  }

  private async generateDocumentByType(
    docType: AnalyticsDocumentType,
    clinicId: string,
    startDate: Date,
    endDate: Date
  ): Promise<{ buffer: Buffer | null; metadata: any }> {
    
    switch (docType) {
      case AnalyticsDocumentType.INVOICES:
        return await this.generateInvoicesDocument(clinicId, startDate, endDate);
      
      case AnalyticsDocumentType.RECEIPTS:
        return await this.generateReceiptsDocument(clinicId, startDate, endDate);
      
      case AnalyticsDocumentType.CREDIT_NOTES:
        return await this.generateCreditNotesDocument(clinicId, startDate, endDate);
      
      default:
        throw new Error(`Unsupported document type: ${docType}`);
    }
  }

  private async generateInvoicesDocument(
    clinicId: string,
    startDate: Date,
    endDate: Date
  ): Promise<{ buffer: Buffer | null; metadata: any }> {
    
    // Get invoices for the period
    const invoices = await this.invoiceRepository.find({
      where: {
        clinicId,
        createdAt: Between(startDate, endDate)
      },
      order: { createdAt: 'DESC' }
    });

    if (invoices.length === 0) {
      return { buffer: null, metadata: { type: 'invoices', count: 0, totalAmount: 0 } };
    }

    // Use existing invoice PDF generation logic from SendDocuments service
    const invoiceIds = invoices.map(inv => inv.referenceAlphaId);
    
    // Generate individual PDFs and merge them
    const pdfBuffers: Buffer[] = [];
    let totalAmount = 0;

    for (const invoice of invoices) {
      try {
        const pdfBuffer = await this.sendDocuments.getInvoicePdfBuffer(invoice.referenceAlphaId);
        if (pdfBuffer) {
          pdfBuffers.push(pdfBuffer);
          totalAmount += invoice.totalAmount || 0;
        }
      } catch (error) {
        this.logger.warn('Failed to generate PDF for invoice', {
          invoiceId: invoice.referenceAlphaId,
          error: error.message
        });
      }
    }

    if (pdfBuffers.length === 0) {
      return { buffer: null, metadata: { type: 'invoices', count: 0, totalAmount: 0 } };
    }

    // Merge PDFs
    const mergedBuffer = await this.mergePdfBuffers(pdfBuffers);

    return {
      buffer: mergedBuffer,
      metadata: {
        type: 'invoices',
        count: invoices.length,
        totalAmount,
        dateRange: { start: startDate, end: endDate }
      }
    };
  }

  private async generateReceiptsDocument(
    clinicId: string,
    startDate: Date,
    endDate: Date
  ): Promise<{ buffer: Buffer | null; metadata: any }> {
    
    // Similar implementation for receipts
    // This would follow the same pattern as invoices
    // For now, returning placeholder
    return {
      buffer: null,
      metadata: {
        type: 'receipts',
        count: 0,
        totalAmount: 0,
        dateRange: { start: startDate, end: endDate }
      }
    };
  }

  private async generateCreditNotesDocument(
    clinicId: string,
    startDate: Date,
    endDate: Date
  ): Promise<{ buffer: Buffer | null; metadata: any }> {
    
    // Similar implementation for credit notes
    // This would follow the same pattern as invoices
    // For now, returning placeholder
    return {
      buffer: null,
      metadata: {
        type: 'credit_notes',
        count: 0,
        totalAmount: 0,
        dateRange: { start: startDate, end: endDate }
      }
    };
  }

  private async mergePdfBuffers(buffers: Buffer[]): Promise<Buffer> {
    const mergedPdf = await PDFDocument.create();

    for (const buffer of buffers) {
      try {
        const pdf = await PDFDocument.load(new Uint8Array(buffer));
        const copiedPages = await mergedPdf.copyPages(pdf, pdf.getPageIndices());
        copiedPages.forEach((page) => mergedPdf.addPage(page));
      } catch (error) {
        this.logger.warn('Failed to merge PDF buffer', { error: error.message });
      }
    }

    const mergedPdfBytes = await mergedPdf.save();
    return Buffer.from(mergedPdfBytes);
  }

  private async mergeAndUploadPdfs(
    buffers: Buffer[],
    requestId: string,
    clinicId: string
  ): Promise<string> {
    
    const mergedBuffer = await this.mergePdfBuffers(buffers);
    
    // Upload to S3
    const s3Key = `analytics-documents/${clinicId}/${requestId}/merged-documents.pdf`;
    
    await this.s3Service.uploadFile({
      key: s3Key,
      body: mergedBuffer,
      contentType: 'application/pdf'
    });

    return s3Key;
  }

  private async generateAndUploadExcelReport(
    metadata: any[],
    requestId: string,
    clinicId: string,
    startDate: Date,
    endDate: Date
  ): Promise<string> {

    const workbook = new ExcelJS.Workbook();

    // Summary sheet
    const summarySheet = workbook.addWorksheet('Summary');
    summarySheet.columns = [
      { header: 'Document Type', key: 'type', width: 20 },
      { header: 'Count', key: 'count', width: 15 },
      { header: 'Total Amount', key: 'totalAmount', width: 20 },
      { header: 'Date Range', key: 'dateRange', width: 30 }
    ];

    // Add summary data
    metadata.forEach(meta => {
      summarySheet.addRow({
        type: meta.type,
        count: meta.count,
        totalAmount: meta.totalAmount,
        dateRange: `${startDate.toDateString()} - ${endDate.toDateString()}`
      });
    });

    // Style the header
    summarySheet.getRow(1).font = { bold: true };
    summarySheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();

    // Upload to S3
    const s3Key = `analytics-documents/${clinicId}/${requestId}/analytics-report.xlsx`;

    await this.s3Service.uploadFile({
      key: s3Key,
      body: Buffer.from(buffer),
      contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });

    return s3Key;
  }

  private async sendGeneratedDocuments(
    job: AnalyticsDocumentGenerationJob,
    pdfS3Key: string,
    excelS3Key?: string
  ): Promise<void> {

    try {
      // Get pre-signed URLs for the documents
      const pdfUrl = await this.s3Service.getSignedUrl(pdfS3Key, 3600); // 1 hour expiry
      const excelUrl = excelS3Key ? await this.s3Service.getSignedUrl(excelS3Key, 3600) : undefined;

      // Prepare email content
      const emailSubject = `Analytics Documents - ${job.startDate} to ${job.endDate}`;
      const emailBody = this.generateEmailBody(job, pdfUrl, excelUrl);

      // Send via email if requested
      if (job.shareMode.includes(ShareMode.EMAIL) && job.email) {
        await this.sendDocuments.sendEmailWithAttachments({
          to: job.email,
          subject: emailSubject,
          body: emailBody,
          attachments: [
            {
              filename: 'analytics-documents.pdf',
              url: pdfUrl
            },
            ...(excelUrl ? [{
              filename: 'analytics-report.xlsx',
              url: excelUrl
            }] : [])
          ]
        });
      }

      // Send via WhatsApp if requested
      if (job.shareMode.includes(ShareMode.WHATSAPP) && job.phoneNumber) {
        await this.sendDocuments.sendWhatsAppMessage({
          phoneNumber: job.phoneNumber,
          message: `Your analytics documents are ready. PDF: ${pdfUrl}${excelUrl ? ` Excel: ${excelUrl}` : ''}`,
          documentUrl: pdfUrl
        });
      }

    } catch (error) {
      this.logger.error('Error sending generated documents', {
        requestId: job.requestId,
        error: error.message
      });
      // Don't throw here as document generation was successful
    }
  }

  private generateEmailBody(
    job: AnalyticsDocumentGenerationJob,
    pdfUrl: string,
    excelUrl?: string
  ): string {

    const documentTypes = job.documentTypes.join(', ');
    const dateRange = `${job.startDate} to ${job.endDate}`;

    return `
      <h2>Analytics Documents Ready</h2>
      <p>Your requested analytics documents have been generated successfully.</p>

      <h3>Details:</h3>
      <ul>
        <li><strong>Document Types:</strong> ${documentTypes}</li>
        <li><strong>Date Range:</strong> ${dateRange}</li>
        <li><strong>Generated On:</strong> ${new Date().toLocaleString()}</li>
      </ul>

      <h3>Downloads:</h3>
      <ul>
        <li><a href="${pdfUrl}">Download PDF Documents</a></li>
        ${excelUrl ? `<li><a href="${excelUrl}">Download Excel Report</a></li>` : ''}
      </ul>

      <p><em>Note: These links will expire in 1 hour for security purposes.</em></p>

      <p>Best regards,<br/>Your Analytics Team</p>
    `;
  }

  async updateRequestStatus(
    requestId: string,
    status: 'processing' | 'completed' | 'failed',
    s3DocumentKey?: string,
    s3ExcelKey?: string,
    errorMessage?: string
  ): Promise<void> {
    await this.requestRepository.update(
      { requestId },
      {
        status,
        s3DocumentKey,
        s3ExcelKey,
        errorMessage,
        updatedAt: new Date()
      }
    );
  }
}

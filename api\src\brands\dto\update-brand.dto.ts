import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, MaxLength } from 'class-validator';

export class UpdateBrandDto {
	@ApiProperty({
		description: 'The name of the brand',
		example: 'Updated Brand Name'
	})
	@IsNotEmpty({ message: 'Brand name is required' })
	@IsString({ message: 'Brand name must be a string' })
	@MaxLength(50, { message: 'Brand name must not exceed 50 characters' })
	name!: string;
}

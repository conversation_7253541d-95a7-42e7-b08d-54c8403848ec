import { Test, TestingModule } from '@nestjs/testing';
import { PatientVaccinationsService } from './patient-vaccinations.service';
import { CreatePatientVaccinationDto } from './dto/create-patient-vaccination.dto';
import { getRepositoryToken } from '@nestjs/typeorm';
import { PatientVaccination } from './entities/patient-vaccinations.entity';
import { Repository, DataSource, IsNull } from 'typeorm';
import { Patient } from '../patients/entities/patient.entity';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { UpdatePatientVaccinationDto } from './dto/update-patient-vaccination.dto';
import { NotFoundException } from '@nestjs/common';
import { S3Service } from '../utils/aws/s3/s3.service';
import { ClinicVaccinationEntity } from '../clinic-vaccinations/entities/clinic-vaccination.entity';

describe('PatientVaccinationsService', () => {
	let service: PatientVaccinationsService;
	let repository: Repository<PatientVaccination>;
	let logger: WinstonLogger;
	let s3Service: S3Service;
	let dataSource: DataSource;

	const mockCreatePatientVaccinationDto: CreatePatientVaccinationDto = {
		patientId: 'p_1',
		systemGenerated: true,
		vaccinationDate: new Date(),
		vaccineName: 'co-vaccine'
	};

	const mockPatientVaccination: PatientVaccination = {
		id: 'u_1',
		patientId: 'p_1',
		vaccinationId: 'v_1',
		systemGenerated: true,
		vaccinationDate: new Date(),
		vaccineName: 'co-vaccine',
		createdAt: new Date(),
		updatedAt: new Date(),
		appointment: {} as AppointmentEntity,
		reportUrl: 'some_url.com',
		urlMeta: { file: 'file A', type: 'image' },
		patient: {} as Patient,
		vaccineId: 'v_1',
		vaccination: {} as ClinicVaccinationEntity,
		removedFromInvoice: false
	};

	const mockUpdatePatientVaccinationDto: UpdatePatientVaccinationDto = {
		vaccineName: 'cova-shield'
	};

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				PatientVaccinationsService,
				{
					provide: getRepositoryToken(PatientVaccination),
					useValue: {
						create: jest.fn(),
						save: jest.fn(),
						find: jest.fn(),
						findOne: jest.fn(),
						update: jest.fn(),
						softDelete: jest.fn(),
						createQueryBuilder: jest.fn(() => ({
							where: jest.fn().mockReturnThis(),
							andWhere: jest.fn().mockReturnThis(),
							getMany: jest.fn().mockResolvedValue([]),
							softDelete: jest.fn().mockReturnThis(),
							execute: jest
								.fn()
								.mockResolvedValue({ affected: 1 })
						}))
					}
				},
				{
					provide: WinstonLogger,
					useValue: {
						log: jest.fn(),
						error: jest.fn()
					}
				},
				{
					provide: S3Service,
					useValue: {
						deleteFile: jest.fn().mockResolvedValue(undefined),
						uploadPdfToS3: jest
							.fn()
							.mockResolvedValue('upload-result'),
						getViewPreSignedUrl: jest
							.fn()
							.mockResolvedValue('signed-url')
					}
				},
				{
					provide: DataSource,
					useValue: {
						transaction: jest.fn()
					}
				}
			]
		}).compile();

		service = module.get<PatientVaccinationsService>(
			PatientVaccinationsService
		);
		repository = module.get<Repository<PatientVaccination>>(
			getRepositoryToken(PatientVaccination)
		);
		logger = module.get<WinstonLogger>(WinstonLogger);
		s3Service = module.get<S3Service>(S3Service);
		dataSource = module.get<DataSource>(DataSource);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it('should be defined', () => {
		expect(service).toBeDefined();
	});

	describe('create patient-vaccinations', () => {
		it('should be defined', () => {
			expect(service.create).toBeDefined();
		});

		it('should create patient-vaccination', async () => {
			jest.spyOn(repository, 'save').mockResolvedValue(
				mockPatientVaccination
			);

			const response = await service.create(
				mockCreatePatientVaccinationDto
			);

			expect(repository.save).toHaveBeenCalled();
			expect(response).toEqual(mockPatientVaccination);
		});

		it('should throw error if proper data is not there', async () => {
			jest.spyOn(repository, 'save').mockRejectedValue(
				new Error('something is wrong in data')
			);

			await expect(
				service.create(mockCreatePatientVaccinationDto)
			).rejects.toThrow('something is wrong in data');
		});
	});

	describe('get patient-vaccinations', () => {
		it('should be defined', () => {
			expect(service.get).toBeDefined();
		});

		it('should return all the patient-vaccinations', async () => {
			const mockPatientId: string = 'p_1';
			jest.spyOn(repository, 'find').mockResolvedValue([
				mockPatientVaccination
			]);

			const response = await service.get(mockPatientId);
			expect(repository.find).toHaveBeenCalled();
			expect(response).toEqual([mockPatientVaccination]);
		});

		it('should throw error if there is an issue fetching patient-vaccinations', async () => {
			jest.spyOn(repository, 'find').mockRejectedValue(
				new Error('something is wrong in data')
			);

			await expect(service.get('p_1')).rejects.toThrow(
				'something is wrong in data'
			);
		});
	});

	describe('update patient-vaccination', () => {
		it('should be defined', () => {
			expect(service.update).toBeDefined();
		});

		it('should update vaccination correctly', async () => {
			const mockId = 'm_1';
			const updatedPatientVaccination = {
				...mockPatientVaccination,
				...mockUpdatePatientVaccinationDto
			};

			jest.spyOn(repository, 'findOne').mockResolvedValue(
				mockPatientVaccination
			);
			jest.spyOn(repository, 'save').mockResolvedValue(
				updatedPatientVaccination
			);

			const response = await service.update(
				mockId,
				mockUpdatePatientVaccinationDto
			);

			expect(repository.findOne).toHaveBeenCalledWith({
				where: { id: mockId, deletedAt: IsNull() }
			});
			expect(repository.save).toHaveBeenCalledWith(
				updatedPatientVaccination
			);
			expect(response).toEqual(updatedPatientVaccination);
		});

		it('should throw not found exception if patient_vaccination is not there', async () => {
			const mockId: string = 'm_1';
			jest.spyOn(repository, 'findOne').mockResolvedValue(null);

			await expect(
				service.update(mockId, mockUpdatePatientVaccinationDto)
			).rejects.toThrow(NotFoundException);
		});
	});

	describe('softDeleteByAppointmentId', () => {
		const appointmentId = 'appointment_123';
		const mockVaccinationsWithReports = [
			{
				...mockPatientVaccination,
				id: 'v1',
				reportUrl: 'file1.pdf',
				appointmentId
			},
			{
				...mockPatientVaccination,
				id: 'v2',
				reportUrl: 'file2.pdf',
				appointmentId
			}
		];

		it('should be defined', () => {
			expect(service.softDeleteByAppointmentId).toBeDefined();
		});

		it('should soft delete vaccinations and handle S3 deletion', async () => {
			// Mock the transaction to call the callback with a mock entity manager
			const mockEntityManager = {
				getRepository: jest.fn().mockReturnValue({
					find: jest
						.fn()
						.mockResolvedValue(mockVaccinationsWithReports),
					softDelete: jest.fn().mockResolvedValue({ affected: 2 })
				})
			};

			// @ts-ignore - Ignore TypeScript type checking for mock
			jest.spyOn(dataSource, 'transaction').mockImplementation(
				async (callback: any) => {
					return await callback(mockEntityManager as any);
				}
			);

			// Mock setImmediate to execute callback immediately
			jest.spyOn(global, 'setImmediate').mockImplementation(
				(callback: any) => {
					callback();
					return {} as NodeJS.Immediate;
				}
			);

			await service.softDeleteByAppointmentId(appointmentId);

			expect(dataSource.transaction).toHaveBeenCalled();
			expect(mockEntityManager.getRepository).toHaveBeenCalledWith(
				PatientVaccination
			);
			expect(logger.log).toHaveBeenCalledWith(
				'Found vaccinations to delete',
				{ appointmentId, count: 2 }
			);
			expect(s3Service.deleteFile).toHaveBeenCalledWith('file1.pdf');
			expect(s3Service.deleteFile).toHaveBeenCalledWith('file2.pdf');
		});

		it('should handle no vaccinations found', async () => {
			const mockEntityManager = {
				getRepository: jest.fn().mockReturnValue({
					find: jest.fn().mockResolvedValue([])
				})
			};

			// @ts-ignore - Ignore TypeScript type checking for mock
			jest.spyOn(dataSource, 'transaction').mockImplementation(
				async (callback: any) => {
					return await callback(mockEntityManager as any);
				}
			);

			await service.softDeleteByAppointmentId(appointmentId);

			expect(logger.log).toHaveBeenCalledWith(
				'No vaccinations found to delete',
				{ appointmentId }
			);
		});

		it('should handle S3 deletion errors gracefully', async () => {
			const mockEntityManager = {
				getRepository: jest.fn().mockReturnValue({
					find: jest
						.fn()
						.mockResolvedValue(mockVaccinationsWithReports),
					softDelete: jest.fn().mockResolvedValue({ affected: 2 })
				})
			};

			// @ts-ignore - Ignore TypeScript type checking for mock
			jest.spyOn(dataSource, 'transaction').mockImplementation(
				async (callback: any) => {
					return await callback(mockEntityManager as any);
				}
			);

			jest.spyOn(global, 'setImmediate').mockImplementation(
				(callback: any) => {
					callback();
					return {} as NodeJS.Immediate;
				}
			);

			// Mock S3 service to throw error for first file, succeed for second
			jest.spyOn(s3Service, 'deleteFile')
				.mockRejectedValueOnce(new Error('S3 deletion failed'))
				.mockResolvedValueOnce(undefined);

			await service.softDeleteByAppointmentId(appointmentId);

			expect(logger.error).toHaveBeenCalledWith(
				'Failed to delete S3 file for vaccination',
				{
					appointmentId,
					fileKey: 'file1.pdf',
					error: 'S3 deletion failed'
				}
			);
		});

		it('should use existing entity manager when provided', async () => {
			const mockEntityManager = {
				getRepository: jest.fn().mockReturnValue({
					find: jest
						.fn()
						.mockResolvedValue(mockVaccinationsWithReports),
					softDelete: jest.fn().mockResolvedValue({ affected: 2 })
				})
			};

			await service.softDeleteByAppointmentId(
				appointmentId,
				mockEntityManager as any
			);

			expect(mockEntityManager.getRepository).toHaveBeenCalledWith(
				PatientVaccination
			);
			expect(dataSource.transaction).not.toHaveBeenCalled();
		});

		it('should handle errors in softDeleteInTransaction', async () => {
			const mockEntityManager = {
				getRepository: jest.fn().mockReturnValue({
					find: jest.fn().mockRejectedValue(new Error('Database error'))
				})
			};

			// @ts-ignore - Ignore TypeScript type checking for mock
			jest.spyOn(dataSource, 'transaction').mockImplementation(
				async (callback: any) => {
					return await callback(mockEntityManager as any);
				}
			);

			await expect(
				service.softDeleteByAppointmentId(appointmentId)
			).rejects.toThrow('Database error');

			expect(logger.error).toHaveBeenCalledWith(
				'Error soft deleting patient vaccinations',
				{
					appointmentId,
					error: 'Database error'
				}
			);
		});
	});

	describe('softDeleteByAppointmentAndVaccinationId', () => {
		const appointmentId = 'appointment_123';
		const vaccinationId = 'vaccination_456';
		const mockSpecificVaccinations = [
			{
				...mockPatientVaccination,
				id: 'v1',
				reportUrl: 'specific_file.pdf',
				appointmentId,
				vaccinationId
			}
		];

		it('should be defined', () => {
			expect(
				service.softDeleteByAppointmentAndVaccinationId
			).toBeDefined();
		});

		it('should soft delete specific vaccinations', async () => {
			const mockEntityManager = {
				getRepository: jest.fn().mockReturnValue({
					find: jest.fn().mockResolvedValue(mockSpecificVaccinations),
					softDelete: jest.fn().mockResolvedValue({ affected: 1 })
				})
			};

			// @ts-ignore - Ignore TypeScript type checking for mock
			jest.spyOn(dataSource, 'transaction').mockImplementation(
				async (callback: any) => {
					return await callback(mockEntityManager as any);
				}
			);

			jest.spyOn(global, 'setImmediate').mockImplementation(
				(callback: any) => {
					callback();
					return {} as NodeJS.Immediate;
				}
			);

			await service.softDeleteByAppointmentAndVaccinationId(
				appointmentId,
				vaccinationId
			);

			expect(mockEntityManager.getRepository).toHaveBeenCalledWith(
				PatientVaccination
			);
			expect(logger.log).toHaveBeenCalledWith(
				'Found specific vaccinations to delete',
				{
					appointmentId,
					vaccinationId,
					count: 1
				}
			);
			expect(s3Service.deleteFile).toHaveBeenCalledWith(
				'specific_file.pdf'
			);
		});

		it('should handle no specific vaccinations found', async () => {
			const mockEntityManager = {
				getRepository: jest.fn().mockReturnValue({
					find: jest.fn().mockResolvedValue([])
				})
			};

			// @ts-ignore - Ignore TypeScript type checking for mock
			jest.spyOn(dataSource, 'transaction').mockImplementation(
				async (callback: any) => {
					return await callback(mockEntityManager as any);
				}
			);

			await service.softDeleteByAppointmentAndVaccinationId(
				appointmentId,
				vaccinationId
			);

			expect(logger.log).toHaveBeenCalledWith(
				'No specific vaccinations found to delete',
				{
					appointmentId,
					vaccinationId
				}
			);
		});

		it('should use existing entity manager when provided', async () => {
			const mockEntityManager = {
				getRepository: jest.fn().mockReturnValue({
					find: jest.fn().mockResolvedValue(mockSpecificVaccinations),
					softDelete: jest.fn().mockResolvedValue({ affected: 1 })
				})
			};

			await service.softDeleteByAppointmentAndVaccinationId(
				appointmentId,
				vaccinationId,
				mockEntityManager as any
			);

			expect(mockEntityManager.getRepository).toHaveBeenCalledWith(
				PatientVaccination
			);
			expect(dataSource.transaction).not.toHaveBeenCalled();
		});

		it('should handle errors in softDeleteSpecificInTransaction', async () => {
			const mockEntityManager = {
				getRepository: jest.fn().mockReturnValue({
					find: jest.fn().mockRejectedValue(new Error('Database error'))
				})
			};

			// @ts-ignore - Ignore TypeScript type checking for mock
			jest.spyOn(dataSource, 'transaction').mockImplementation(
				async (callback: any) => {
					return await callback(mockEntityManager as any);
				}
			);

			await expect(
				service.softDeleteByAppointmentAndVaccinationId(
					appointmentId,
					vaccinationId
				)
			).rejects.toThrow('Database error');

			expect(logger.error).toHaveBeenCalledWith(
				'Error soft deleting specific patient vaccinations',
				{
					appointmentId,
					vaccinationId,
					error: 'Database error'
				}
			);
		});
	});

	describe('markAsRemovedFromInvoiceByPatientVaccinationId', () => {
		const patientVaccinationId = 'pv_123';

		it('should be defined', () => {
			expect(
				service.markAsRemovedFromInvoiceByPatientVaccinationId
			).toBeDefined();
		});

		it('should mark vaccination as removed from invoice', async () => {
			const mockManager = {
				findOne: jest.fn().mockResolvedValue(mockPatientVaccination),
				update: jest.fn().mockResolvedValue({ affected: 1 })
			};

			Object.defineProperty(dataSource, 'manager', {
				value: mockManager,
				configurable: true
			});

			await service.markAsRemovedFromInvoiceByPatientVaccinationId(
				patientVaccinationId
			);

			expect(mockManager.findOne).toHaveBeenCalledWith(
				PatientVaccination,
				{
					where: {
						id: patientVaccinationId,
						removedFromInvoice: false,
						deletedAt: IsNull()
					}
				}
			);

			expect(mockManager.update).toHaveBeenCalledWith(
				PatientVaccination,
				{ id: patientVaccinationId },
				{ removedFromInvoice: true }
			);

			expect(logger.log).toHaveBeenCalledWith(
				'Found patient vaccination to mark as removed from invoice',
				{
					patientVaccinationId,
					found: true,
					vaccineName: mockPatientVaccination.vaccineName
				}
			);

			expect(logger.log).toHaveBeenCalledWith(
				'Marked patient vaccination as removed from invoice',
				{
					patientVaccinationId,
					vaccineName: mockPatientVaccination.vaccineName
				}
			);
		});

		it('should handle case when vaccination not found', async () => {
			const mockManager = {
				findOne: jest.fn().mockResolvedValue(null)
			};

			Object.defineProperty(dataSource, 'manager', {
				value: mockManager,
				configurable: true
			});

			await service.markAsRemovedFromInvoiceByPatientVaccinationId(
				patientVaccinationId
			);

			expect(mockManager.findOne).toHaveBeenCalledWith(
				PatientVaccination,
				{
					where: {
						id: patientVaccinationId,
						removedFromInvoice: false,
						deletedAt: IsNull()
					}
				}
			);

			expect(logger.log).toHaveBeenCalledWith(
				'Found patient vaccination to mark as removed from invoice',
				{
					patientVaccinationId,
					found: false,
					vaccineName: undefined
				}
			);

			expect(logger.log).toHaveBeenCalledWith(
				'No patient vaccination found to mark as removed',
				{
					patientVaccinationId
				}
			);
		});

		it('should use provided entity manager', async () => {
			const mockEntityManager = {
				findOne: jest.fn().mockResolvedValue(mockPatientVaccination),
				update: jest.fn().mockResolvedValue({ affected: 1 })
			};

			await service.markAsRemovedFromInvoiceByPatientVaccinationId(
				patientVaccinationId,
				mockEntityManager as any
			);

			expect(mockEntityManager.findOne).toHaveBeenCalledWith(
				PatientVaccination,
				{
					where: {
						id: patientVaccinationId,
						removedFromInvoice: false,
						deletedAt: IsNull()
					}
				}
			);

			expect(mockEntityManager.update).toHaveBeenCalledWith(
				PatientVaccination,
				{ id: patientVaccinationId },
				{ removedFromInvoice: true }
			);
		});

		it('should handle errors gracefully', async () => {
			const mockManager = {
				findOne: jest
					.fn()
					.mockRejectedValue(new Error('Database error'))
			};

			Object.defineProperty(dataSource, 'manager', {
				value: mockManager,
				configurable: true
			});

			await expect(
				service.markAsRemovedFromInvoiceByPatientVaccinationId(
					patientVaccinationId
				)
			).rejects.toThrow('Database error');

			expect(logger.error).toHaveBeenCalledWith(
				'Error marking patient vaccination as removed from invoice',
				{
					patientVaccinationId,
					error: 'Database error'
				}
			);
		});
	});
});


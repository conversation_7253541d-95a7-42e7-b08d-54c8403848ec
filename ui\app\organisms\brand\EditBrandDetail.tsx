import React from 'react';
import { Control, FieldErrors, UseFormRegister } from 'react-hook-form';
import { Button } from '@/app/atoms';
import Input from '@/app/atoms/Input';

interface EditBrandFormData {
    name: string;
}

interface EditBrandFormProps {
    brandId: string;
    control: Control<EditBrandFormData>;
    register: UseFormRegister<EditBrandFormData>;
    handleSubmit: () => void;
    errors: FieldErrors<EditBrandFormData>;
    onCancel: () => void;
}

const EditBrandForm: React.FC<EditBrandFormProps> = ({
    brandId,
    control,
    register,
    handleSubmit,
    errors,
    onCancel,
}) => {
    return (
        <form onSubmit={handleSubmit}>
            <div className="space-y-4">
                <Input
                    id="brand-name"
                    name="name"
                    label="Brand Name"
                    type="text"
                    register={register}
                    errorMessage={errors.name?.message}
                    placeholder="Enter brand name"
                    required
                />
                <div className="flex justify-end gap-2">
                    <Button
                        type="button"
                        variant="secondary"
                        onClick={onCancel}
                        id="cancel-edit-brand"
                    >
                        Cancel
                    </Button>
                    <Button type="submit" id="update-brand-submit">
                        Update Brand
                    </Button>
                </div>
            </div>
        </form>
    );
};

export default EditBrandForm;

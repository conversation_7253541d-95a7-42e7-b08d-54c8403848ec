/* eslint-disable @typescript-eslint/no-unused-vars */
import { Test, TestingModule } from '@nestjs/testing';
import { AppointmentsService } from './appointments.service';
import { AppointmentEntity } from './entities/appointment.entity';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { AppointmentDoctorsEntity } from './entities/appointment-doctor.entity';
import { EnumAppointmentStatus } from './enums/enum-appointment-status';
import { SESMailService } from '../utils/aws/ses/send-mail-service';
import { TasksService } from '../tasks/tasks.service';
import { Patient } from '../patients/entities/patient.entity';
import { ClinicRoomEntity } from '../clinics/entities/clinic-room.entity';
import { AppointmentDetailsEntity } from './entities/appointment-details.entity';
import { CreateAppointmentDto } from './dto/create/create-appointment.dto';
import { UpdateAppointmentsDto } from './dto/create/update-appointment.dto';
import {
	NotFoundException,
	BadRequestException,
	InternalServerErrorException
} from '@nestjs/common';
import { EnumAppointmentType } from './enums/enum-appointment-type';
import { EnumAppointmentTriage } from './enums/enum-appointment-triage';
import { CartEntity } from '../carts/entites/cart.entity';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { UpdateAppointmentFeildsDto } from './dto/create/update-appointmentField.dto';
import { WhatsappService } from '../utils/whatsapp-integration/whatsapp.service';
import { PatientRemindersService } from '../patient-reminders/patient-reminder.service';
import { EmrService } from '../emr/emr.service';
import { SqsService } from '../utils/aws/sqs/sqs.service';
import { GlobalReminderService } from '../patient-global-reminders/global-reminders.service';
import { AvailabilityService } from '../availability/availability.service';
import { LongTermMedicationsService } from '../long-term-medications/long-term-medications.service';
import { AppointmentGateway } from '../socket/socket.appointment.gateway';
import { RedisService } from '../utils/redis/redis.service';
import { UpdateAppointmentDetailsDto } from './dto/details/update-appointment-details.dto';

describe('AppointmentsService', () => {
	let service: AppointmentsService;
	let appointmentsRepository: jest.Mocked<Repository<AppointmentEntity>>;
	let appointmentDoctorsRepository: jest.Mocked<
		Repository<AppointmentDoctorsEntity>
	>;
	let appointmentDetailsRepository: jest.Mocked<
		Repository<AppointmentDetailsEntity>
	>;
	let mailService: jest.Mocked<SESMailService>;
	let taskService: jest.Mocked<TasksService>;
	let logger: jest.Mocked<WinstonLogger>;
	let whatsappService: jest.Mocked<WhatsappService>;

	const mockPatient = {
		id: 'patient_1',
		patientName: 'Fluffy',
		patientOwners: [
			{
				ownerBrand: {
					firstName: 'John',
					lastName: 'Doe',
					email: '<EMAIL>',
					globalOwner: {
						countryCode: '91',
						phoneNumber: '**********'
					}
				}
			}
		]
	} as unknown as Patient;

	const mockAppointments: AppointmentEntity = {
		id: 'uuid_appointment_1',
		clinicId: 'clinic_1',
		brandId: 'brand_1',
		patientId: 'patient_1',
		roomId: 'room_1',
		reason: 'checkup',
		type: EnumAppointmentType.GeneralCheckup,
		triage: EnumAppointmentTriage.Low,
		date: new Date(),
		startTime: new Date(),
		endTime: new Date(),
		isBlocked: false,
		status: EnumAppointmentStatus.Scheduled,
		createdAt: new Date(),
		updatedAt: new Date(),
		createdBy: 'user_1',
		updatedBy: 'user_1',
		patient: mockPatient,
		appointmentDoctors: [],
		room: {} as ClinicRoomEntity,
		appointmentDetails: {} as AppointmentDetailsEntity,
		labReports: [],
		clinic: {
			name: 'Test Clinic',
			brand: {
				name: 'Test Brand'
			},
			phoneNumbers: [
				{
					number: '**********'
				}
			],
			addressLine1: '123 Main St',
			addressLine2: 'Suite 100',
			city: 'Test City',
			state: 'Test State',
			addressPincode: '12345',
			country: 'Test Country'
		} as any,
		cart: new CartEntity(),
		checkinTime: new Date(),
		checkoutTime: new Date(),
		receivingCareTime: new Date(),
		mode: 'Clinic'
	};

	const mockQueryBuilder = {
		leftJoinAndSelect: jest.fn().mockReturnThis(),
		where: jest.fn().mockReturnThis(),
		andWhere: jest.fn().mockReturnThis(),
		orderBy: jest.fn().mockReturnThis(),
		skip: jest.fn().mockReturnThis(),
		take: jest.fn().mockReturnThis(),
		getManyAndCount: jest.fn().mockResolvedValue([[mockAppointments], 1]),
		addOrderBy: jest.fn().mockReturnThis(),
		addSelect: jest.fn().mockReturnThis()
	};

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				AppointmentsService,
				{
					provide: getRepositoryToken(AppointmentEntity),
					useValue: {
						create: jest.fn(),
						save: jest.fn(),
						findAndCount: jest.fn(),
						findOne: jest.fn(),
						update: jest.fn(),
						find: jest.fn(),
						createQueryBuilder: jest.fn(() => mockQueryBuilder),
						createTask: jest.fn()
					}
				},
				{
					provide: getRepositoryToken(AppointmentDoctorsEntity),
					useValue: {
						save: jest.fn(),
						delete: jest.fn()
					}
				},
				{
					provide: getRepositoryToken(AppointmentDetailsEntity),
					useValue: {
						save: jest.fn(),
						findOne: jest.fn(),
						update: jest.fn()
					}
				},
				{
					provide: SESMailService,
					useValue: {
						sendMail: jest.fn()
					}
				},
				{
					provide: WhatsappService,
					useValue: {
						sendTemplateMessage: jest.fn()
					}
				},
				{
					provide: TasksService,
					useValue: {
						createTask: jest.fn()
					}
				},
				{
					provide: WinstonLogger,
					useValue: {
						log: jest.fn(),
						error: jest.fn(),
						warn: jest.fn()
					}
				},
				{
					provide: PatientRemindersService,
					useValue: {
						findAll: jest.fn(),
						completeReminder: jest.fn()
					}
				},
				{
					provide: EmrService,
					useValue: {
						createEmr: jest.fn()
					}
				},
				{
					provide: SqsService,
					useValue: {
						sendMessage: jest.fn()
					}
				},
				{
					provide: GlobalReminderService,
					useValue: {
						createReminder: jest.fn(),
						updateReminder: jest.fn()
					}
				},
				{
					provide: AvailabilityService,
					useValue: {
						checkAvailability: jest.fn()
					}
				},
				{
					provide: LongTermMedicationsService,
					useValue: {
						findAll: jest.fn()
					}
				},
				{
					provide: AppointmentGateway,
					useValue: {
						emitAppointmentUpdate: jest.fn()
					}
				},
				{
					provide: RedisService,
					useValue: {
						get: jest.fn(),
						set: jest.fn(),
						del: jest.fn(),
						setLock: jest.fn().mockResolvedValue(true),
						releaseLock: jest.fn().mockResolvedValue(true)
					}
				}
			]
		}).compile();

		service = module.get<AppointmentsService>(AppointmentsService);
		appointmentsRepository = module.get(
			getRepositoryToken(AppointmentEntity)
		);
		appointmentDoctorsRepository = module.get(
			getRepositoryToken(AppointmentDoctorsEntity)
		);
		appointmentDetailsRepository = module.get(
			getRepositoryToken(AppointmentDetailsEntity)
		);
		mailService = module.get(SESMailService);
		taskService = module.get(TasksService);
		logger = module.get(WinstonLogger);
	});

	beforeEach(() => {
		// Reset all mock calls before each test
		jest.clearAllMocks();
	});

	it('should be defined', () => {
		expect(service).toBeDefined();
	});

	describe('createAppointment', () => {
		it('should create a new appointment and send an email', async () => {
			const createAppointmentDto: CreateAppointmentDto = {
				date: new Date().toISOString(),
				startTime: new Date(),
				endTime: new Date(),
				patientId: 'patient_1',
				doctorIds: ['doctor_1'],
				providerIds: ['provider_1'],
				clinicId: 'clinic_1',
				roomId: 'room_1',
				reason: 'checkup',
				type: 'General Checkup',
				triage: 'low'
			};

			const mockBrandId = 'brand_1';

			const mockAppointmentDoctor = {
				id: 'doctorAppointment1',
				appointmentId: 'appointment1',
				doctorId: 'doctor_1',
				primary: true,
				clinicUser: {
					id: 'clinicUser1',
					user: {
						firstName: 'John',
						lastName: 'Doe'
					}
				}
			};

			const mockAppointmentWithDetails = {
				...mockAppointments,
				appointmentDoctors: [mockAppointmentDoctor],
				patient: {
					...mockPatient,
					patientOwners: [
						{
							ownerBrand: {
								firstName: 'John',
								lastName: 'Doe',
								email: '<EMAIL>',
								globalOwner: {
									countryCode: '91',
									phoneNumber: '**********'
								}
							}
						}
					]
				},
				clinic: {
					name: 'Test Clinic',
					brand: {
						name: 'Test Brand'
					},
					phoneNumbers: [
						{
							number: '**********'
						}
					]
				}
			};

			appointmentsRepository.create.mockReturnValue(mockAppointments);
			appointmentsRepository.save.mockResolvedValue(mockAppointments);
			appointmentDetailsRepository.save.mockResolvedValue(
				{} as AppointmentDetailsEntity
			);
			appointmentDoctorsRepository.save.mockResolvedValue(
				mockAppointmentDoctor as any
			);
			appointmentsRepository.findOne.mockResolvedValue(
				mockAppointmentWithDetails as any
			);

			const result = await service.createAppointment(
				createAppointmentDto,
				mockBrandId
			);

			expect(result).toEqual(mockAppointments);
			expect(appointmentsRepository.create).toHaveBeenCalledWith(
				expect.objectContaining({
					...createAppointmentDto,
					date: expect.any(Date),
					brandId: mockBrandId
				})
			);
			expect(appointmentsRepository.save).toHaveBeenCalled();
			expect(appointmentDetailsRepository.save).toHaveBeenCalled();
			expect(mailService.sendMail).toHaveBeenCalled();
		});

		it('should throw an error if appointment creation fails', async () => {
			appointmentsRepository.create.mockImplementation(() => {
				throw new InternalServerErrorException(
					'Appointment creation failed'
				);
			});

			await expect(
				service.createAppointment({} as CreateAppointmentDto, 'brand_1')
			).rejects.toThrow('Appointment creation failed');
		});

		it('should throw InternalServerErrorException if saving appointment entity fails', async () => {
			const createAppointmentDto: CreateAppointmentDto = {
				date: new Date().toISOString(),
				startTime: new Date(),
				endTime: new Date(),
				patientId: 'patient_1',
				doctorIds: ['doctor_1'],
				providerIds: [],
				clinicId: 'clinic_1',
				roomId: 'room_1',
				reason: 'checkup',
				type: 'General Checkup',
				triage: 'low'
			};

			appointmentsRepository.create.mockReturnValue(mockAppointments);
			appointmentsRepository.save.mockResolvedValue(null as any); // Simulate save failure

			await expect(
				service.createAppointment(createAppointmentDto, 'brand_1')
			).rejects.toThrow(InternalServerErrorException);
		});

		it('should throw InternalServerErrorException if saving appointment details fails', async () => {
			const createAppointmentDto: CreateAppointmentDto = {
				date: new Date().toISOString(),
				startTime: new Date(),
				endTime: new Date(),
				patientId: 'patient_1',
				doctorIds: ['doctor_1'],
				providerIds: [],
				clinicId: 'clinic_1',
				roomId: 'room_1',
				reason: 'checkup',
				type: 'General Checkup',
				triage: 'low'
			};

			appointmentsRepository.create.mockReturnValue(mockAppointments);
			appointmentsRepository.save.mockResolvedValue(mockAppointments);
			appointmentDetailsRepository.save.mockResolvedValue(null as any); // Simulate details save failure

			await expect(
				service.createAppointment(createAppointmentDto, 'brand_1')
			).rejects.toThrow(InternalServerErrorException);
		});

		it('should create appointment with both doctors and providers', async () => {
			const createAppointmentDto: CreateAppointmentDto = {
				date: new Date().toISOString(),
				startTime: new Date(),
				endTime: new Date(),
				patientId: 'patient_1',
				doctorIds: ['doctor_1', 'doctor_2'],
				providerIds: ['provider_1'],
				clinicId: 'clinic_1',
				roomId: 'room_1',
				reason: 'checkup',
				type: 'General Checkup',
				triage: 'low'
			};

			const mockAppointmentDoctor = {
				id: 'doctorAppointment1',
				appointmentId: 'appointment1',
				doctorId: 'doctor_1',
				primary: true
			};

			appointmentsRepository.create.mockReturnValue(mockAppointments);
			appointmentsRepository.save.mockResolvedValue(mockAppointments);
			appointmentDetailsRepository.save.mockResolvedValue({
				id: 'details_1',
				appointmentId: 'appointment1',
				details: {},
				prescriptionCreatedAt: new Date(),
				appointment: mockAppointments
			} as any);
			appointmentDoctorsRepository.save.mockResolvedValue(
				mockAppointmentDoctor as any
			);

			const result = await service.createAppointment(
				createAppointmentDto,
				'brand_1'
			);

			expect(result).toEqual(mockAppointments);
			expect(appointmentDoctorsRepository.save).toHaveBeenCalledTimes(3); // 2 doctors + 1 provider
		});
	});

	describe('getAllAppointments', () => {
		it('should return the list of appointments', async () => {
			const expectedOutput = {
				appointments: [mockAppointments],
				total: 1
			};

			const result = await service.getAllAppointments(
				1,
				10,
				'ASC',
				new Date().toISOString()
			);

			expect(result).toEqual(expectedOutput);
			expect(
				appointmentsRepository.createQueryBuilder
			).toHaveBeenCalled();
		});

		it('should filter appointments by specific date', async () => {
			const testDate = '2023-12-01';
			const expectedOutput = {
				appointments: [mockAppointments],
				total: 1
			};

			const result = await service.getAllAppointments(
				1,
				10,
				'DESC',
				testDate
			);

			expect(result).toEqual(expectedOutput);
			expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
				'appointment.date BETWEEN :startDate AND :endDate',
				expect.any(Object)
			);
		});

		it('should filter appointments by single doctor ID', async () => {
			const doctorIds = ['doctor_1'];
			const expectedOutput = {
				appointments: [mockAppointments],
				total: 1
			};

			const result = await service.getAllAppointments(
				1,
				10,
				'DESC',
				new Date().toISOString(),
				'',
				doctorIds,
				[],
				false,
				''
			);

			expect(result).toEqual(expectedOutput);
			// Should have 4 andWhere calls: clinicId, type, date, and doctors filter
			expect(mockQueryBuilder.andWhere).toHaveBeenCalledTimes(4);
		});

		it('should filter appointments by multiple doctor IDs', async () => {
			const doctorIds = ['doctor_1', 'doctor_2'];
			const expectedOutput = {
				appointments: [mockAppointments],
				total: 1
			};

			const result = await service.getAllAppointments(
				1,
				10,
				'DESC',
				new Date().toISOString(),
				'',
				doctorIds,
				[],
				false,
				''
			);

			expect(result).toEqual(expectedOutput);
			// Should have 4 andWhere calls: clinicId, type, date, and doctors filter
			expect(mockQueryBuilder.andWhere).toHaveBeenCalledTimes(4);
		});

		it('should filter appointments by single status', async () => {
			const statuses = ['Scheduled'];
			const expectedOutput = {
				appointments: [mockAppointments],
				total: 1
			};

			const result = await service.getAllAppointments(
				1,
				10,
				'DESC',
				new Date().toISOString(),
				'',
				[],
				statuses,
				false,
				''
			);

			expect(result).toEqual(expectedOutput);
			expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
				'status IN (:...status)',
				expect.any(Object)
			);
		});

		it('should filter appointments by multiple statuses', async () => {
			const statuses = ['Scheduled', 'Checkedin'];
			const expectedOutput = {
				appointments: [mockAppointments],
				total: 1
			};

			const result = await service.getAllAppointments(
				1,
				10,
				'DESC',
				new Date().toISOString(),
				'',
				[],
				statuses,
				false,
				''
			);

			expect(result).toEqual(expectedOutput);
			expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
				'status IN (:...status)',
				expect.any(Object)
			);
		});

		it('should return only primary doctor appointments when onlyPrimary is true', async () => {
			const expectedOutput = {
				appointments: [mockAppointments],
				total: 1
			};

			const result = await service.getAllAppointments(
				1,
				10,
				'DESC',
				new Date().toISOString(),
				'',
				[],
				[],
				true,
				''
			);

			expect(result).toEqual(expectedOutput);
			expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
				'appointmentDoctors.primary = :isPrimary',
				expect.any(Object)
			);
		});

		it('should search appointments by patient name', async () => {
			const searchTerm = 'Fluffy';
			const expectedOutput = {
				appointments: [mockAppointments],
				total: 1
			};

			const result = await service.getAllAppointments(
				1,
				10,
				'DESC',
				new Date().toISOString(),
				searchTerm,
				[],
				[],
				false,
				''
			);

			expect(result).toEqual(expectedOutput);
			// Should have 4 andWhere calls: clinicId, type, date, and search filter
			expect(mockQueryBuilder.andWhere).toHaveBeenCalledTimes(4);
		});
	});

	describe('getAppointmentsForPatient', () => {
		it('should return appointments for a patient', async () => {
			// Mock the appointment with proper appointmentDetails structure
			const mockAppointmentWithDetails = {
				...mockAppointments,
				appointmentDetails: {
					id: 'details_1',
					appointmentId: 'uuid_appointment_1',
					details: {
						subjective: 'Test subjective',
						objective: {
							vitals: [],
							physicalExam: [],
							ultrasoundExam: [],
							labReports: [],
							bodyMaps: []
						},
						assessment: {
							list: [],
							notes: ''
						},
						plans: {
							list: [],
							notes: ''
						},
						prescription: {
							list: [],
							notes: ''
						},
						followup: null,
						invoiceAmount: 0,
						attachments: {
							list: []
						}
					},
					createdAt: new Date(),
					updatedAt: new Date()
				} as AppointmentDetailsEntity
			};

			appointmentsRepository.find.mockResolvedValue([
				mockAppointmentWithDetails
			]);

			const result = await service.getAppointmentsForPatient('patient_1');

			// The service injects appointmentId into details, so we expect that modification
			const expectedResult = [
				{
					...mockAppointmentWithDetails,
					appointmentDetails: {
						...mockAppointmentWithDetails.appointmentDetails,
						details: {
							...mockAppointmentWithDetails.appointmentDetails
								.details,
							appointmentId: 'uuid_appointment_1'
						}
					}
				}
			];

			expect(result).toEqual(expectedResult);
			expect(appointmentsRepository.find).toHaveBeenCalled();
		});
	});

	describe('updateAppointmentStatus', () => {
		const mockTaskService = {
			createTask: jest.fn()
		};
		it('should update the appointment status successfully', async () => {
			const updateDto: UpdateAppointmentsDto = {
				status: EnumAppointmentStatus.Checkedin
			};

			appointmentsRepository.findOne.mockResolvedValue(mockAppointments);
			appointmentsRepository.save.mockResolvedValue({
				...mockAppointments,
				status: EnumAppointmentStatus.Checkedin
			});

			const result = await service.updateAppointmentStatus(
				'uuid_appointment_1',
				updateDto
			);

			expect(result.status).toEqual(EnumAppointmentStatus.Checkedin);
		});

		it('should throw NotFoundException if the appointment is not found', async () => {
			appointmentsRepository.findOne.mockResolvedValue(null);

			await expect(
				service.updateAppointmentStatus('uuid_appointment_1', {
					status: EnumAppointmentStatus.Checkedin
				})
			).rejects.toThrow(NotFoundException);
		});

		it('should throw BadRequestException for invalid status transition', async () => {
			const updateDto: UpdateAppointmentsDto = {
				status: EnumAppointmentStatus.Completed // Invalid transition for this example
			};

			appointmentsRepository.findOne.mockResolvedValue(mockAppointments);

			await expect(
				service.updateAppointmentStatus('uuid_appointment_1', updateDto)
			).rejects.toThrow(BadRequestException);
		});
	});

	describe('deleteAppointment', () => {
		it('should delete an appointment by marking it as deleted', async () => {
			appointmentsRepository.findOne.mockResolvedValue(mockAppointments);
			appointmentsRepository.update.mockResolvedValue({
				affected: 1,
				raw: {},
				generatedMaps: []
			});

			const result =
				await service.deleteAppointment('uuid_appointment_1');

			expect(result).toEqual({ status: true });
			expect(appointmentsRepository.update).toHaveBeenCalledWith(
				'uuid_appointment_1',
				{ deletedAt: expect.any(Date) }
			);
		});

		it('should throw NotFoundException if the appointment is not found', async () => {
			appointmentsRepository.findOne.mockResolvedValue(null);

			await expect(
				service.deleteAppointment('uuid_appointment_1')
			).rejects.toThrow(NotFoundException);
		});
	});

	// describe('sendAppointmentMailBy24thHour', () => {
	// 	it('should send reminder emails for appointments 24 hours in advance', async () => {
	// 		appointmentsRepository.find.mockResolvedValue([mockAppointments]);

	// 		await service.sendAppointmentMailBy24thHour();

	// 		expect(mailService.sendMail).toHaveBeenCalled();
	// 	});

	// 	it('should not send emails if no appointments are found', async () => {
	// 		appointmentsRepository.find.mockResolvedValue([]);

	// 		await service.sendAppointmentMailBy24thHour();

	// 		expect(mailService.sendMail).not.toHaveBeenCalled();
	// 	});
	// });

	describe('updateAppointment', () => {
		it('should be defined', () => {
			expect(service.updateAppointment).toBeDefined();
		});

		it('should update the appointment field', async () => {
			const mockId = 'uuid';
			const mockUpdateAppointmentFieldDto: UpdateAppointmentFeildsDto = {
				doctorIds: ['uuid1'],
				patientId: 'p_1',
				providerIds: ['pp1'],
				startTime: new Date()
			};
			const mockAppointmentDoctor = {
				id: 'doctorAppointment1',
				appointmentId: 'appointment1',
				doctorId: 'doctor_1',
				primary: true,
				clinicUser: {
					id: 'clinicUser1',
					user: {
						firstName: 'John',
						lastName: 'Doe'
					}
				}
			} as AppointmentDoctorsEntity;

			appointmentsRepository.findOne.mockResolvedValue(mockAppointments);
			appointmentDoctorsRepository.delete.mockResolvedValue({
				affected: 1,
				raw: {}
			});
			appointmentDoctorsRepository.save.mockResolvedValue(
				mockAppointmentDoctor
			);

			const response = await service.updateAppointment(
				mockId,
				mockUpdateAppointmentFieldDto
			);

			expect(mailService.sendMail).toHaveBeenCalled();
			expect(appointmentDoctorsRepository.delete).toHaveBeenCalled();
			expect(appointmentDoctorsRepository.save).toHaveBeenCalledTimes(2);
			expect(appointmentsRepository.findOne).toHaveBeenCalled();
		});

		it('should throw error if appointment is not found', async () => {
			const mockId = 'uuid';
			const mockUpdateAppointmentFieldDto: UpdateAppointmentFeildsDto = {
				doctorIds: ['uuid1'],
				patientId: 'p_1',
				providerIds: ['pp1'],
				startTime: new Date()
			};

			appointmentsRepository.findOne.mockResolvedValue(null);

			await expect(
				service.updateAppointment(mockId, mockUpdateAppointmentFieldDto)
			).rejects.toThrow(NotFoundException);
		});
	});

	describe('getAppointmentDetails', () => {
		it('should return appointment details successfully', async () => {
			const mockQueryBuilder = {
				leftJoinAndSelect: jest.fn().mockReturnThis(),
				where: jest.fn().mockReturnThis(),
				getOne: jest.fn().mockResolvedValue(mockAppointments)
			};

			appointmentsRepository.createQueryBuilder.mockReturnValue(
				mockQueryBuilder as any
			);
			appointmentDetailsRepository.findOne.mockResolvedValue({
				id: 'details_1',
				appointmentId: 'uuid_appointment_1',
				details: { subjective: 'Test' },
				createdAt: new Date(),
				updatedAt: new Date(),
				prescriptionCreatedAt: new Date(),
				appointment: mockAppointments
			} as any);

			const result =
				await service.getAppointmentDetails('uuid_appointment_1');

			expect(result).toBeDefined();
			expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalled();
			expect(mockQueryBuilder.where).toHaveBeenCalled();
		});

		it('should throw NotFoundException if appointment not found', async () => {
			const mockQueryBuilder = {
				leftJoinAndSelect: jest.fn().mockReturnThis(),
				where: jest.fn().mockReturnThis(),
				getOne: jest.fn().mockResolvedValue(null)
			};

			appointmentsRepository.createQueryBuilder.mockReturnValue(
				mockQueryBuilder as any
			);

			await expect(
				service.getAppointmentDetails('non-existent-id')
			).rejects.toThrow(NotFoundException);
		});
	});

	describe('checkPatientOnGoingAppointment', () => {
		it('should return true if patient has ongoing appointment', async () => {
			appointmentsRepository.findOne.mockResolvedValue(mockAppointments);

			const result =
				await service.checkPatientOnGoingAppointment('patient_1');

			expect(result.hasOngoingAppointment).toBe(true);
			expect(result.appointment).toEqual(mockAppointments);
		});

		it('should return false if patient has no ongoing appointment', async () => {
			appointmentsRepository.findOne.mockResolvedValue(null);

			const result =
				await service.checkPatientOnGoingAppointment('patient_1');

			expect(result.hasOngoingAppointment).toBe(false);
			expect(result.appointment).toBeNull();
		});
	});

	describe('updateAppointmentDetails', () => {
		it('should update appointment details successfully', async () => {
			const updateDto: UpdateAppointmentDetailsDto = {
				appointmentId: 'uuid_appointment_1',
				details: {
					subjective: 'Updated subjective',
					objective: {
						vitals: [],
						physicalExam: [],
						ultrasoundExam: [],
						labReports: [],
						bodyMaps: []
					},
					assessment: { list: [], notes: 'Updated assessment' },
					plans: { list: [], notes: 'Updated plans' },
					prescription: { list: [], notes: 'Updated prescription' },
					followup: null,
					invoiceAmount: 100,
					attachments: { list: [] }
				}
			};

			// Mock Redis operations
			const redisService = {
				set: jest.fn().mockResolvedValue('OK'),
				get: jest.fn().mockResolvedValue(null),
				del: jest.fn().mockResolvedValue(1)
			};

			// Mock appointment repository findOne
			appointmentsRepository.findOne.mockResolvedValue(mockAppointments);

			appointmentDetailsRepository.findOne.mockResolvedValue({
				id: 'details_1',
				appointmentId: 'uuid_appointment_1',
				details: { subjective: 'Old subjective' },
				createdAt: new Date(),
				updatedAt: new Date(),
				prescriptionCreatedAt: new Date(),
				appointment: mockAppointments
			} as any);

			// Mock update method to return UpdateResult
			appointmentDetailsRepository.update.mockResolvedValue({
				affected: 1,
				raw: {},
				generatedMaps: []
			} as any);

			// Mock the second findOne call (after update) to return updated details
			appointmentDetailsRepository.findOne
				.mockResolvedValueOnce({
					id: 'details_1',
					appointmentId: 'uuid_appointment_1',
					details: { subjective: 'Old subjective' },
					createdAt: new Date(),
					updatedAt: new Date(),
					prescriptionCreatedAt: new Date(),
					appointment: mockAppointments
				} as any)
				.mockResolvedValueOnce({
					id: 'details_1',
					appointmentId: 'uuid_appointment_1',
					details: updateDto.details,
					createdAt: new Date(),
					updatedAt: new Date(),
					prescriptionCreatedAt: new Date(),
					appointment: mockAppointments
				} as any);

			const result = await service.updateAppointmentDetails(
				'uuid_appointment_1',
				updateDto
			);

			expect(result).toBeDefined();
			expect(appointmentDetailsRepository.update).toHaveBeenCalled();
		});

		it('should throw NotFoundException if appointment details not found', async () => {
			appointmentDetailsRepository.findOne.mockResolvedValue(null);

			await expect(
				service.updateAppointmentDetails(
					'non-existent-id',
					{} as UpdateAppointmentDetailsDto
				)
			).rejects.toThrow(NotFoundException);
		});
	});

	afterEach(() => {
		jest.clearAllMocks();
	});
});

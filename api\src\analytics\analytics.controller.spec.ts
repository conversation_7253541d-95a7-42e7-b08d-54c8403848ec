import { Test, TestingModule } from '@nestjs/testing';
import { Reflector } from '@nestjs/core';
import { Response } from 'express';
import { AnalyticsController } from './analytics.controller';
import { AnalyticsService } from './analytics.service';
import { RoleService } from '../roles/role.service';
import {
	AnalyticsType,
	AnalyticsReportType,
	AppointmentAnalyticsType,
	RevenueChartDataPoint,
	CollectedPaymentsChartDataPoint,
	AppointmentsChartResponse,
	DoctorSummaryResponseDto,
	SummaryResponseDto
} from './dto/analytics.dto';

describe('AnalyticsController', () => {
	let controller: AnalyticsController;
	let service: AnalyticsService;

	const mockAnalyticsService = {
		getRevenueChartData: jest.fn(),
		getCollectedPaymentsChartData: jest.fn(),
		getAppointmentsChartData: jest.fn(),
		getDoctorSummary: jest.fn(),
		getSummary: jest.fn(),
		generateReport: jest.fn()
	};

	const mockResponse = {
		set: jest.fn(),
		setHeader: jest.fn(),
		send: jest.fn(),
		end: jest.fn(),
		status: jest.fn().mockReturnThis(),
		json: jest.fn().mockReturnThis()
	} as unknown as Response;

	beforeEach(async () => {
		const mockRoleService = {
			findById: jest.fn(),
			findOneById: jest.fn(),
			findByName: jest.fn(),
			findAll: jest.fn()
		};

		const mockReflector = {
			get: jest.fn(),
			getAll: jest.fn(),
			getAllAndOverride: jest.fn(),
			getAllAndMerge: jest.fn()
		};

		const module: TestingModule = await Test.createTestingModule({
			controllers: [AnalyticsController],
			providers: [
				{
					provide: AnalyticsService,
					useValue: mockAnalyticsService
				},
				{
					provide: RoleService,
					useValue: mockRoleService
				},
				{
					provide: Reflector,
					useValue: mockReflector
				}
			]
		}).compile();

		controller = module.get<AnalyticsController>(AnalyticsController);
		service = module.get<AnalyticsService>(AnalyticsService);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
		expect(service).toBeDefined();
	});

	describe('getRevenueChartData', () => {
		it('should return revenue chart data successfully', async () => {
			// Arrange
			const startDate = '2023-01-01';
			const endDate = '2023-01-31';
			const clinicId = 'clinic-123';

			const expectedResult: RevenueChartDataPoint[] = [
				{
					date: '2023-01-01',
					products: 100,
					services: 200,
					diagnostics: 50,
					medications: 75,
					vaccinations: 25
				}
			];

			mockAnalyticsService.getRevenueChartData.mockResolvedValue(
				expectedResult
			);

			// Act
			const result = await controller.getRevenueChartData({
				startDate,
				endDate,
				clinicId
			});

			// Assert
			expect(service.getRevenueChartData).toHaveBeenCalledTimes(1);
			expect(service.getRevenueChartData).toHaveBeenCalledWith({
				startDate,
				endDate,
				clinicId
			});
			expect(result).toEqual(expectedResult);
		});

		it('should handle service errors properly', async () => {
			// Arrange
			const startDate = '2023-01-01';
			const endDate = '2023-01-31';
			const clinicId = 'clinic-123';
			const error = new Error('Database connection failed');

			mockAnalyticsService.getRevenueChartData.mockRejectedValue(error);

			// Act & Assert
			await expect(
				controller.getRevenueChartData({ startDate, endDate, clinicId })
			).rejects.toThrow('Database connection failed');

			expect(service.getRevenueChartData).toHaveBeenCalledWith({
				startDate,
				endDate,
				clinicId
			});
		});
	});

	describe('getCollectedPaymentsChartData', () => {
		it('should return collected payments chart data successfully', async () => {
			// Arrange
			const startDate = '2023-01-01';
			const endDate = '2023-01-31';
			const clinicId = 'clinic-123';

			const expectedResult: CollectedPaymentsChartDataPoint[] = [
				{
					date: '2023-01-01',
					cash: 100,
					card: 200,
					wallet: 50,
					cheque: 75,
					bankTransfer: 25
				}
			];

			mockAnalyticsService.getCollectedPaymentsChartData.mockResolvedValue(
				expectedResult
			);

			// Act
			const result = await controller.getCollectedPaymentsChartData({
				startDate,
				endDate,
				clinicId
			});

			// Assert
			expect(service.getCollectedPaymentsChartData).toHaveBeenCalledTimes(
				1
			);
			expect(service.getCollectedPaymentsChartData).toHaveBeenCalledWith({
				startDate,
				endDate,
				clinicId
			});
			expect(result).toEqual(expectedResult);
		});

		it('should handle empty payment data', async () => {
			// Arrange
			const startDate = '2023-01-01';
			const endDate = '2023-01-31';
			const clinicId = 'clinic-123';

			mockAnalyticsService.getCollectedPaymentsChartData.mockResolvedValue(
				[]
			);

			// Act
			const result = await controller.getCollectedPaymentsChartData({
				startDate,
				endDate,
				clinicId
			});

			// Assert
			expect(result).toEqual([]);
			expect(result).toHaveLength(0);
		});
	});

	describe('getAppointmentsChartData', () => {
		it('should return appointments chart data for ALL type', async () => {
			// Arrange
			const startDate = '2023-01-01';
			const endDate = '2023-01-31';
			const clinicId = 'clinic-123';
			const type = AppointmentAnalyticsType.ALL;

			const expectedResult: AppointmentsChartResponse = {
				total: [{ date: '2023-01-01', total: 10 }],
				missed: [{ date: '2023-01-01', missed: 2 }]
			};

			mockAnalyticsService.getAppointmentsChartData.mockResolvedValue(
				expectedResult
			);

			// Act
			const result = await controller.getAppointmentsChartData({
				startDate,
				endDate,
				clinicId,
				type
			});

			// Assert
			expect(service.getAppointmentsChartData).toHaveBeenCalledWith({
				startDate,
				endDate,
				clinicId,
				type
			});
			expect(result).toEqual(expectedResult);
		});

		it('should handle different appointment analytics types', async () => {
			// Arrange
			const startDate = '2023-01-01';
			const endDate = '2023-01-31';
			const clinicId = 'clinic-123';
			const type = AppointmentAnalyticsType.BUSIEST_DAYS;

			const expectedResult: AppointmentsChartResponse = {
				total: [],
				missed: [],
				busiestDays: [
					{
						day: 'Monday',
						count: 15,
						weeksCount: 4,
						total: 60
					}
				]
			};

			mockAnalyticsService.getAppointmentsChartData.mockResolvedValue(
				expectedResult
			);

			// Act
			const result = await controller.getAppointmentsChartData({
				startDate,
				endDate,
				clinicId,
				type
			});

			// Assert
			expect(service.getAppointmentsChartData).toHaveBeenCalledWith({
				startDate,
				endDate,
				clinicId,
				type
			});
			expect(result.busiestDays).toBeDefined();
			expect(result.busiestDays).toHaveLength(1);
		});
	});

	describe('getDoctorSummary', () => {
		it('should return doctor summary data', async () => {
			// Arrange
			const startDate = '2023-01-01';
			const endDate = '2023-01-31';
			const clinicId = 'clinic-123';

			const expectedResult: DoctorSummaryResponseDto[] = [
				{
					doctorName: 'Dr. Smith',
					numAppointments: 50,
					totalRevenue: 5000,
					revenuePerAppointment: 100,
					avgAppointmentDurationMinutes: 30
				}
			];

			mockAnalyticsService.getDoctorSummary.mockResolvedValue(
				expectedResult
			);

			// Act
			const result = await controller.getDoctorSummary({
				startDate,
				endDate,
				clinicId
			});

			// Assert
			expect(service.getDoctorSummary).toHaveBeenCalledWith({
				startDate,
				endDate,
				clinicId
			});
			expect(result).toEqual(expectedResult);
			expect(result[0].doctorName).toBe('Dr. Smith');
		});

		it('should handle multiple doctors in summary', async () => {
			// Arrange
			const startDate = '2023-01-01';
			const endDate = '2023-01-31';
			const clinicId = 'clinic-123';

			const expectedResult: DoctorSummaryResponseDto[] = [
				{
					doctorName: 'Dr. Smith',
					numAppointments: 50,
					totalRevenue: 5000,
					revenuePerAppointment: 100,
					avgAppointmentDurationMinutes: 30
				},
				{
					doctorName: 'Dr. Johnson',
					numAppointments: 30,
					totalRevenue: 3000,
					revenuePerAppointment: 100,
					avgAppointmentDurationMinutes: 25
				}
			];

			mockAnalyticsService.getDoctorSummary.mockResolvedValue(
				expectedResult
			);

			// Act
			const result = await controller.getDoctorSummary({
				startDate,
				endDate,
				clinicId
			});

			// Assert
			expect(result).toHaveLength(2);
			expect(result.map(d => d.doctorName)).toContain('Dr. Smith');
			expect(result.map(d => d.doctorName)).toContain('Dr. Johnson');
		});
	});

	describe('getSummary', () => {
		it('should return comprehensive clinic summary', async () => {
			// Arrange
			const startDate = '2023-01-01';
			const endDate = '2023-01-31';
			const clinicId = 'clinic-123';

			const expectedResult: SummaryResponseDto = {
				appointmentsCompleted: 100,
				invoicesGenerated: 95,
				totalBilling: 10000,
				creditNotesGenerated: 5,
				totalCreditNotes: 500,
				amountCollected: {
					cash: 2000,
					card: 3000,
					wallet: 1000,
					cheque: 500,
					bankTransfer: 1500,
					total: 8000
				},
				amountRefunded: {
					cash: 100,
					card: 200,
					wallet: 50,
					cheque: 25,
					bankTransfer: 75,
					total: 450
				},
				badDebts: 1000
			};

			mockAnalyticsService.getSummary.mockResolvedValue(expectedResult);

			// Act
			const result = await controller.getSummary({
				startDate,
				endDate,
				clinicId
			});

			// Assert
			expect(service.getSummary).toHaveBeenCalledWith({
				startDate,
				endDate,
				clinicId
			});
			expect(result).toEqual(expectedResult);
			expect(result.amountCollected.total).toBe(8000);
			expect(result.amountRefunded.total).toBe(450);
		});
	});

	describe('downloadReport', () => {
		it('should generate and download report successfully', async () => {
			// Arrange
			const type = AnalyticsType.REVENUE;
			const startDate = '2023-01-01';
			const endDate = '2023-01-31';
			const clinicId = 'clinic-123';
			const reportType = AnalyticsReportType.BY_BILLING;

			const mockBuffer = Buffer.from('mock excel data');
			mockAnalyticsService.generateReport.mockResolvedValue(mockBuffer);

			// Act
			await controller.downloadReport(
				{
					type,
					startDate,
					endDate,
					clinicId,
					reportType
				},
				mockResponse
			);

			// Assert
			expect(service.generateReport).toHaveBeenCalledWith({
				type,
				startDate,
				endDate,
				clinicId,
				reportType
			});
			expect(mockResponse.set).toHaveBeenCalledWith({
				'Content-Type':
					'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
				'Content-Disposition': `attachment; filename="${type}-report.xlsx"`,
				'Content-Length': mockBuffer.length,
				'Cache-Control': 'no-cache'
			});
			expect(mockResponse.end).toHaveBeenCalledWith(mockBuffer);
		});

		it('should handle report generation without optional reportType', async () => {
			// Arrange
			const type = AnalyticsType.APPOINTMENTS;
			const startDate = '2023-01-01';
			const endDate = '2023-01-31';
			const clinicId = 'clinic-123';

			const mockBuffer = Buffer.from('mock excel data');
			mockAnalyticsService.generateReport.mockResolvedValue(mockBuffer);

			// Act
			await controller.downloadReport(
				{
					type,
					startDate,
					endDate,
					clinicId
				},
				mockResponse
			);

			// Assert
			expect(service.generateReport).toHaveBeenCalledWith({
				type,
				startDate,
				endDate,
				clinicId
			});
			expect(mockResponse.end).toHaveBeenCalledWith(mockBuffer);
		});

		it('should handle report generation errors', async () => {
			// Arrange
			const type = AnalyticsType.REVENUE;
			const startDate = '2023-01-01';
			const endDate = '2023-01-31';
			const clinicId = 'clinic-123';
			const error = new Error('Report generation failed');

			mockAnalyticsService.generateReport.mockRejectedValue(error);

			// Act & Assert
			await expect(
				controller.downloadReport(
					{
						type,
						startDate,
						endDate,
						clinicId
					},
					mockResponse
				)
			).rejects.toThrow('Report generation failed');

			expect(service.generateReport).toHaveBeenCalledWith({
				type,
				startDate,
				endDate,
				clinicId
			});
		});
	});
});

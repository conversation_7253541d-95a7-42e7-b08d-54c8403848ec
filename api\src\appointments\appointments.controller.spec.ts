import { Test, TestingModule } from '@nestjs/testing';
import { AppointmentsController } from './appointments.controller';
import { AppointmentsService } from './appointments.service';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import {
	HttpException,
	HttpStatus,
	NotFoundException,
	BadRequestException
} from '@nestjs/common';
import { EnumAppointmentStatus } from './enums/enum-appointment-status';
import { CreateAppointmentDto } from './dto/create/create-appointment.dto';
import { UpdateAppointmentsDto } from './dto/create/update-appointment.dto';
import { UpdateAppointmentFeildsDto } from './dto/create/update-appointmentField.dto';
import { AppointmentEntity } from './entities/appointment.entity';
import { EnumAppointmentType } from './enums/enum-appointment-type';
import { EnumAppointmentTriage } from './enums/enum-appointment-triage';
import { ClinicRoomEntity } from '../clinics/entities/clinic-room.entity';
import { ClinicEntity } from '../clinics/entities/clinic.entity';
import { Patient } from '../patients/entities/patient.entity';
import { AppointmentDetailsEntity } from './entities/appointment-details.entity';
import { AppointmentDoctorsEntity } from './entities/appointment-doctor.entity';
import { CartEntity } from '../carts/entites/cart.entity';
import { RoleService } from '../roles/role.service';

describe('AppointmentsController', () => {
	let controller: AppointmentsController;
	let appointmentsService: jest.Mocked<AppointmentsService>;
	let logger: jest.Mocked<WinstonLogger>;

	const createdDate = new Date();

	const mockPatient = new Patient();
	const mockRoom = new ClinicRoomEntity();
	const mockAppointmentDoctor = new AppointmentDoctorsEntity();
	const mockAppointmentDetailsEntity = new AppointmentDetailsEntity();
	const mockClinic = new ClinicEntity();

	const mockAppointments: AppointmentEntity = {
		id: 'uuid_appointment_1',
		clinicId: 'clinic_1',
		brandId: 'brand_1',
		patientId: 'patient_1',
		roomId: 'room_1',
		reason: 'checkup',
		type: EnumAppointmentType.GeneralCheckup,
		triage: EnumAppointmentTriage.Low,
		date: new Date(),
		startTime: new Date(),
		endTime: new Date(),
		isBlocked: false,
		status: EnumAppointmentStatus.Scheduled,
		createdAt: new Date(),
		updatedAt: new Date(),
		createdBy: 'user_1',
		updatedBy: 'user_1',
		patient: mockPatient,
		appointmentDoctors: [],
		room: {} as ClinicRoomEntity,
		appointmentDetails: {} as AppointmentDetailsEntity,
		labReports: [],
		clinic: {} as any,
		cart: new CartEntity(),
		checkinTime: new Date(),
		checkoutTime: new Date(),
		receivingCareTime: new Date(),
		mode: 'Clinic'
	};

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [AppointmentsController],
			providers: [
				{
					provide: AppointmentsService,
					useValue: {
						createAppointment: jest.fn(),
						getAllAppointments: jest.fn(() =>
							Promise.resolve(mockAppointments)
						),
						getAppointmentsForPatient: jest.fn(),
						getAppointmentDetails: jest.fn(),
						updateAppointmentDetails: jest.fn(),
						updateAppointmentStatus: jest.fn(),
						deleteAppointment: jest.fn(),
						updateAppointment: jest.fn(),
						checkPatientOnGoingAppointment: jest.fn()
					}
				},
				{
					provide: RoleService,
					useValue: {
						findByName: jest.fn(),
						findById: jest.fn()
					}
				},
				{
					provide: WinstonLogger,
					useValue: { log: jest.fn(), error: jest.fn() }
				}
			]
		}).compile();

		controller = module.get<AppointmentsController>(AppointmentsController);
		appointmentsService = module.get(AppointmentsService);
		logger = module.get(WinstonLogger);
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	describe('createAppointment', () => {
		it('should create an appointment successfully', async () => {
			const createAppointmentDto: CreateAppointmentDto = {
				date: new Date().toISOString(),
				startTime: new Date(),
				endTime: new Date(),
				patientId: 'patient_1',
				doctorIds: ['doctor_1'],
				clinicId: 'clinic_1',
				roomId: 'room_1',
				reason: 'checkup',
				type: 'General Checkup',
				triage: 'low',
				providerIds: []
			};

			const mockReq = {
				user: {
					clinicId: 'clinic_1',
					brandId: 'brand_1'
				}
			};

			appointmentsService.createAppointment.mockResolvedValue(
				mockAppointments
			);

			const result = await controller.createAppointment(
				createAppointmentDto,
				mockReq
			);

			expect(result).toEqual(mockAppointments);
			expect(appointmentsService.createAppointment).toHaveBeenCalledWith(
				createAppointmentDto,
				mockReq.user.brandId
			);
		});

		it('should throw HttpException if appointment creation fails', async () => {
			const createAppointmentDto: CreateAppointmentDto = {
				date: new Date().toISOString(),
				startTime: new Date(),
				endTime: new Date(),
				patientId: 'patient_1',
				doctorIds: ['doctor_1'],
				clinicId: 'clinic_1',
				roomId: 'room_1',
				reason: 'checkup',
				type: 'General Checkup',
				triage: 'low',
				providerIds: []
			};

			const mockReq = {
				user: {
					clinicId: 'clinic_1',
					brandId: 'brand_1'
				}
			};

			appointmentsService.createAppointment.mockRejectedValue(
				new Error('Creation failed')
			);

			await expect(
				controller.createAppointment(createAppointmentDto, mockReq)
			).rejects.toThrow(HttpException);
		});
	});

	describe('getAllAppointments', () => {
		it('should return all appointments', async () => {
			const query = {
				page: 1,
				limit: 10,
				orderBy: 'DESC',
				date: '',
				search: '',
				doctors: '',
				status: '',
				onlyPrimary: 'false'
			};
			const result = await controller.getAllAppointments(query, {
				user: { clinicId: 'clinic1' }
			});
			expect(result).toEqual(mockAppointments);
			expect(appointmentsService.getAllAppointments).toHaveBeenCalledWith(
				1,
				10,
				'DESC',
				'',
				'',
				[],
				[],
				false,
				'clinic1'
			);
		});

		it('should throw HttpException if fetching appointments fails', async () => {
			appointmentsService.getAllAppointments.mockRejectedValue(
				new Error('Fetch failed')
			);
			const query = {
				page: 1,
				limit: 10,
				orderBy: 'DESC',
				date: '',
				search: '',
				doctors: '',
				status: '',
				onlyPrimary: 'false'
			};
			await expect(
				controller.getAllAppointments(query, { user: { clinicId: 'clinic1' } })
			).rejects.toThrow(HttpException);
		});
	});

	describe('getAppointmentsForPatient', () => {
		it('should return appointments for a patient', async () => {
			appointmentsService.getAppointmentsForPatient.mockResolvedValue([
				mockAppointments
			]);

			const result =
				await controller.getAppointmentsForPatient('patient1');

			expect(result).toEqual([
				expect.objectContaining({
					id: 'uuid_appointment_1',
					status: EnumAppointmentStatus.Scheduled,
					priority: EnumAppointmentTriage.Low,
					visitType: EnumAppointmentType.GeneralCheckup,
					reason: 'checkup',
					patientId: 'patient_1'
				})
			]);
		});

		it('should throw NotFoundException if no appointments are found', async () => {
			appointmentsService.getAppointmentsForPatient.mockRejectedValue(
				new NotFoundException()
			);

			await expect(
				controller.getAppointmentsForPatient('patient1')
			).rejects.toThrow(HttpException);
		});
	});

	describe('getAppointmentDetails', () => {
		it('should return appointment details', async () => {
			const mockAppointmentDetails = {
				id: 'aa3c24c7-ffe3-4ba5-846a-8b15cc7de382',
				clinicId: '8e16b9e0-e40c-4bb5-945e-2640ca042ec3',
				patientId: 'adf3a75e-0db6-48bc-b865-4e50597b6009',
				roomId: '79078ffb-9cad-4fdd-9c44-6842dd0a7774',
				reason: 'Annual Check-Up',
				type: EnumAppointmentType.Consultation,
				triage: null,
				date: new Date('2024-09-17T18:30:00.000Z'),
				startTime: new Date('2024-09-18T12:00:00.000Z'),
				endTime: new Date('2024-09-18T12:30:00.000Z'),
				deletedAt: null,
				weight: 1,
				notes: null,
				preVisitQuestions: null,
				isBlocked: false,
				status: EnumAppointmentStatus.Scheduled,
				createdAt: new Date('2024-09-18T11:53:24.644Z'),
				updatedAt: new Date('2024-09-18T11:53:24.644Z'),
				createdBy: null,
				updatedBy: null,
				initialNotes: null,
				appointmentDoctors: [
					{
						id: 'cb436428-2979-4467-83d8-7a30ff579932',
						appointmentId: 'aa3c24c7-ffe3-4ba5-846a-8b15cc7de382',
						doctorId: '9410318b-f77c-4eb7-ae3f-d282b72c1260',
						primary: false,
						createdBy: null,
						updatedBy: null,
						createdAt: new Date('2024-09-18T11:53:24.666Z'),
						updatedAt: new Date('2024-09-18T11:53:24.666Z'),
						doctor: {
							id: '9410318b-f77c-4eb7-ae3f-d282b72c1260',
							firstName: 'Rohit-Doctor-1',
							lastName: 'Hitman',
							email: '<EMAIL>'
						}
					},
					{
						id: 'a4d241e3-df85-4134-a0d8-7a1cc28ce90c',
						appointmentId: 'aa3c24c7-ffe3-4ba5-846a-8b15cc7de382',
						doctorId: '1f3c27c9-0905-4503-8450-cb52df383480',
						primary: true,
						createdBy: null,
						updatedBy: null,
						createdAt: new Date('2024-09-18T11:53:24.666Z'),
						updatedAt: new Date('2024-09-18T11:53:24.666Z'),
						doctor: {
							id: '1f3c27c9-0905-4503-8450-cb52df383480',
							firstName: 'test123-18th',
							lastName: 'Decock',
							email: '<EMAIL>'
						}
					}
				],
				room: null,
				appointmentDetails: {
					id: 'de0317fe-41c7-4a90-ac86-cfec4347be02',
					appointmentId: 'aa3c24c7-ffe3-4ba5-846a-8b15cc7de382',
					details: null,
					createdAt: new Date('2024-09-18T11:53:24.659Z'),
					updatedAt: new Date('2024-09-18T11:53:24.659Z'),
					createdBy: null,
					updatedBy: null
				},
				patient: {} as Patient,
				clinic: {} as ClinicEntity,
				labReports: [],
				cart: {} as CartEntity
			};

			appointmentsService.getAppointmentDetails.mockResolvedValue(
				mockAppointmentDetails as any
			);

			const result = await controller.getAppointmentDetails(
				'aa3c24c7-ffe3-4ba5-846a-8b15cc7de382'
			);

			expect(result).toEqual(mockAppointmentDetails);
		});

		it('should throw HttpException if fetching details fails', async () => {
			appointmentsService.getAppointmentDetails.mockRejectedValue(
				new NotFoundException('Appointment details not found')
			);

			await expect(
				controller.getAppointmentDetails('non-existent-id')
			).rejects.toThrow(HttpException);
		});
	});

	describe('updateAppointmentDetails', () => {
		it('should update appointment details successfully', async () => {
			const updateDto: UpdateAppointmentFeildsDto = {
				patientId: 'patient_1',
				weight: 15,
				doctorIds: ['doc1', 'doc2'],
				providerIds: []
			};
			const mockReq = {
				user: {
					id: 'user_1',
					clinicId: 'clinic_1',
					brandId: 'brand_1'
				}
			};
			appointmentsService.updateAppointmentDetails.mockResolvedValue(
				mockAppointments as any
			);

			const result = await controller.updateAppointmentDetails(
				'appointment1',
				updateDto,
				mockReq
			);

			expect(result).toEqual(mockAppointments);
		});

		it('should throw HttpException if update fails', async () => {
			const updateDto: UpdateAppointmentFeildsDto = {
				patientId: 'patient_1',
				weight: 15,
				doctorIds: ['doc1', 'doc2'],
				providerIds: []
			};
			const mockReq = {
				user: {
					id: 'user_1',
					clinicId: 'clinic_1',
					brandId: 'brand_1'
				}
			};
			appointmentsService.updateAppointmentDetails.mockRejectedValue(
				new Error('Update failed')
			);

			await expect(
				controller.updateAppointmentDetails(
					'appointment1',
					updateDto,
					mockReq
				)
			).rejects.toThrow(HttpException);
		});
	});

	describe('updateAppointmentStatus', () => {
		it('should update appointment status successfully', async () => {
			const updateDto: UpdateAppointmentsDto = {
				status: EnumAppointmentStatus.Checkedin
			};
			appointmentsService.updateAppointmentStatus.mockResolvedValue(
				mockAppointments
			);

			const result = await controller.updateAppointmentStatus(
				'appointment1',
				updateDto
			);

			expect(result).toEqual({
				message: expect.any(String),
				appointment: mockAppointments
			});
		});

		it('should throw NotFoundException if appointment is not found', async () => {
			const updateDto: UpdateAppointmentsDto = {
				status: EnumAppointmentStatus.Checkedin
			};
			appointmentsService.updateAppointmentStatus.mockRejectedValue(
				new NotFoundException()
			);

			await expect(
				controller.updateAppointmentStatus('appointment1', updateDto)
			).rejects.toThrow(NotFoundException);
		});
	});

	describe('deleteAppointment', () => {
		it('should delete an appointment successfully', async () => {
			appointmentsService.deleteAppointment.mockResolvedValue({
				status: true
			});

			const result = await controller.deleteAppointment('appointment1');

			expect(result).toEqual({ status: true });
		});

		it('should throw HttpException if deletion fails', async () => {
			appointmentsService.deleteAppointment.mockRejectedValue(
				new Error('Deletion failed')
			);

			await expect(
				controller.deleteAppointment('appointment1')
			).rejects.toThrow(HttpException);
		});
	});

	describe('updateAppointmentFields', () => {
		it('should update appointment fields successfully', async () => {
			const updateDto: UpdateAppointmentFeildsDto = {
				patientId: 'patient_1',
				weight: 15,
				doctorIds: ['doc1', 'doc2'],
				providerIds: []
			};
			appointmentsService.updateAppointment.mockResolvedValue(
				mockAppointments
			);

			const result = await controller.updateAppointmentFields(
				'appointment1',
				updateDto
			);

			expect(result).toEqual({
				message: expect.any(String),
				appointment: mockAppointments
			});
		});

		it('should throw BadRequestException if update fails', async () => {
			const updateDto: UpdateAppointmentFeildsDto = {
				patientId: 'patient_1',
				weight: 15,
				doctorIds: ['doc1', 'doc2'],
				providerIds: []
			};
			appointmentsService.updateAppointment.mockRejectedValue(
				new Error('Update failed')
			);

			await expect(
				controller.updateAppointmentFields('appointment1', updateDto)
			).rejects.toThrow(BadRequestException);
		});
	});

	describe('checkOngoingAppointment', () => {
		it('should return ongoing appointment if present', async () => {
			appointmentsService.checkPatientOnGoingAppointment.mockResolvedValue(
				mockAppointments as any
			);

			const result = await controller.checkOngoingAppointment('patient1');

			expect(result).toEqual(mockAppointments);
		});

		it('should throw HttpException if check fails', async () => {
			appointmentsService.checkPatientOnGoingAppointment.mockRejectedValue(
				new Error('Check failed')
			);

			await expect(
				controller.checkOngoingAppointment('patient1')
			).rejects.toThrow(HttpException);
		});
	});

	describe('getAllAppointments - Error Handling', () => {
		it('should handle invalid JSON in doctors parameter', async () => {
			const mockReq = { user: { clinicId: 'clinic_1' } };
			appointmentsService.getAllAppointments.mockResolvedValue({
				appointments: [mockAppointments as any],
				total: 1
			});
			const query = {
				page: 1,
				limit: 10,
				orderBy: 'DESC',
				date: '',
				search: '',
				doctors: 'invalid-json',
				status: '[]',
				onlyPrimary: 'false'
			};
			const result = await controller.getAllAppointments(query, mockReq);

			expect(result).toBeDefined();
			expect(appointmentsService.getAllAppointments).toHaveBeenCalledWith(
				1,
				10,
				'DESC',
				'',
				'',
				[],
				[],
				false,
				'clinic_1'
			);
		});

		it('should handle invalid JSON in status parameter', async () => {
			const mockReq = { user: { clinicId: 'clinic_1' } };
			appointmentsService.getAllAppointments.mockResolvedValue({
				appointments: [mockAppointments as any],
				total: 1
			});
			const query = {
				page: 1,
				limit: 10,
				orderBy: 'DESC',
				date: '',
				search: '',
				doctors: '[]',
				status: 'invalid-json',
				onlyPrimary: 'false'
			};
			const result = await controller.getAllAppointments(query, mockReq);

			expect(result).toBeDefined();
			expect(appointmentsService.getAllAppointments).toHaveBeenCalledWith(
				1,
				10,
				'DESC',
				'',
				'',
				[],
				[],
				false,
				'clinic_1'
			);
		});

		it('should handle onlyPrimary parameter when value is false', async () => {
			const mockReq = { user: { clinicId: 'clinic_1' } };
			appointmentsService.getAllAppointments.mockResolvedValue({
				appointments: [mockAppointments as any],
				total: 1
			});
			const query = {
				page: 1,
				limit: 10,
				orderBy: 'DESC',
				date: '',
				search: '',
				doctors: '[]',
				status: '[]',
				onlyPrimary: 'false'
			};
			const result = await controller.getAllAppointments(query, mockReq);

			expect(result).toBeDefined();
			expect(appointmentsService.getAllAppointments).toHaveBeenCalledWith(
				1,
				10,
				'DESC',
				'',
				'',
				[],
				[],
				false,
				'clinic_1'
			);
		});
	});

	describe('getAppointmentsForPatient - Error Handling', () => {
		it('should return 404 when service throws NotFoundException', async () => {
			appointmentsService.getAppointmentsForPatient.mockRejectedValue(
				new NotFoundException('Patient not found')
			);

			await expect(
				controller.getAppointmentsForPatient('non-existent-patient')
			).rejects.toThrow(HttpException);
		});

		it('should handle other errors gracefully', async () => {
			appointmentsService.getAppointmentsForPatient.mockRejectedValue(
				new Error('Database connection failed')
			);

			await expect(
				controller.getAppointmentsForPatient('patient_1')
			).rejects.toThrow(Error);
		});
	});

	describe('deleteAppointment - Error Handling', () => {
		it('should return 404 when appointment ID does not exist', async () => {
			appointmentsService.deleteAppointment.mockRejectedValue(
				new NotFoundException('Appointment not found')
			);

			await expect(
				controller.deleteAppointment('non-existent-id')
			).rejects.toThrow(HttpException);
		});
	});

	describe('updateAppointmentStatus - Error Handling', () => {
		it('should return 400 for invalid status transition', async () => {
			const updateDto = { status: 'InvalidStatus' as any };
			appointmentsService.updateAppointmentStatus.mockRejectedValue(
				new BadRequestException('Invalid status transition')
			);

			await expect(
				controller.updateAppointmentStatus('appointment_1', updateDto)
			).rejects.toThrow(BadRequestException);
		});
	});
});

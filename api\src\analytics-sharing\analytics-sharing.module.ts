import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnalyticsSharingController } from './analytics-sharing.controller';
import { AnalyticsSharingService } from './analytics-sharing.service';
import { AnalyticsDocumentGenerationService } from './services/analytics-document-generation.service';
import { AnalyticsDocumentRequest } from './entities/analytics-document-request.entity';
import { UtilsModule } from '../utils/utils.module';
import { AnalyticsModule } from '../analytics/analytics.module';
import { InvoiceModule } from '../invoice/invoice.module';
import { ReceiptModule } from '../receipt/receipt.module';
import { CreditNoteModule } from '../credit-note/credit-note.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([AnalyticsDocumentRequest]),
    UtilsModule,
    forwardRef(() => AnalyticsModule),
    forwardRef(() => InvoiceModule),
    forwardRef(() => ReceiptModule),
    forwardRef(() => CreditNoteModule),
  ],
  controllers: [AnalyticsSharingController],
  providers: [AnalyticsSharingService, AnalyticsDocumentGenerationService],
  exports: [AnalyticsSharingService, AnalyticsDocumentGenerationService],
})
export class AnalyticsSharingModule {}

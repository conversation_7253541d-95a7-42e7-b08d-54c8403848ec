import { Test, TestingModule } from '@nestjs/testing';
import { Reflector } from '@nestjs/core';
import { AnalyticsController } from './analytics.controller';
import { AnalyticsService } from './analytics.service';
import { RoleService } from '../roles/role.service';

describe('AnalyticsModule', () => {
	let module: TestingModule;

	beforeEach(async () => {
		// Mock the service dependencies
		const mockAnalyticsService = {
			getRevenueChartData: jest.fn(),
			getCollectedPaymentsChartData: jest.fn(),
			getAppointmentsChartData: jest.fn(),
			getDoctorSummary: jest.fn(),
			getSummary: jest.fn(),
			generateReport: jest.fn()
		};

		const mockRoleService = {
			findById: jest.fn(),
			findOneById: jest.fn(),
			findByName: jest.fn(),
			findAll: jest.fn()
		};

		const mockReflector = {
			get: jest.fn(),
			getAll: jest.fn(),
			getAllAndOverride: jest.fn(),
			getAllAndMerge: jest.fn()
		};

		module = await Test.createTestingModule({
			controllers: [AnalyticsController],
			providers: [
				{
					provide: AnalyticsService,
					useValue: mockAnalyticsService
				},
				{
					provide: RoleService,
					useValue: mockRoleService
				},
				{
					provide: Reflector,
					useValue: mockReflector
				}
			]
		}).compile();
	});

	afterEach(async () => {
		if (module) {
			await module.close();
		}
	});

	it('should be defined', () => {
		expect(module).toBeDefined();
	});

	it('should have AnalyticsController', () => {
		const controller = module.get<AnalyticsController>(AnalyticsController);
		expect(controller).toBeDefined();
		expect(controller).toBeInstanceOf(AnalyticsController);
	});

	it('should have AnalyticsService', () => {
		const service = module.get<AnalyticsService>(AnalyticsService);
		expect(service).toBeDefined();
	});

	it('should export AnalyticsService', () => {
		const service = module.get<AnalyticsService>(AnalyticsService);
		expect(service).toBeDefined();
	});

	describe('Dependency Injection', () => {
		it('should inject dependencies correctly', () => {
			const controller =
				module.get<AnalyticsController>(AnalyticsController);
			const service = module.get<AnalyticsService>(AnalyticsService);

			expect(controller).toBeDefined();
			expect(service).toBeDefined();
		});

		it('should be able to resolve all dependencies', async () => {
			expect(() => {
				module.get<AnalyticsController>(AnalyticsController);
				module.get<AnalyticsService>(AnalyticsService);
			}).not.toThrow();
		});
	});

	describe('Module Compilation', () => {
		it('should compile without errors', async () => {
			expect(module).toBeDefined();
			expect(module.get).toBeDefined();
		});

		it('should initialize all providers', () => {
			const service = module.get<AnalyticsService>(AnalyticsService);
			expect(service).toBeDefined();
		});

		it('should initialize all controllers', () => {
			const controller =
				module.get<AnalyticsController>(AnalyticsController);
			expect(controller).toBeDefined();
		});
	});
});

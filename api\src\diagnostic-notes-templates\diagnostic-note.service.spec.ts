import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { DiagnosticTemplatesService } from './diagnostic-note.service';
import {
	DiagnosticTemplate,
	TemplateType
} from './entities/diagnostic-template.entity';
import { DiagnosticNote } from './entities/diagnostic-note.entity';
import { CreateDiagnosticTemplateDto } from './dto/create-template.dto';
import {
	CreateDiagnosticNoteDto,
	UpdateDiagnosticNoteDto
} from './dto/diagnostic-note.dto';
import {
	NotFoundException,
	ConflictException,
	InternalServerErrorException
} from '@nestjs/common';
import { LabReport } from '../clinic-lab-report/entities/lab-report.entity';
import { ClinicLabReport } from '../clinic-lab-report/entities/clinic-lab-report.entity';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { S3Service } from '../utils/aws/s3/s3.service';
import { PatientsService } from '../patients/patients.service';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { AppointmentDetailsEntity } from '../appointments/entities/appointment-details.entity';
import { AppointmentGateway } from '../socket/socket.appointment.gateway';

// Mock external dependencies
jest.mock('../utils/common/generate_alpha-numeric_code', () => ({
	generateUniqueCode: jest.fn().mockResolvedValue('DN001')
}));

jest.mock('../utils/generatePdf', () => ({
	generatePDF: jest.fn().mockResolvedValue(Buffer.from('pdf-content'))
}));

jest.mock('../utils/pdfs/diagnosticNote', () => ({
	generateDiagnosticReportNote: jest.fn().mockReturnValue('<html>note</html>')
}));

jest.mock('../utils/pdfs/diagnosticTable', () => ({
	generateDiagnosticReportTable: jest
		.fn()
		.mockReturnValue('<html>table</html>')
}));

jest.mock('uuidv7', () => ({
	uuidv4: jest.fn().mockReturnValue('uuid-123')
}));

describe('DiagnosticTemplatesService', () => {
	let service: DiagnosticTemplatesService;
	let templateRepository: Repository<DiagnosticTemplate>;
	let diagnosticNoteRepository: Repository<DiagnosticNote>;
	let labReportRepository: Repository<LabReport>;
	let clinicLabReportRepository: Repository<ClinicLabReport>;
	let appointmentRepository: Repository<AppointmentEntity>;
	let appointmentDetailsRepository: Repository<AppointmentDetailsEntity>;
	let logger: WinstonLogger;
	let s3Service: S3Service;
	let patientService: PatientsService;
	let appointmentGateway: AppointmentGateway;
	const mockQueryRunner = {
		manager: {
			remove: jest.fn().mockResolvedValue(undefined),
			save: jest.fn().mockResolvedValue(undefined)
		},
		connect: jest.fn(),
		startTransaction: jest.fn(),
		commitTransaction: jest.fn(),
		rollbackTransaction: jest.fn(),
		release: jest.fn()
	};

	const mockDataSource = {
		createQueryRunner: jest.fn(() => mockQueryRunner)
	};

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				DiagnosticTemplatesService,
				{
					provide: getRepositoryToken(DiagnosticTemplate),
					useValue: {
						findOne: jest.fn(),
						create: jest.fn(),
						save: jest.fn(),
						find: jest.fn(),
						remove: jest.fn(),
						createQueryBuilder: jest.fn()
					}
				},
				{
					provide: getRepositoryToken(DiagnosticNote),
					useValue: {
						findOne: jest.fn(),
						create: jest.fn(),
						save: jest.fn(),
						find: jest.fn(),
						remove: jest.fn()
					}
				},
				{
					provide: getRepositoryToken(LabReport),
					useValue: {
						findOne: jest.fn(),
						create: jest.fn(),
						save: jest.fn(),
						find: jest.fn(),
						remove: jest.fn()
					}
				},
				{
					provide: getRepositoryToken(ClinicLabReport),
					useValue: {
						findOne: jest.fn(),
						create: jest.fn(),
						save: jest.fn(),
						find: jest.fn(),
						remove: jest.fn()
					}
				},
				{
					provide: getRepositoryToken(AppointmentEntity),
					useValue: {
						findOne: jest.fn(),
						create: jest.fn(),
						save: jest.fn(),
						find: jest.fn(),
						remove: jest.fn()
					}
				},
				{
					provide: getRepositoryToken(AppointmentDetailsEntity),
					useValue: {
						findOne: jest.fn(),
						create: jest.fn(),
						save: jest.fn(),
						find: jest.fn(),
						remove: jest.fn()
					}
				},
				{
					provide: WinstonLogger,
					useValue: {
						log: jest.fn(),
						error: jest.fn(),
						warn: jest.fn()
					}
				},
				{
					provide: S3Service,
					useValue: {
						deleteFile: jest.fn(),
						uploadPdfToS3: jest.fn().mockResolvedValue('s3-key')
					}
				},
				{
					provide: PatientsService,
					useValue: {
						getPatientDetails: jest.fn()
					}
				},
				{
					provide: DataSource,
					useValue: mockDataSource
				},
				{
					provide: AppointmentGateway,
					useValue: {
						publishAppointmentUpdate: jest.fn()
					}
				}
			]
		}).compile();

		service = module.get<DiagnosticTemplatesService>(
			DiagnosticTemplatesService
		);
		templateRepository = module.get<Repository<DiagnosticTemplate>>(
			getRepositoryToken(DiagnosticTemplate)
		);
		diagnosticNoteRepository = module.get<Repository<DiagnosticNote>>(
			getRepositoryToken(DiagnosticNote)
		);
		labReportRepository = module.get<Repository<LabReport>>(
			getRepositoryToken(LabReport)
		);
		clinicLabReportRepository = module.get<Repository<ClinicLabReport>>(
			getRepositoryToken(ClinicLabReport)
		);
		appointmentRepository = module.get<Repository<AppointmentEntity>>(
			getRepositoryToken(AppointmentEntity)
		);
		appointmentDetailsRepository = module.get<
			Repository<AppointmentDetailsEntity>
		>(getRepositoryToken(AppointmentDetailsEntity));
		logger = module.get<WinstonLogger>(WinstonLogger);
		s3Service = module.get<S3Service>(S3Service);
		patientService = module.get<PatientsService>(PatientsService);
		appointmentGateway =
			module.get<AppointmentGateway>(AppointmentGateway);
	});

	it('should be defined', () => {
		expect(service).toBeDefined();
	});

	describe('create', () => {
		it('should create a new diagnostic template', async () => {
			const createDto: CreateDiagnosticTemplateDto = {
				templateName: 'Template 1',
				clinicId: 'clinic-id',
				assignedDiagnostics: [],
				templateType: TemplateType.NOTES
			};
			const result = { id: '1', ...createDto };
			jest.spyOn(templateRepository, 'findOne').mockResolvedValue(null);
			jest.spyOn(templateRepository, 'create').mockReturnValue(
				result as any
			);
			jest.spyOn(templateRepository, 'save').mockResolvedValue(
				result as any
			);

			expect(await service.create(createDto, 'user-id')).toBe(result);
			expect(templateRepository.findOne).toHaveBeenCalledWith({
				where: {
					clinicId: createDto.clinicId,
					templateName: createDto.templateName
				}
			});
			expect(templateRepository.create).toHaveBeenCalledWith({
				...createDto,
				createdBy: 'user-id',
				updatedBy: 'user-id'
			});
			expect(templateRepository.save).toHaveBeenCalledWith(result);
		});

		it('should throw ConflictException if template with same name exists', async () => {
			const createDto: CreateDiagnosticTemplateDto = {
				templateName: 'Template 1',
				clinicId: 'clinic-id',
				assignedDiagnostics: [],
				templateType: TemplateType.NOTES
			};
			jest.spyOn(templateRepository, 'findOne').mockResolvedValue(
				{} as any
			);

			await expect(service.create(createDto, 'user-id')).rejects.toThrow(
				ConflictException
			);
		});
	});

	describe('findAll', () => {
		it('should return all templates for a clinic', async () => {
			const clinicId = 'clinic-id';
			const result = [{ id: '1', templateName: 'Template 1' }];
			jest.spyOn(templateRepository, 'find').mockResolvedValue(
				result as any
			);

			expect(await service.findAll(clinicId)).toBe(result);
			expect(templateRepository.find).toHaveBeenCalledWith({
				where: { clinicId },
				order: { createdAt: 'DESC' }
			});
		});
	});

	describe('findOne', () => {
		it('should return a template by id', async () => {
			const id = '1';
			const clinicId = 'clinic-id';
			const result = { id, templateName: 'Template 1' };
			jest.spyOn(templateRepository, 'findOne').mockResolvedValue(
				result as any
			);

			expect(await service.findOne(id, clinicId)).toBe(result);
			expect(templateRepository.findOne).toHaveBeenCalledWith({
				where: { id, clinicId }
			});
		});

		it('should throw NotFoundException if template not found', async () => {
			const id = '1';
			const clinicId = 'clinic-id';
			jest.spyOn(templateRepository, 'findOne').mockResolvedValue(null);

			await expect(service.findOne(id, clinicId)).rejects.toThrow(
				NotFoundException
			);
		});
	});

	describe('update', () => {
		it('should update a template', async () => {
			const id = '1';
			const updateDto: Partial<CreateDiagnosticTemplateDto> = {
				templateName: 'Updated Template'
			};
			const clinicId = 'clinic-id';
			const result = { id, ...updateDto };
			jest.spyOn(service, 'findOne').mockResolvedValue(result as any);
			jest.spyOn(templateRepository, 'findOne').mockResolvedValue(null);
			jest.spyOn(templateRepository, 'save').mockResolvedValue(
				result as any
			);

			expect(
				await service.update(id, updateDto, 'user-id', clinicId)
			).toBe(result);
			expect(service.findOne).toHaveBeenCalledWith(id, clinicId);
			expect(templateRepository.save).toHaveBeenCalledWith(result);
		});

		it('should throw ConflictException if template with same name exists', async () => {
			const id = '1';
			const updateDto: Partial<CreateDiagnosticTemplateDto> = {
				templateName: 'Updated Template'
			};
			const clinicId = 'clinic-id';
			const existingTemplate = {
				id: '2',
				templateName: 'Updated Template'
			};
			jest.spyOn(service, 'findOne').mockResolvedValue({
				id,
				templateName: 'Template 1'
			} as any);
			jest.spyOn(templateRepository, 'findOne').mockResolvedValue(
				existingTemplate as any
			);

			await expect(
				service.update(id, updateDto, 'user-id', clinicId)
			).rejects.toThrow(ConflictException);
		});
	});

	describe('remove', () => {
		it('should delete a template and its associated notes', async () => {
			const id = 'note-1';
			const clinicId = 'clinic-id';
			const template = { id, clinicId };
			const associatedNotes = [{ id: 'note-1' }];
			jest.spyOn(templateRepository, 'findOne').mockResolvedValue(
				template as any
			);
			jest.spyOn(diagnosticNoteRepository, 'find').mockResolvedValue(
				associatedNotes as any
			);

			await service.remove(id, clinicId);
			expect(templateRepository.findOne).toHaveBeenCalledWith({
				where: { id, clinicId }
			});
			expect(diagnosticNoteRepository.find).toHaveBeenCalledWith({
				where: { templateId: id }
			});
			// The service uses queryRunner.manager.remove, not repository.remove
			expect(mockQueryRunner.manager.remove).toHaveBeenCalledWith(
				DiagnosticNote,
				associatedNotes
			);
			expect(mockQueryRunner.manager.remove).toHaveBeenCalledWith(
				DiagnosticTemplate,
				template
			);
		});

		it('should throw NotFoundException if template not found', async () => {
			const id = '1';
			const clinicId = 'clinic-id';
			jest.spyOn(templateRepository, 'findOne').mockResolvedValue(null);

			await expect(service.remove(id, clinicId)).rejects.toThrow(
				NotFoundException
			);
		});
	});

	describe('createNote', () => {
		it('should create a new diagnostic note', async () => {
			const createNoteDto: CreateDiagnosticNoteDto = {
				labReportId: 'lab-report-id',
				clinicId: 'clinic-id',
				templateId: 'template-id',
				templateName: 'Template 1',
				noteData: { notes: 'Some notes' }
			};
			const labReport = {
				id: 'lab-report-id',
				patientId: 'patient-id',
				clinicId: 'clinic-id',
				appointmentId: 'appointment-id',
				createdAt: new Date(),
				clinicLabReport: { name: 'Test Lab Report' }
			};
			const result = { id: '1', ...createNoteDto };

			// Mock the lab report repository
			jest.spyOn(labReportRepository, 'findOne').mockResolvedValue(
				labReport as any
			);

			// Mock the service methods that will be called
			jest.spyOn(service as any, 'getCompleteNoteData').mockResolvedValue(
				{}
			);
			jest.spyOn(
				service as any,
				'generateAndUploadPDF'
			).mockResolvedValue('file-key');
			jest.spyOn(
				service as any,
				'updateAppointmentDetailsAndBroadcastDiagnosticNotes'
			).mockResolvedValue(undefined);

			// Mock queryRunner.manager.save
			mockQueryRunner.manager.save = jest.fn().mockResolvedValue(result);

			const actualResult = await service.createNote(
				createNoteDto,
				'user-id'
			);

			expect(labReportRepository.findOne).toHaveBeenCalledWith({
				where: {
					id: createNoteDto.labReportId,
					clinicId: createNoteDto.clinicId
				},
				relations: ['clinicLabReport']
			});
			expect(actualResult).toEqual(result);
		});
	});

	describe('updateNote', () => {
		it('should update a diagnostic note', async () => {
			const id = '1';
			const updateNoteDto: UpdateDiagnosticNoteDto = {
				noteData: { notes: 'Updated notes' }
			};
			const note = {
				id,
				templateName: 'Test Template',
				noteData: { notes: 'Old notes' },
				patientId: 'patient-id',
				diagnosticNumber: 'DN001',
				labReport: {
					clinicLabReport: { name: 'Test Lab' },
					createdAt: new Date()
				}
			};
			const result = { id, ...updateNoteDto };

			// Mock the findNoteForUpdate method
			jest.spyOn(service as any, 'findNoteForUpdate').mockResolvedValue(
				note
			);
			jest.spyOn(
				service as any,
				'validateAndUpdateTemplate'
			).mockResolvedValue(undefined);
			jest.spyOn(
				service as any,
				'updateNoteDataAndPdf'
			).mockResolvedValue(undefined);
			jest.spyOn(service as any, 'updateNoteMetadata').mockReturnValue(
				undefined
			);
			jest.spyOn(
				service as any,
				'handlePostUpdateActions'
			).mockResolvedValue(undefined);

			// Mock queryRunner.manager.save
			mockQueryRunner.manager.save = jest.fn().mockResolvedValue(result);

			const actualResult = await service.updateNote(
				id,
				updateNoteDto,
				'user-id'
			);

			expect(service['findNoteForUpdate']).toHaveBeenCalledWith(id);
			expect(actualResult).toEqual(result);
		});

		it('should throw NotFoundException if note not found', async () => {
			const id = '1';
			const updateNoteDto: UpdateDiagnosticNoteDto = {
				noteData: { notes: 'Updated notes' }
			};
			jest.spyOn(diagnosticNoteRepository, 'findOne').mockResolvedValue(
				null
			);

			await expect(
				service.updateNote(id, updateNoteDto, 'user-id')
			).rejects.toThrow(NotFoundException);
		});
	});

	describe('deleteNote', () => {
		it('should delete a diagnostic note', async () => {
			const id = '1';
			const note = { id };
			jest.spyOn(diagnosticNoteRepository, 'findOne').mockResolvedValue(
				note as any
			);
			jest.spyOn(diagnosticNoteRepository, 'remove').mockResolvedValue(
				undefined as any
			);

			await service.deleteNote(id);
			expect(diagnosticNoteRepository.findOne).toHaveBeenCalledWith({
				where: { id }
			});
			expect(diagnosticNoteRepository.remove).toHaveBeenCalledWith(note);
		});

		it('should throw NotFoundException if note not found', async () => {
			const id = '1';
			jest.spyOn(diagnosticNoteRepository, 'findOne').mockResolvedValue(
				null
			);

			await expect(service.deleteNote(id)).rejects.toThrow(
				NotFoundException
			);
		});

		it('should update appointment details when appointmentId and labReportId exist', async () => {
			const note = {
				id: 'note-id',
				appointmentId: 'appointment-id',
				labReportId: 'lab-report-id'
			};

			jest.spyOn(diagnosticNoteRepository, 'findOne').mockResolvedValue(
				note as any
			);
			jest.spyOn(diagnosticNoteRepository, 'remove').mockResolvedValue(
				undefined as any
			);

			const updateSpy = jest
				.spyOn(
					service as any,
					'updateAppointmentDetailsAndBroadcastDiagnosticNotes'
				)
				.mockResolvedValue(undefined);

			const result = await service.deleteNote('note-id');

			expect(updateSpy).toHaveBeenCalledWith(
				'appointment-id',
				'lab-report-id'
			);
			expect(result).toEqual({ success: true });
		});
	});

	// Additional test coverage for missing methods
	describe('findAll - Error Handling', () => {
		it('should handle errors when fetching templates', async () => {
			const clinicId = 'clinic-id';
			const error = new Error('Database error');
			jest.spyOn(templateRepository, 'find').mockRejectedValue(error);

			await expect(service.findAll(clinicId)).rejects.toThrow(error);
			expect(logger.error).toHaveBeenCalledWith(
				'Error fetching templates',
				{ error, clinicId }
			);
		});
	});

	describe('createNote - Error Handling', () => {
		it('should handle errors during note creation and rollback transaction', async () => {
			const createNoteDto = {
				labReportId: 'lab-report-id',
				clinicId: 'clinic-id',
				templateId: 'template-id',
				templateName: 'Template 1',
				noteData: { notes: 'Some notes' }
			};

			const error = new Error('Creation failed');
			jest.spyOn(labReportRepository, 'findOne').mockRejectedValue(error);

			await expect(
				service.createNote(createNoteDto, 'user-id')
			).rejects.toThrow(error);

			expect(mockQueryRunner.rollbackTransaction).toHaveBeenCalled();
			expect(mockQueryRunner.release).toHaveBeenCalled();
			expect(logger.error).toHaveBeenCalledWith(
				'Error creating diagnostic note',
				{ error }
			);
		});

		it('should throw NotFoundException when lab report not found', async () => {
			const createNoteDto = {
				labReportId: 'lab-report-id',
				clinicId: 'clinic-id',
				templateId: 'template-id',
				templateName: 'Template 1',
				noteData: { notes: 'Some notes' }
			};

			jest.spyOn(labReportRepository, 'findOne').mockResolvedValue(null);

			await expect(
				service.createNote(createNoteDto, 'user-id')
			).rejects.toThrow(NotFoundException);
		});
	});

	describe('getTemplatesForLabReport', () => {
		it('should return templates for a lab report', async () => {
			const labReportId = 'lab-report-id';
			const clinicId = 'clinic-id';
			const labReport = { id: labReportId, clinicId };
			const templates = [
				{ id: '1', templateName: 'Template 1' },
				{ id: '2', templateName: 'Template 2' }
			];

			jest.spyOn(clinicLabReportRepository, 'findOne').mockResolvedValue(
				labReport as any
			);
			jest.spyOn(templateRepository, 'find').mockResolvedValue(
				templates as any
			);

			const result = await service.getTemplatesForLabReport(
				labReportId,
				clinicId
			);

			expect(clinicLabReportRepository.findOne).toHaveBeenCalledWith({
				where: { id: labReportId, clinicId }
			});
			expect(result).toEqual(templates);
		});

		it('should throw NotFoundException if lab report not found', async () => {
			const labReportId = 'lab-report-id';
			const clinicId = 'clinic-id';

			jest.spyOn(clinicLabReportRepository, 'findOne').mockResolvedValue(
				null
			);

			await expect(
				service.getTemplatesForLabReport(labReportId, clinicId)
			).rejects.toThrow(NotFoundException);
		});
	});

	describe('findTemplatesByDiagnostic', () => {
		it('should return templates for a diagnostic', async () => {
			const clinicLabReportId = 'clinic-lab-report-id';
			const clinicId = 'clinic-id';
			const templates = [
				{ id: '1', templateName: 'Template 1' },
				{ id: '2', templateName: 'Template 2' }
			];

			const mockQueryBuilder = {
				where: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				getMany: jest.fn().mockResolvedValue(templates)
			};

			jest.spyOn(
				templateRepository,
				'createQueryBuilder'
			).mockReturnValue(mockQueryBuilder as any);

			const result = await service.findTemplatesByDiagnostic(
				clinicLabReportId,
				clinicId
			);

			expect(result).toEqual({
				status: true,
				data: templates
			});
		});

		it('should handle errors when finding templates by diagnostic', async () => {
			const clinicLabReportId = 'clinic-lab-report-id';
			const clinicId = 'clinic-id';
			const error = new Error('Database error');

			const mockQueryBuilder = {
				where: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				getMany: jest.fn().mockRejectedValue(error)
			};

			jest.spyOn(
				templateRepository,
				'createQueryBuilder'
			).mockReturnValue(mockQueryBuilder as any);

			await expect(
				service.findTemplatesByDiagnostic(clinicLabReportId, clinicId)
			).rejects.toThrow(error);
		});
	});

	describe('getPatientNotes', () => {
		it('should return diagnostic notes for a patient', async () => {
			const patientId = 'patient-id';
			const notes = [
				{
					id: '1',
					templateName: 'Template 1',
					noteData: { notes: 'Note 1' }
				}
			];

			jest.spyOn(diagnosticNoteRepository, 'find').mockResolvedValue(
				notes as any
			);

			const result = await service.getPatientNotes(patientId);
			expect(result).toEqual(notes);
		});

		it('should throw NotFoundException if no notes found for patient', async () => {
			const patientId = 'patient-id';

			jest.spyOn(diagnosticNoteRepository, 'find').mockResolvedValue(
				null as any
			);

			await expect(service.getPatientNotes(patientId)).rejects.toThrow(
				NotFoundException
			);
		});
	});

	describe('getNote', () => {
		it('should return a specific diagnostic note', async () => {
			const noteId = 'note-id';
			const notes = [
				{
					id: noteId,
					templateName: 'Template 1',
					noteData: { notes: 'Note content' }
				}
			];

			jest.spyOn(diagnosticNoteRepository, 'find').mockResolvedValue(
				notes as any
			);

			const result = await service.getNote(noteId);
			expect(result).toEqual(notes);
		});

		it('should throw NotFoundException if note not found', async () => {
			const noteId = 'note-id';

			jest.spyOn(diagnosticNoteRepository, 'find').mockResolvedValue(
				null as any
			);

			await expect(service.getNote(noteId)).rejects.toThrow(
				NotFoundException
			);
		});
	});

	describe('getNotesByLabReport', () => {
		it('should return notes for a lab report', async () => {
			const labReportId = 'lab-report-id';
			const clinicId = 'clinic-id';
			const notes = [
				{
					id: '1',
					templateName: 'Template 1',
					noteData: { notes: 'Note 1' }
				}
			];

			jest.spyOn(diagnosticNoteRepository, 'find').mockResolvedValue(
				notes as any
			);

			const result = await service.getNotesByLabReport(
				labReportId,
				clinicId
			);

			expect(diagnosticNoteRepository.find).toHaveBeenCalledWith({
				where: { labReportId, clinicId },
				relations: ['template', 'creator'],
				order: { createdAt: 'DESC' }
			});
			expect(result).toEqual(notes);
		});
	});

	describe('Private Methods Coverage', () => {
		let mockNote: DiagnosticNote;
		let updateNoteDto: UpdateDiagnosticNoteDto;

		beforeEach(() => {
			mockNote = {
				id: 'note-id',
				templateId: 'template-id',
				templateName: 'Template 1',
				noteData: { notes: 'Old notes' },
				patientId: 'patient-id',
				labReportId: 'lab-report-id',
				diagnosticNumber: 'DN001',
				fileKey: 'old-file-key',
				version: 1,
				appointmentId: 'appointment-id'
			} as DiagnosticNote;

			updateNoteDto = {
				templateId: 'new-template-id',
				templateName: 'New Template',
				noteData: { notes: 'Updated notes' },
				templateType: 'notes'
			};
		});

		describe('validateAndUpdateTemplate', () => {
			it('should return early if no templateId provided', async () => {
				const dto = { noteData: { notes: 'test' } };
				await service['validateAndUpdateTemplate'](mockNote, dto);
				expect(templateRepository.findOne).not.toHaveBeenCalled();
			});

			it('should update template when new templateId provided', async () => {
				const template = { id: 'new-template-id', isActive: true };
				jest.spyOn(templateRepository, 'findOne').mockResolvedValue(
					template as any
				);

				await service['validateAndUpdateTemplate'](
					mockNote,
					updateNoteDto
				);

				expect(mockNote.templateId).toBe('new-template-id');
				expect(mockNote.templateName).toBe('New Template');
			});

			it('should throw NotFoundException if template not found', async () => {
				jest.spyOn(templateRepository, 'findOne').mockResolvedValue(
					null
				);

				await expect(
					service['validateAndUpdateTemplate'](mockNote, updateNoteDto)
				).rejects.toThrow(NotFoundException);
			});
		});

		describe('updateNoteDataByType', () => {
			it('should update note data for notes template type', () => {
				const dto = {
					templateType: 'notes' as const,
					noteData: { notes: 'New notes content' }
				};

				service['updateNoteDataByType'](mockNote, dto);

				expect(mockNote.noteData).toEqual({
					notes: 'New notes content',
					values: {}
				});
			});

			it('should update note data for table template type', () => {
				const dto = {
					templateType: 'table' as const,
					noteData: { values: { field1: 'value1' } }
				};

				service['updateNoteDataByType'](mockNote, dto);

				expect(mockNote.noteData).toEqual({
					notes: '',
					values: { field1: 'value1' }
				});
			});
		});

		describe('deleteOldPdfFile', () => {
			it('should return early if no fileKey provided', async () => {
				await service['deleteOldPdfFile'](undefined);
				expect(s3Service.deleteFile).not.toHaveBeenCalled();
			});

			it('should handle S3 deletion errors gracefully', async () => {
				const error = new Error('S3 error');
				jest.spyOn(s3Service, 'deleteFile').mockRejectedValue(error);

				await service['deleteOldPdfFile']('file-key');

				expect(logger.warn).toHaveBeenCalledWith(
					'Failed to delete old PDF file',
					{
						fileKey: 'file-key',
						error
					}
				);
			});
		});

		describe('updateNoteMetadata', () => {
			it('should update note metadata', () => {
				service['updateNoteMetadata'](mockNote, 'user-id');

				expect(mockNote.updatedBy).toBe('user-id');
				expect(mockNote.version).toBe(2);
				expect(mockNote.updatedAt).toBeInstanceOf(Date);
			});
		});

		describe('handleUpdateError', () => {
			it('should re-throw NotFoundException', () => {
				const error = new NotFoundException('Not found');

				expect(() =>
					service['handleUpdateError'](error, 'note-id', 'user-id')
				).toThrow(NotFoundException);
			});

			it('should throw InternalServerErrorException for other errors', () => {
				const error = new Error('Some error');

				expect(() =>
					service['handleUpdateError'](error, 'note-id', 'user-id')
				).toThrow(InternalServerErrorException);
			});
		});

		describe('generateAndUploadPDF', () => {
			it('should generate and upload PDF for notes template', async () => {
				const noteData = { notes: 'Test notes' };

				const result = await service['generateAndUploadPDF'](
					noteData,
					'notes'
				);

				expect(result).toBe('diagnostic-notes/uuid-123.pdf');
				expect(s3Service.uploadPdfToS3).toHaveBeenCalled();
			});

			it('should generate and upload PDF for table template', async () => {
				const noteData = { values: { field1: 'value1' } };

				const result = await service['generateAndUploadPDF'](
					noteData,
					'table'
				);

				expect(result).toBe('diagnostic-notes/uuid-123.pdf');
			});

			it('should handle PDF generation errors', async () => {
				const error = new Error('PDF generation failed');
				const { generatePDF } = require('../utils/generatePdf');
				generatePDF.mockRejectedValue(error);

				await expect(
					service['generateAndUploadPDF']({}, 'notes')
				).rejects.toThrow(InternalServerErrorException);

				expect(logger.error).toHaveBeenCalledWith(
					'Error generating/uploading PDF',
					{ error }
				);
			});
		});

		describe('getCompleteNoteData', () => {
			it('should format complete note data for PDF generation', async () => {
				const patientDetails = {
					patientName: 'Fluffy',
					breed: 'golden_retriever',
					species: 'Dog',
					age: '2 years',
					gender: 'Male',
					clinic: {
						name: 'Pet Clinic',
						addressLine1: '123 Main St',
						addressPincode: '12345',
						city: 'Test City',
						phoneNumbers: [{ number: '************' }],
						email: '<EMAIL>',
						website: 'www.clinic.com',
						logoUrl: 'logo.png'
					},
					patientOwners: [
						{
							ownerBrand: {
								firstName: 'John',
								lastName: 'Doe',
								email: '<EMAIL>',
								globalOwner: {
									countryCode: '+1',
									phoneNumber: '************'
								}
							}
						}
					]
				};

				jest.spyOn(patientService, 'getPatientDetails').mockResolvedValue(
					patientDetails as any
				);

				const result = await service['getCompleteNoteData'](
					'Test Template',
					{ notes: 'Test notes' },
					'patient-id',
					'DN001',
					'Lab Test',
					new Date()
				);

				expect(result.petName).toBe('Fluffy');
				expect(result.petBreed).toBe('Golden Retriever');
				expect(result.clinicName).toBe('Pet Clinic');
				expect(result.templateName).toBe('Test Template');
			});
		});

		describe('getClinicAddress', () => {
			it('should format clinic address correctly', () => {
				const patientDetail = {
					clinic: {
						addressLine1: '123 Main St',
						addressPincode: '12345'
					}
				} as any;

				const result = service.getClinicAddress(patientDetail);
				expect(result).toBe('123 Main St, - 12345');
			});

			it('should handle missing address parts', () => {
				const patientDetail = {
					clinic: {
						addressLine1: '123 Main St'
					}
				} as any;

				const result = service.getClinicAddress(patientDetail);
				expect(result).toBe('123 Main St');
			});
		});
	});

	describe('updateAppointmentDetailsAndBroadcastDiagnosticNotes', () => {
		it('should handle appointment not found', async () => {
			jest.spyOn(appointmentRepository, 'findOne').mockResolvedValue(null);

			await service['updateAppointmentDetailsAndBroadcastDiagnosticNotes'](
				'appointment-id',
				'lab-report-id'
			);

			expect(logger.warn).toHaveBeenCalledWith(
				'Appointment appointment-id not found for diagnostic notes update',
				{ appointmentId: 'appointment-id' }
			);
		});

		it('should handle lab report not found in appointment details', async () => {
			const appointment = {
				id: 'appointment-id',
				appointmentDetails: {
					details: {
						objective: {
							labReports: [
								{ labReportId: 'other-lab-report-id' }
							]
						}
					}
				}
			};

			jest.spyOn(appointmentRepository, 'findOne').mockResolvedValue(
				appointment as any
			);

			await service['updateAppointmentDetailsAndBroadcastDiagnosticNotes'](
				'appointment-id',
				'lab-report-id'
			);

			expect(logger.warn).toHaveBeenCalledWith(
				'Lab report lab-report-id not found in appointment appointment-id details',
				{ appointmentId: 'appointment-id', labReportId: 'lab-report-id' }
			);
		});

		it('should create new appointment details if none exist', async () => {
			const appointment = {
				id: 'appointment-id',
				appointmentDetails: null
			};

			const diagnosticNotes = [{ id: 'note-1' }];

			jest.spyOn(appointmentRepository, 'findOne').mockResolvedValue(
				appointment as any
			);
			jest.spyOn(diagnosticNoteRepository, 'find').mockResolvedValue(
				diagnosticNotes as any
			);
			jest.spyOn(
				appointmentDetailsRepository,
				'create'
			).mockReturnValue({} as any);
			jest.spyOn(appointmentDetailsRepository, 'save').mockResolvedValue(
				{} as any
			);
			jest.spyOn(appointmentGateway, 'publishAppointmentUpdate').mockResolvedValue(
				undefined
			);

			// Mock the scenario where appointment has no existing details
			// but has lab reports that need updating
			const mockAppointmentWithDetails = {
				...appointment,
				appointmentDetails: {
					details: {
						objective: {
							labReports: [
								{ labReportId: 'lab-report-id' }
							]
						}
					}
				}
			};

			// First call returns appointment without details, second call simulates finding lab reports
			jest.spyOn(appointmentRepository, 'findOne')
				.mockResolvedValueOnce(mockAppointmentWithDetails as any);

			await service['updateAppointmentDetailsAndBroadcastDiagnosticNotes'](
				'appointment-id',
				'lab-report-id'
			);

			// The method should handle the case where appointment details exist
			expect(appointmentGateway.publishAppointmentUpdate).toHaveBeenCalled();
		});

		it('should handle errors gracefully', async () => {
			const error = new Error('Database error');
			jest.spyOn(appointmentRepository, 'findOne').mockRejectedValue(error);

			await service['updateAppointmentDetailsAndBroadcastDiagnosticNotes'](
				'appointment-id',
				'lab-report-id'
			);

			expect(logger.error).toHaveBeenCalledWith(
				'Error updating appointment details and broadcasting diagnostic notes change',
				expect.objectContaining({
					appointmentId: 'appointment-id',
					labReportId: 'lab-report-id',
					error: 'Database error'
				})
			);
		});
	});
});

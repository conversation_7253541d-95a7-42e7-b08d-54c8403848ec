import { AnalyticsDocumentType, ShareMode, RecipientType } from '../dto/analytics-document-types.enum';

export interface AnalyticsDocumentGenerationJob {
  requestId: string;
  clinicId: string;
  userId: string;
  startDate: string;
  endDate: string;
  documentTypes: AnalyticsDocumentType[];
  shareMode: ShareMode[];
  recipientType: RecipientType;
  email?: string;
  phoneNumber?: string;
  includeExcelReport: boolean;
}

export interface DocumentGenerationResult {
  success: boolean;
  s3DocumentKey?: string;
  s3ExcelKey?: string;
  errorMessage?: string;
}

export interface AnalyticsDocumentMetadata {
  documentType: AnalyticsDocumentType;
  count: number;
  totalAmount: number;
  dateRange: {
    start: Date;
    end: Date;
  };
}

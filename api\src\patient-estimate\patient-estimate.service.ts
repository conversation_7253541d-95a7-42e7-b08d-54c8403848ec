import {
	Injectable,
	InternalServerErrorException,
	NotFoundException
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, Repository } from 'typeorm';
import {
	PatientEstimate,
	SignatureStatus
} from './entities/patient-estimate.entity';
import { CreatePatientEstimateDto } from './dto/create-patient-estimate.dto';
import { UpdatePatientEstimateDto } from './dto/update-patient-estimate.dto';
import { uuidv4 } from 'uuidv7';
import { generatePDF } from '../utils/generatePdf';
import { SESMailService } from '../utils/aws/ses/send-mail-service';
import { WhatsappService } from '../utils/whatsapp-integration/whatsapp.service';
import { S3Service } from '../utils/aws/s3/s3.service';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import {
	generateTreatmentEstimateHml,
	TreatmentEstimate
} from '../utils/pdfs/treatmentEstimate';
import moment = require('moment');
import { generateUniqueCode } from '../utils/common/generate_alpha-numeric_code';
import {
	nonSignableDocumentMailTemplate,
	signableDocumentMailTemplate
} from '../utils/mail-generator/mail-template-generator';
import { DEV_SES_EMAIL } from '../utils/constants';
import {
	getClientBookingUrl,
	isProduction,
	isProductionOrUat
} from '../utils/common/get-login-url';
import { UpdateSignedDocumentDto } from '../patient-document-libraries/dto/update-patient-document-library.dto';
import {
	sendtreatmentEstimateDocument,
	sendtreatmentEstimateDocumentClinicLink,
	sendTreatmentEstimateUrl
} from '../utils/communicatoins/whatsapp-template-generator';
import { selectTemplate } from '../utils/common/template-helper.util';
@Injectable()
export class PatientEstimateService {
	constructor(
		@InjectRepository(PatientEstimate)
		private patientEstimateRepository: Repository<PatientEstimate>,
		private readonly logger: WinstonLogger,
		private readonly mailService: SESMailService,
		private readonly whatsappService: WhatsappService,
		private s3Service: S3Service
	) {}

	async sendMail(
		body: string,
		buffers: Buffer[],
		fileName: string[],
		email: string,
		subject?: string
	) {
		try {
			if (isProductionOrUat() && email) {
				this.mailService.sendMail({
					body: body,
					subject: subject ?? 'signable document attachments',
					pdfBuffers: buffers,
					pdfFileNames: fileName,
					toMailAddress: email
				});
				this.logger.log('Production Mail Log', {
					body: body,
					subject: subject ?? 'signable document attachments',
					pdfFileNames: fileName,
					toMailAddress: DEV_SES_EMAIL
				});
			} else if (!isProduction()) {
				this.mailService.sendMail({
					body: body,
					subject: subject ?? 'signable document attachments',
					pdfBuffers: buffers,
					pdfFileNames: fileName,
					toMailAddress: DEV_SES_EMAIL
				});
				this.logger.log('UAT Mail Log', {
					body: body,
					subject: subject ?? 'signable document attachments',
					pdfFileNames: fileName,
					toMailAddress: DEV_SES_EMAIL
				});
			}
		} catch (error) {
			console.log('eroor => ', error);
			this.logger.log('Send Mail Error ', {
				error
			});
		}
	}

	async create(
		createDto: CreatePatientEstimateDto
	): Promise<PatientEstimate> {
		const response = await this.patientEstimateRepository.create({
			...createDto
		});

		const patientEstimate =
			await this.patientEstimateRepository.save(response);

		if (patientEstimate.signatureRequired) {
			await this.sendSignableDocumentUrl(
				patientEstimate?.id,
				`https://${createDto?.urlPath}/estimate-signed-doc/${patientEstimate?.id}`
			);
		} else {
			await this.sendEstimateDocument(patientEstimate?.id);
		}
		const patientEstimateResponse =
			await this.patientEstimateRepository.findOne({
				where: { id: response.id },
				relations: ['doctor']
			});
		return patientEstimateResponse!;
	}

	async findAll(): Promise<PatientEstimate[]> {
		return await this.patientEstimateRepository.find();
	}

	async findOne(id: string): Promise<PatientEstimate> {
		const estimate = await this.patientEstimateRepository.findOne({
			where: { id },
			relations: ['clinic', 'clinic.brand']
		});

		if (!estimate) {
			throw new NotFoundException(
				`Patient estimate with ID ${id} not found`
			);
		}

		return estimate;
	}

	async findByPatient(
		patientId: string,
		page: number = 1,
		limit: number = 10,
		search?: string // Optional search parameter
	): Promise<{
		data: PatientEstimate[];
		total: number;
		page: number;
		pageCount: number;
	}> {
		try {
			this.logger.log('Fetching PatientEstimates', {
				patientId,
				page,
				limit,
				search
			});

			const queryBuilder = this.patientEstimateRepository
				.createQueryBuilder('patientEstimate')
				.leftJoinAndSelect('patientEstimate.doctor', 'user')
				.where('patientEstimate.patientId = :patientId', { patientId });

			// Add search functionality if search term is provided
			const updatedSearch = search?.trim();
			if (
				updatedSearch &&
				!'treatment estimate'.includes(updatedSearch.toLowerCase())
			) {
				this.logger.log('Adding search filter', { updatedSearch });

				queryBuilder.andWhere(
					new Brackets(qb => {
						qb.where('user.firstName ILIKE :updatedSearch', {
							updatedSearch: `%${updatedSearch}%`
						}).orWhere('user.lastName ILIKE :updatedSearch', {
							updatedSearch: `%${updatedSearch}%`
						});
					})
				);
			}

			// Apply pagination
			queryBuilder
				.skip((page - 1) * limit)
				// .take(limit)
				.orderBy('patientEstimate.createdAt', 'DESC');

			// Fetch data and total count
			const [data, total] = await queryBuilder.getManyAndCount();

			this.logger.log('PatientEstimates fetched successfully', {
				count: data.length,
				page,
				limit
			});

			return {
				data,
				total,
				page,
				pageCount: Math.ceil(total / limit)
			};
		} catch (error) {
			this.logger.error('Error fetching PatientEstimates', {
				error
			});
			throw new InternalServerErrorException(
				'Failed to fetch PatientEstimates'
			);
		}
	}

	async update(
		id: string,
		updateDto: UpdatePatientEstimateDto
	): Promise<PatientEstimate> {
		const estimate = await this.findOne(id);
		Object.assign(estimate, updateDto);
		return await this.patientEstimateRepository.save(estimate);
	}

	async remove(id: string): Promise<void> {
		const result = await this.patientEstimateRepository.delete(id);
		if (result.affected === 0) {
			throw new NotFoundException(
				`Patient estimate with ID ${id} not found`
			);
		}
	}

	async sendSignableDocumentUrl(estimateId: string, url: string) {
		const estimateResponse = await this.patientEstimateRepository.findOne({
			where: { id: estimateId },
			relations: [
				'patient',
				'patient.patientOwners',
				'patient.patientOwners.ownerBrand',
				'patient.patientOwners.ownerBrand.globalOwner',
				'clinic',
				'clinic.brand'
			]
		});
		if (estimateResponse) {
			estimateResponse?.patient?.patientOwners?.map(async patient => {
				const clientName: string = `${patient?.ownerBrand?.firstName} ${patient?.ownerBrand?.lastName}`;
				const brandName: string =
					estimateResponse?.clinic?.brand?.name || '';
				const clientEmail: string = patient?.ownerBrand?.email || '';
				const clientMobileNumber: string =
					patient?.ownerBrand?.globalOwner?.phoneNumber || '';
				const patientName: string =
					estimateResponse?.patient?.patientName || '';

				// Extract brand from the original URL for WhatsApp proxy URL
				const urlMatch = url.match(/https:\/\/([^\/]+)\//);
				const fullDomain = urlMatch ? urlMatch[1] : '';
				// Extract just the brand part (subdomain) from domain like "topdogpetsclinic.nidana.io"
				const brand = fullDomain.split('.')[0];

				this.logger.log('URL extraction for WhatsApp', {
					originalUrl: url,
					fullDomain,
					extractedBrand: brand
				});

				// Create WhatsApp proxy URL using centralized format
				const whatsappUrl = `${brand}&patientestimateid=${estimateId}`;

				if (clientEmail?.length) {
					const { body, subject, email } =
						signableDocumentMailTemplate({
							clientName,
							brandName,
							url,
							email: clientEmail,
							patientName
						});
					await this.sendMail(
						body,
						[],
						[],
						email,
						`Please review and sign ${patientName}’s treatment estimate - ${brandName}`
					);
					this.logger.log('email successfully send', estimateId);
				} else {
					this.logger.log('email is not present for user');
				}
				if (clientMobileNumber.length && isProductionOrUat()) {
					const { templateName, mobileNumber, valuesArray } =
						sendTreatmentEstimateUrl({
							clientName,
							brandName,
							url: whatsappUrl, // Use modified URL for WhatsApp
							mobileNumber: clientMobileNumber,
							patientName
						});
					const response =
						await this.whatsappService.sendTemplateMessage({
							templateName,
							valuesArray,
							mobileNumber
						});
					this.logger.log(
						'message on whatsapp successfully send',
						response
					);
				} else {
					this.logger.log('mobile number is not present for user');
				}
			});
		} else {
			this.logger.log('document not found');
		}
	}

	async sendEstimateDocument(estimateId: string): Promise<any> {
		const estimateResponse = await this.patientEstimateRepository.findOne({
			where: { id: estimateId },
			relations: [
				'patient',
				'patient.patientOwners',
				'patient.patientOwners.ownerBrand',
				'patient.patientOwners.ownerBrand.globalOwner',
				'clinic',
				'clinic.brand'
			]
		});
		if (estimateResponse) {
			const fileKey = `patient-estimate-library/${estimateResponse?.patientId}/${uuidv4()}`;
			const documentId = await generateUniqueCode(
				'documentId',
				this.patientEstimateRepository
			);
			const htmlObject = this.generateHtmlObject(
				estimateResponse,
				documentId
			);
			const documentHtml = generateTreatmentEstimateHml(htmlObject);

			const pdfbuffer: Buffer = await generatePDF(documentHtml);
			await this.s3Service.uploadPdfToS3(pdfbuffer, fileKey);
			const viewSignedUrl =
				await this.s3Service.getViewPreSignedUrl(fileKey);
			const updateEstimateResponse =
				await this.patientEstimateRepository.update(
					{ id: estimateId },
					{
						signatureStatus: SignatureStatus.PENDING,
						fileKey: fileKey,
						documentId: documentId
					}
				);
			estimateResponse?.patient?.patientOwners?.map(async patient => {
				const clientName: string = `${patient?.ownerBrand?.firstName} ${patient?.ownerBrand?.lastName}`;
				const brandName: string =
					estimateResponse?.clinic?.brand?.name || '';
				const clientEmail: string = patient?.ownerBrand?.email || '';
				const clientMobileNumber: string =
					patient?.ownerBrand?.globalOwner?.phoneNumber || '';
				const patientName: string =
					estimateResponse?.patient?.patientName || '';
				const clinicContactNo: string =
					estimateResponse?.clinic?.phoneNumbers[0]?.number || '';
				if (clientEmail?.length) {
					const { body, subject, email } =
						nonSignableDocumentMailTemplate({
							clientName,
							patientName,
							contactNo: clinicContactNo,
							brandName,
							email: clientEmail,
							documentName: 'document for',
							documentTitle: 'Treatment Estimate'
						});

					await this.sendMail(
						body,
						[pdfbuffer],
						[`Treatment_Estimate.pdf`],
						email,
						subject
					);
					this.logger.log('email successfully send', documentId);
				} else {
					this.logger.log('email is not present for user');
				}
				if (clientMobileNumber.length && isProductionOrUat()) {
					const templateArgs = {
						clientName,
						brandName,
						contactNo: clinicContactNo,
						mobileNumber: clientMobileNumber,
						patientName,
						documentName: `document for`,
						documentUrl: viewSignedUrl
					};
					const { mobileNumber, templateName, valuesArray } =
						selectTemplate(
							estimateResponse?.clinic,
							templateArgs,
							sendtreatmentEstimateDocument,
							sendtreatmentEstimateDocumentClinicLink
						);

					const response =
						await this.whatsappService.sendTemplateMessage({
							templateName,
							valuesArray,
							mobileNumber
						});
					this.logger.log(
						'message on whatsapp successfully send',
						documentId
					);
				} else {
					this.logger.log('mobile number is not present for user');
				}
			});
			return fileKey;
		} else {
			this.logger.log('document not found');
		}
	}

	async sendSignedDocument(
		estimateId: string,
		updateSignedDocumentDto: UpdateSignedDocumentDto
	) {
		this.logger.log('start sending document for id', {
			estimateId,
			updateSignedDocumentDto
		});
		const estimateResponse = await this.patientEstimateRepository.findOne({
			where: { id: estimateId },
			relations: [
				'patient',
				'patient.patientOwners',
				'patient.patientOwners.ownerBrand',
				'patient.patientOwners.ownerBrand.globalOwner',
				'clinic',
				'clinic.brand'
			]
		});

		if (estimateResponse) {
			try {
				const fileKey = `patient-document-library/${estimateId}/${uuidv4()}`;
				const documentId = await generateUniqueCode(
					'documentId',
					this.patientEstimateRepository
				);
				const htmlObject = this.generateHtmlObject(
					estimateResponse,
					documentId
				);
				const documentHtml = generateTreatmentEstimateHml({
					...htmlObject,
					digitalSignature: updateSignedDocumentDto?.signatureSvg,
					vetName: `${updateSignedDocumentDto?.firstName} ${updateSignedDocumentDto?.lastName}`,
					docDate: moment().format('DD MM YYYY')
				});

				const pdfbuffer: Buffer = await generatePDF(documentHtml);
				await this.s3Service.uploadPdfToS3(pdfbuffer, fileKey);
				const viewSignedUrl =
					await this.s3Service.getViewPreSignedUrl(fileKey);

				const updateDocumentResponse =
					await this.patientEstimateRepository.update(
						{ id: estimateId },
						{
							signaturedBy: `${updateSignedDocumentDto.firstName} ${updateSignedDocumentDto.lastName}`,
							signatureStatus: SignatureStatus.COMPLETED,
							fileKey,
							documentId
						}
					);
				estimateResponse?.patient?.patientOwners?.map(async patient => {
					const clientName: string = `${patient?.ownerBrand?.firstName} ${patient?.ownerBrand?.lastName}`;
					const brandName: string =
						estimateResponse?.clinic?.brand?.name || '';
					const clientEmail: string =
						patient?.ownerBrand?.email || '';
					const clientMobileNumber: string =
						patient?.ownerBrand?.globalOwner?.phoneNumber || '';
					const patientName: string =
						estimateResponse?.patient?.patientName || '';
					const clinicContactNo: string =
						estimateResponse?.clinic?.phoneNumbers[0]?.number || '';
					if (clientEmail?.length) {
						const { body, subject, email } =
							nonSignableDocumentMailTemplate({
								clientName,
								patientName,
								contactNo: clinicContactNo,
								brandName,
								email: clientEmail,
								documentName: 'document for',
								documentTitle: 'Treatment Estimate'
							});

						await this.sendMail(
							body,
							[pdfbuffer],
							[`Treatment_Estimate.pdf`],
							email,
							`Your signed copy for Treatment_Estimate is here`
						);
						this.logger.log('email successfully send', documentId);
					} else {
						this.logger.log('email is not present for user');
					}

					if (clientMobileNumber.length && isProductionOrUat()) {
						const templateArgs = {
							clientName,
							brandName,
							contactNo: clinicContactNo,
							mobileNumber: clientMobileNumber,
							patientName,
							documentName: 'document for',
							documentUrl: viewSignedUrl
						};
						const { mobileNumber, templateName, valuesArray } =
							selectTemplate(
								estimateResponse?.clinic,
								templateArgs,
								sendtreatmentEstimateDocument,
								sendtreatmentEstimateDocumentClinicLink
							);
						const response =
							await this.whatsappService.sendTemplateMessage({
								templateName,
								valuesArray,
								mobileNumber
							});
						this.logger.log(
							'message on whatsapp successfully send',
							documentId
						);
					} else {
						this.logger.log(
							'mobile number is not present for user'
						);
					}
				});

				this.logger.log('successfully update the data', {
					id: documentId,
					documentSendStatus: 'completed',
					signedBy: {
						firstName: updateSignedDocumentDto.firstName,
						lastName: updateSignedDocumentDto?.lastName
					},
					signedRecieved: 'completed',
					fileKey
				});
				return updateDocumentResponse;
			} catch (error) {
				this.logger.log(
					'🚀 ~ PatientDocumentLibrariesService ~ error:',
					error
				);
			}
		}
		return null;
	}

	generateHtmlObject(estimateResponse: PatientEstimate, documentId: string) {
		const htmlObject = {
			clinicAddress1: estimateResponse?.clinic?.addressLine1 || '',
			clinicAddress2:
				`${estimateResponse?.clinic?.addressLine2 || ''} ${estimateResponse?.clinic?.addressPincode || ''}` ||
				'',
			clinicAddress3: `${estimateResponse?.clinic?.city} ${estimateResponse?.clinic?.state} ${estimateResponse?.clinic?.country}`,
			clinicContactNo: `${estimateResponse?.clinic?.phoneNumbers[0]?.country_code}${estimateResponse?.clinic?.phoneNumbers[0]?.number}`,
			clinicEmail: estimateResponse?.clinic?.email || '',
			clinicName: estimateResponse?.clinic?.name,
			clinicWebsite: estimateResponse?.clinic?.website,
			date: moment(estimateResponse?.createdAt).format('DD MM YYYY'),
			documentId: documentId,
			estimateTotal: estimateResponse?.estimateTotal,
			ownerEmail:
				estimateResponse?.patient?.patientOwners[0]?.ownerBrand?.email,
			ownerMobile: `${estimateResponse?.patient?.patientOwners[0]?.ownerBrand?.globalOwner?.countryCode}${estimateResponse?.patient?.patientOwners[0]?.ownerBrand?.globalOwner?.phoneNumber}`,
			ownerName: `${estimateResponse?.patient?.patientOwners[0]?.ownerBrand?.firstName} ${estimateResponse?.patient?.patientOwners[0]?.ownerBrand?.lastName}`,
			lineItems: estimateResponse?.treatmentPlan,
			petName: estimateResponse?.patient?.patientName,
			petBreed: estimateResponse?.patient?.breed
		} as TreatmentEstimate;
		return htmlObject;
	}
}

#!/bin/bash

# Colors for terminal output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print section headers
print_header() {
    echo -e "\n${BLUE}===========================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}===========================================================${NC}\n"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        echo -e "${RED}Error: Docker is not running. Please start Docker and try again.${NC}"
        exit 1
    fi
}

# Function to create necessary directories
create_directories() {
    print_header "Creating necessary directories"
    
    mkdir -p sonar-configs
    
    # Copy property files if they don't exist in the target location
    if [ ! -f api/sonar-project.properties ]; then
        echo -e "${YELLOW}Copying sonar-project.properties to api directory${NC}"
        cp sonar-configs/sonar-project-api.properties api/sonar-project.properties
    fi
    
    if [ ! -f ui/sonar-project.properties ]; then
        echo -e "${YELLOW}Copying sonar-project.properties to ui directory${NC}"
        cp sonar-configs/sonar-project-ui.properties ui/sonar-project.properties
    fi
    
    if [ ! -f patientportal/sonar-project.properties ]; then
        echo -e "${YELLOW}Copying sonar-project.properties to patientportal directory${NC}"
        cp sonar-configs/sonar-project-patientportal.properties patientportal/sonar-project.properties
    fi
    
    echo -e "${GREEN}Directories and configuration files are ready.${NC}"
}

# Function to start SonarQube server
start_sonarqube() {
    local version=${1:-"standard"}
    local compose_file="docker-compose.sonarqube.yml"

    if [ "$version" = "lite" ]; then
        compose_file="docker-compose.sonarqube-lite.yml"
        print_header "Starting SonarQube Server (Lite Version)"
    else
        print_header "Starting SonarQube Server (Standard Version)"
    fi

    echo -e "${YELLOW}Starting SonarQube server and database...${NC}"

    # Try to start with the specified compose file
    if ! docker-compose -f $compose_file up -d sonarqube sonarqube-db; then
        echo -e "${RED}Failed to start SonarQube with $compose_file${NC}"

        if [ "$version" != "lite" ]; then
            echo -e "${YELLOW}Trying with lite version instead...${NC}"
            start_sonarqube "lite"
            return
        else
            echo -e "${RED}Failed to start SonarQube. Please check Docker logs for more details.${NC}"
            echo -e "${YELLOW}Try running: docker logs sonarqube-server${NC}"
            exit 1
        fi
    fi

    echo -e "${YELLOW}Waiting for SonarQube to be ready (this may take a minute or two)...${NC}"
    local max_attempts=30
    local attempt=0

    while [ $attempt -lt $max_attempts ]; do
        if curl -s http://localhost:9000/api/system/status | grep -q '"status":"UP"'; then
            break
        fi
        echo -e "${YELLOW}SonarQube is starting up. Waiting... (attempt $((attempt + 1))/$max_attempts)${NC}"
        sleep 10
        attempt=$((attempt + 1))
    done

    if [ $attempt -eq $max_attempts ]; then
        echo -e "${RED}SonarQube failed to start within expected time. Check logs with:${NC}"
        echo -e "${YELLOW}docker logs sonarqube-server${NC}"
        exit 1
    fi

    echo -e "${GREEN}SonarQube server is up and running at http://localhost:9000${NC}"
    echo -e "${YELLOW}Default credentials: admin/admin${NC}"
    echo -e "${YELLOW}You will be prompted to change the password on first login${NC}"
}

# Function to run scans
run_scans() {
    local component=$1
    local version=${2:-"standard"}
    local compose_file="docker-compose.sonarqube.yml"

    if [ "$version" = "lite" ]; then
        compose_file="docker-compose.sonarqube-lite.yml"
    fi

    # Check if SonarQube is running
    if ! docker ps | grep -q sonarqube-server; then
        echo -e "${RED}SonarQube server is not running. Please start it first with:${NC}"
        echo -e "${YELLOW}./sonar-scan.sh start${NC}"
        exit 1
    fi

    case $component in
        "api")
            print_header "Running SonarQube scan for API"
            docker-compose -f $compose_file run --rm sonar-scanner-api
            ;;
        "ui")
            print_header "Running SonarQube scan for UI"
            docker-compose -f $compose_file run --rm sonar-scanner-ui
            ;;
        "patientportal")
            print_header "Running SonarQube scan for Patient Portal"
            docker-compose -f $compose_file run --rm sonar-scanner-patientportal
            ;;
        "all")
            print_header "Running SonarQube scan for all components"
            docker-compose -f $compose_file run --rm sonar-scanner-api
            docker-compose -f $compose_file run --rm sonar-scanner-ui
            docker-compose -f $compose_file run --rm sonar-scanner-patientportal
            ;;
        *)
            echo -e "${RED}Invalid component: $component${NC}"
            echo -e "${YELLOW}Valid options: api, ui, patientportal, all${NC}"
            exit 1
            ;;
    esac
}

# Function to stop SonarQube server
stop_sonarqube() {
    local version=${1:-"standard"}
    local compose_file="docker-compose.sonarqube.yml"

    if [ "$version" = "lite" ]; then
        compose_file="docker-compose.sonarqube-lite.yml"
        print_header "Stopping SonarQube Server (Lite Version)"
    else
        print_header "Stopping SonarQube Server (Standard Version)"
    fi

    echo -e "${YELLOW}Stopping SonarQube server and database...${NC}"

    # Try to stop with the specified compose file
    if ! docker-compose -f $compose_file down; then
        echo -e "${RED}Failed to stop SonarQube with $compose_file${NC}"

        # If standard version failed, try lite version
        if [ "$version" != "lite" ]; then
            echo -e "${YELLOW}Trying to stop lite version instead...${NC}"
            stop_sonarqube "lite"
        else
            echo -e "${RED}Failed to stop SonarQube. You may need to stop containers manually.${NC}"
            echo -e "${YELLOW}Try running: docker stop sonarqube-server sonarqube-postgres${NC}"
        fi
    else
        echo -e "${GREEN}SonarQube server has been stopped.${NC}"
    fi
}

# Main function
main() {
    check_docker
    
    case $1 in
        "start")
            create_directories
            start_sonarqube $2
            ;;
        "start-lite")
            create_directories
            start_sonarqube "lite"
            ;;
        "scan")
            if [ -z "$2" ]; then
                echo -e "${RED}Error: Please specify a component to scan (api, ui, patientportal, all)${NC}"
                exit 1
            fi
            # Determine which version is running
            local version="standard"
            if docker ps | grep -q sonarqube-server-lite; then
                version="lite"
            fi
            run_scans $2 $version
            ;;
        "stop")
            # Try to stop both versions
            stop_sonarqube "standard"
            stop_sonarqube "lite"
            ;;
        "restart")
            stop_sonarqube $2
            start_sonarqube $2
            ;;
        "logs")
            echo -e "${YELLOW}Showing SonarQube logs...${NC}"
            if docker ps | grep -q sonarqube-server-lite; then
                docker logs -f sonarqube-server-lite
            else
                docker logs -f sonarqube-server
            fi
            ;;
        *)
            echo -e "${YELLOW}Usage: $0 [start|start-lite|scan|stop|restart|logs] [component|version]${NC}"
            echo -e "${YELLOW}  start [lite]: Start SonarQube server (standard or lite version)${NC}"
            echo -e "${YELLOW}  start-lite: Start SonarQube server (lite version for macOS compatibility)${NC}"
            echo -e "${YELLOW}  scan [component]: Run SonarQube scan for the specified component (api, ui, patientportal, all)${NC}"
            echo -e "${YELLOW}  stop: Stop SonarQube server${NC}"
            echo -e "${YELLOW}  restart [lite]: Restart SonarQube server${NC}"
            echo -e "${YELLOW}  logs: Show SonarQube server logs${NC}"
            exit 1
            ;;
    esac
}

# Execute main function with all arguments
main "$@"
